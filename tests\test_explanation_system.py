# AI Coding Agent - Explanation System Tests
"""
Tests for the ExplanationSystem module
"""

import pytest
from unittest.mock import Mock

from src.explanation_system import (
    ExplanationSystem, ExplanationContext, Explanation,
    ExplanationType, CommunicationStyle
)
from src.requirements_parser import UserProfile, TechnicalLevel, ProjectRequirement, ProjectType


class TestExplanationSystem:
    """Test cases for ExplanationSystem"""
    
    def setup_method(self):
        """Set up test fixtures"""
        # Mock LLM manager to avoid actual LLM calls in tests
        self.mock_llm_manager = Mock()
        self.mock_conversation_manager = Mock()
        
        self.system = ExplanationSystem(
            llm_manager=self.mock_llm_manager,
            conversation_manager=self.mock_conversation_manager
        )
    
    def test_initialization(self):
        """Test ExplanationSystem initialization"""
        assert self.system is not None
        assert self.system.llm_manager is not None
        assert self.system.conversation_manager is not None
        assert isinstance(self.system.style_templates, dict)
        assert isinstance(self.system.level_adaptations, dict)
        assert isinstance(self.system.concept_complexity, dict)
    
    def test_assess_user_understanding_novice(self):
        """Test assessment of novice understanding level"""
        context = ExplanationContext(
            topic="API",
            user_question="What is an API?",
            technical_level=TechnicalLevel.BEGINNER,
            communication_style=CommunicationStyle.FRIENDLY,
            previous_interactions=[]
        )
        
        understanding = self.system._assess_user_understanding(context)
        assert understanding == "novice"
    
    def test_assess_user_understanding_advanced(self):
        """Test assessment of advanced understanding level"""
        context = ExplanationContext(
            topic="API",
            user_question="How should I architect the API for scalability?",
            technical_level=TechnicalLevel.EXPERT,
            communication_style=CommunicationStyle.TECHNICAL,
            previous_interactions=[]
        )
        
        understanding = self.system._assess_user_understanding(context)
        assert understanding == "advanced"
    
    def test_determine_explanation_type_concept(self):
        """Test determination of concept explanation type"""
        context = ExplanationContext(
            topic="database",
            user_question="What is a database?",
            technical_level=TechnicalLevel.BEGINNER,
            communication_style=CommunicationStyle.FRIENDLY,
            previous_interactions=[]
        )
        
        explanation_type = self.system._determine_explanation_type("database", "What is a database?", context)
        assert explanation_type == ExplanationType.CONCEPT_EXPLANATION
    
    def test_determine_explanation_type_step_by_step(self):
        """Test determination of step-by-step explanation type"""
        context = ExplanationContext(
            topic="deployment",
            user_question="How to deploy my web application?",
            technical_level=TechnicalLevel.INTERMEDIATE,
            communication_style=CommunicationStyle.PROFESSIONAL,
            previous_interactions=[]
        )
        
        explanation_type = self.system._determine_explanation_type("deployment", "How to deploy my web application?", context)
        assert explanation_type == ExplanationType.STEP_BY_STEP_GUIDE
    
    def test_determine_explanation_type_comparison(self):
        """Test determination of comparison explanation type"""
        context = ExplanationContext(
            topic="frameworks",
            user_question="What's the difference between React and Vue?",
            technical_level=TechnicalLevel.INTERMEDIATE,
            communication_style=CommunicationStyle.TECHNICAL,
            previous_interactions=[]
        )
        
        explanation_type = self.system._determine_explanation_type("frameworks", "What's the difference between React and Vue?", context)
        assert explanation_type == ExplanationType.COMPARISON
    
    def test_simplify_technical_terms(self):
        """Test simplification of technical terms for beginners"""
        content = "You need to create an API that connects to a database for authentication."
        simplified = self.system._simplify_technical_terms(content)
        
        assert "API (a way for different programs to talk to each other)" in simplified
        assert "database (a place where information is stored)" in simplified
        assert "authentication (verifying who someone is, like logging in)" in simplified
    
    def test_add_beginner_context(self):
        """Test addition of beginner-friendly context"""
        content = "APIs are important for web development."
        enhanced = self.system._add_beginner_context(content)
        
        assert "Great question!" in enhanced or "Let me explain" in enhanced
        assert "don't worry" in enhanced.lower()
        assert "practice" in enhanced.lower()
    
    def test_apply_friendly_communication_style(self):
        """Test application of friendly communication style"""
        content = "APIs allow different applications to communicate."
        styled = self.system._apply_communication_style(content, CommunicationStyle.FRIENDLY)
        
        assert "happy to help" in styled.lower() or "great question" in styled.lower()
    
    def test_apply_professional_communication_style(self):
        """Test application of professional communication style"""
        content = "You'll need to implement authentication. Don't forget about security."
        styled = self.system._apply_communication_style(content, CommunicationStyle.PROFESSIONAL)
        
        assert "you will" in styled.lower()
        assert "do not" in styled.lower()
    
    def test_apply_encouraging_communication_style(self):
        """Test application of encouraging communication style"""
        content = "Authentication can be complex to implement."
        styled = self.system._apply_communication_style(content, CommunicationStyle.ENCOURAGING)
        
        assert "you're" in styled.lower() or "you've got this" in styled.lower()
    
    def test_apply_concise_communication_style(self):
        """Test application of concise communication style"""
        content = "In order to implement authentication, it is important to note that you need secure passwords."
        styled = self.system._apply_communication_style(content, CommunicationStyle.CONCISE)
        
        assert "In order to" not in styled
        assert "To implement" in styled or "implement" in styled
    
    def test_generate_examples_with_project_context(self):
        """Test example generation with project context"""
        project = ProjectRequirement(project_type=ProjectType.WEB_APP)
        context = ExplanationContext(
            topic="authentication",
            user_question="How does authentication work?",
            technical_level=TechnicalLevel.INTERMEDIATE,
            communication_style=CommunicationStyle.FRIENDLY,
            previous_interactions=[],
            project_context=project
        )
        
        examples = self.system._generate_examples("authentication", context)
        
        assert len(examples) > 0
        # Should include project-specific example
        assert any("web_app" in example.lower() for example in examples)
    
    def test_generate_examples_beginner_level(self):
        """Test example generation for beginner level"""
        context = ExplanationContext(
            topic="database",
            user_question="What is a database?",
            technical_level=TechnicalLevel.BEGINNER,
            communication_style=CommunicationStyle.FRIENDLY,
            previous_interactions=[]
        )
        
        examples = self.system._generate_examples("database", context)
        
        assert len(examples) > 0
        # Should include simple analogies
        assert any("computer" in example.lower() or "recipe" in example.lower() for example in examples)
    
    def test_generate_follow_up_suggestions(self):
        """Test generation of follow-up suggestions"""
        context = ExplanationContext(
            topic="API",
            user_question="What is an API?",
            technical_level=TechnicalLevel.INTERMEDIATE,
            communication_style=CommunicationStyle.FRIENDLY,
            previous_interactions=[]
        )
        
        suggestions = self.system._generate_follow_up_suggestions("API", context)
        
        assert len(suggestions) > 0
        assert len(suggestions) <= 3
        # Should include relevant follow-up questions
        assert any("project" in suggestion.lower() for suggestion in suggestions)
    
    def test_identify_related_concepts(self):
        """Test identification of related concepts"""
        related = self.system._identify_related_concepts("api", TechnicalLevel.INTERMEDIATE)
        
        assert len(related) > 0
        assert "REST" in related or "HTTP" in related or "JSON" in related
    
    def test_identify_related_concepts_beginner_filter(self):
        """Test that related concepts are filtered for beginners"""
        related_beginner = self.system._identify_related_concepts("api", TechnicalLevel.BEGINNER)
        related_expert = self.system._identify_related_concepts("api", TechnicalLevel.EXPERT)
        
        # Beginner should get fewer concepts
        assert len(related_beginner) <= len(related_expert)
    
    def test_calculate_explanation_confidence(self):
        """Test calculation of explanation confidence score"""
        context = ExplanationContext(
            topic="API",
            user_question="What is an API?",
            technical_level=TechnicalLevel.INTERMEDIATE,
            communication_style=CommunicationStyle.FRIENDLY,
            previous_interactions=[],
            project_context=ProjectRequirement(project_type=ProjectType.WEB_APP)
        )
        
        # High-quality content
        good_content = "An API is a way for applications to communicate. For example, when you use a weather app, it uses an API to get weather data. In your web_app project, this would mean creating endpoints that other applications can call."
        
        confidence = self.system._calculate_explanation_confidence(good_content, context)
        assert confidence > 0.7  # Should be high confidence
        
        # Low-quality content
        poor_content = "API."
        confidence = self.system._calculate_explanation_confidence(poor_content, context)
        assert confidence < 0.7  # Should be lower confidence
    
    def test_generate_fallback_explanation(self):
        """Test generation of fallback explanation"""
        context = ExplanationContext(
            topic="machine learning",
            user_question="How does machine learning work?",
            technical_level=TechnicalLevel.BEGINNER,
            communication_style=CommunicationStyle.FRIENDLY,
            previous_interactions=[]
        )
        
        fallback = self.system._generate_fallback_explanation("machine learning", "How does machine learning work?", context)
        
        assert isinstance(fallback, Explanation)
        assert "machine learning" in fallback.content.lower()
        assert fallback.confidence_score < 0.5  # Should indicate low confidence
        assert fallback.metadata.get("fallback") is True
    
    def test_generate_simple_explanation(self):
        """Test generation of simple explanation"""
        explanation = self.system._generate_simple_explanation("API", TechnicalLevel.BEGINNER)
        
        assert "API" in explanation
        assert len(explanation) > 10  # Should be substantial
    
    def test_explain_integration_with_mock(self):
        """Test full explanation generation with mocked LLM"""
        # Mock LLM response
        mock_response = "An API (Application Programming Interface) is a set of protocols and tools that allows different software applications to communicate with each other. It defines how different components should interact."
        
        self.mock_llm_manager.generate_response_with_context.return_value = mock_response
        
        context = ExplanationContext(
            topic="API",
            user_question="What is an API?",
            technical_level=TechnicalLevel.INTERMEDIATE,
            communication_style=CommunicationStyle.FRIENDLY,
            previous_interactions=[]
        )
        
        explanation = self.system.explain("API", "What is an API?", context)
        
        assert isinstance(explanation, Explanation)
        assert explanation.explanation_type == ExplanationType.CONCEPT_EXPLANATION
        assert explanation.technical_level == TechnicalLevel.INTERMEDIATE
        assert explanation.communication_style == CommunicationStyle.FRIENDLY
        assert len(explanation.examples) > 0
        assert len(explanation.follow_up_suggestions) > 0
        assert explanation.confidence_score > 0.0
    
    def test_error_handling_in_explanation(self):
        """Test error handling when LLM fails"""
        # Mock LLM to raise an exception
        self.mock_llm_manager.generate_response_with_context.side_effect = Exception("LLM Error")
        
        context = ExplanationContext(
            topic="database",
            user_question="What is a database?",
            technical_level=TechnicalLevel.BEGINNER,
            communication_style=CommunicationStyle.FRIENDLY,
            previous_interactions=[]
        )
        
        # Should not raise exception, should return fallback explanation
        explanation = self.system.explain("database", "What is a database?", context)
        
        assert isinstance(explanation, Explanation)
        assert explanation.metadata.get("fallback") is True


class TestExplanationContext:
    """Test cases for ExplanationContext data class"""
    
    def test_explanation_context_creation(self):
        """Test ExplanationContext creation"""
        context = ExplanationContext(
            topic="API",
            user_question="What is an API?",
            technical_level=TechnicalLevel.INTERMEDIATE,
            communication_style=CommunicationStyle.FRIENDLY,
            previous_interactions=["Previous question about databases"]
        )
        
        assert context.topic == "API"
        assert context.user_question == "What is an API?"
        assert context.technical_level == TechnicalLevel.INTERMEDIATE
        assert context.communication_style == CommunicationStyle.FRIENDLY
        assert len(context.previous_interactions) == 1


class TestExplanation:
    """Test cases for Explanation data class"""
    
    def test_explanation_creation(self):
        """Test Explanation creation"""
        explanation = Explanation(
            id="test_exp",
            content="Test explanation content",
            explanation_type=ExplanationType.CONCEPT_EXPLANATION,
            technical_level=TechnicalLevel.INTERMEDIATE,
            communication_style=CommunicationStyle.FRIENDLY,
            examples=["Example 1", "Example 2"],
            follow_up_suggestions=["Follow up 1"],
            related_concepts=["Concept 1", "Concept 2"],
            confidence_score=0.8,
            metadata={"test": True}
        )
        
        assert explanation.id == "test_exp"
        assert explanation.content == "Test explanation content"
        assert explanation.explanation_type == ExplanationType.CONCEPT_EXPLANATION
        assert explanation.confidence_score == 0.8
        assert len(explanation.examples) == 2
        assert len(explanation.related_concepts) == 2
