# AI Coding Agent - Phase 3 Comprehensive Test Suite
"""
Comprehensive test suite for all Phase 3 components:
- Error Detection & Resolution Engine
- Enhanced Code Validation Engine
- Phase 0 Integration & Testing
"""

import unittest
import tempfile
import shutil
import time
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

# Import all Phase 3 components
from error_monitor import ErrorMonitor, ErrorEvent
from root_cause_analyzer import RootCauseAnalyzer, RootCause
from auto_fix_generator import AutoFixGenerator, FixSuggestion
from error_explainer import ErrorExplainer, ErrorExplanation
from error_pattern_learner import ErrorPatternLearner, LearningPattern
from validators import CodeValidator
from prototype_orchestrator import PrototypeOrchestrator

class TestPhase3ErrorDetectionEngine(unittest.TestCase):
    """Test Error Detection & Resolution Engine components"""

    def setUp(self):
        """Set up test environment"""
        self.test_dir = Path(tempfile.mkdtemp())
        self.error_monitor = ErrorMonitor(str(self.test_dir))
        self.root_cause_analyzer = RootCauseAnalyzer()
        self.auto_fix_generator = AutoFixGenerator()
        self.error_explainer = ErrorExplainer()
        self.pattern_learner = ErrorPatternLearner()

    def tearDown(self):
        """Clean up test environment"""
        self.error_monitor.stop_monitoring()
        if self.test_dir.exists():
            shutil.rmtree(self.test_dir)

    def test_error_monitor_functionality(self):
        """Test ErrorMonitor core functionality"""
        print("\n🔍 Testing ErrorMonitor...")

        # Test Python error detection
        python_code_with_error = """
def hello_world()
    print("Hello, World!")
    return "success"
"""

        errors = self.error_monitor.check_code_for_errors(python_code_with_error, 'python')
        self.assertGreater(len(errors), 0, "Should detect syntax error")

        # Test error statistics
        stats = self.error_monitor.get_error_statistics()
        self.assertIsInstance(stats, dict, "Should return statistics dictionary")

        print(f"   ✓ Detected {len(errors)} errors in test code")
        print(f"   ✓ Error statistics generated")

    def test_root_cause_analyzer(self):
        """Test RootCauseAnalyzer functionality"""
        print("\n🔬 Testing RootCauseAnalyzer...")

        # Create a test error event
        error_event = ErrorEvent(
            id="test-error-1",
            timestamp=datetime.now(),
            error_type="SyntaxError",
            severity="high",
            source="syntax",
            message="invalid syntax",
            details={"line": 1},
            file_path="test.py",
            line_number=1
        )

        # Analyze the error
        root_cause = self.root_cause_analyzer.analyze_error(error_event)

        self.assertIsInstance(root_cause, RootCause, "Should return RootCause object")
        self.assertGreater(root_cause.confidence, 0.0, "Should have confidence score")
        self.assertIsNotNone(root_cause.primary_cause, "Should identify primary cause")

        print(f"   ✓ Root cause analysis completed")
        print(f"   ✓ Primary cause: {root_cause.primary_cause}")
        print(f"   ✓ Confidence: {root_cause.confidence:.2f}")

    def test_auto_fix_generator(self):
        """Test AutoFixGenerator functionality"""
        print("\n🔧 Testing AutoFixGenerator...")

        # Create test error and root cause
        error_event = ErrorEvent(
            id="test-error-2",
            timestamp=datetime.now(),
            error_type="SyntaxError",
            severity="high",
            source="syntax",
            message="invalid syntax",
            details={},
            file_path="test.py"
        )

        root_cause = RootCause(
            error_id="test-error-2",
            primary_cause="Missing colon after function definition",
            contributing_factors=[],
            confidence=0.9,
            category="syntax",
            description="Syntax error analysis",
            evidence={},
            suggested_solutions=["Add colon after function definition"],
            related_errors=[]
        )

        # Generate fixes
        fixes = self.auto_fix_generator.generate_fix(error_event, root_cause)

        self.assertIsInstance(fixes, list, "Should return list of fixes")
        if fixes:
            self.assertIsInstance(fixes[0], FixSuggestion, "Should return FixSuggestion objects")
            print(f"   ✓ Generated {len(fixes)} fix suggestions")
        else:
            print(f"   ✓ No automatic fixes available for this error type")

    def test_error_explainer(self):
        """Test ErrorExplainer functionality"""
        print("\n💬 Testing ErrorExplainer...")

        # Create test error
        error_event = ErrorEvent(
            id="test-error-3",
            timestamp=datetime.now(),
            error_type="NameError",
            severity="medium",
            source="runtime",
            message="name 'undefined_var' is not defined",
            details={},
            file_path="test.py"
        )

        # Generate explanation
        explanation = self.error_explainer.explain_error(error_event, user_level='beginner')

        self.assertIsInstance(explanation, ErrorExplanation, "Should return ErrorExplanation")
        self.assertIsNotNone(explanation.simple_explanation, "Should have simple explanation")
        self.assertGreater(len(explanation.how_to_fix), 0, "Should provide fix instructions")

        print(f"   ✓ Generated user-friendly explanation")
        print(f"   ✓ Simple explanation: {explanation.simple_explanation[:50]}...")
        print(f"   ✓ Fix suggestions: {len(explanation.how_to_fix)}")

    def test_error_pattern_learner(self):
        """Test ErrorPatternLearner functionality"""
        print("\n🧠 Testing ErrorPatternLearner...")

        # Create test error and root cause
        error_event = ErrorEvent(
            id="test-error-4",
            timestamp=datetime.now(),
            error_type="ImportError",
            severity="medium",
            source="import",
            message="No module named 'requests'",
            details={},
            file_path="test.py"
        )

        root_cause = RootCause(
            error_id="test-error-4",
            primary_cause="Missing dependency",
            contributing_factors=["Package not installed"],
            confidence=0.8,
            category="dependency",
            description="Import error analysis",
            evidence={},
            suggested_solutions=["Install requests package"],
            related_errors=[]
        )

        # Learn from the error
        self.pattern_learner.learn_from_error(error_event, root_cause, True, True)

        # Get prevention suggestions
        suggestions = self.pattern_learner.get_prevention_suggestions()

        self.assertIsInstance(suggestions, list, "Should return prevention suggestions")
        print(f"   ✓ Learning from error completed")
        print(f"   ✓ Generated {len(suggestions)} prevention suggestions")

class TestPhase3CodeValidation(unittest.TestCase):
    """Test Enhanced Code Validation Engine"""

    def setUp(self):
        """Set up test environment"""
        self.validator = CodeValidator()

    def test_advanced_python_validation(self):
        """Test advanced Python validation capabilities"""
        print("\n🐍 Testing Advanced Python Validation...")

        # Test code with various issues
        python_code = """
import os
import subprocess

def process_data(data):
    # Security issue: using eval
    result = eval(data)

    # Performance issue: inefficient loop
    items = []
    for i in range(1000):
        items = items + [i]  # Inefficient concatenation

    # Style issue: long line
    very_long_variable_name_that_exceeds_recommended_line_length = "This is a very long line that should trigger a style warning"

    return result

# Missing main guard
process_data("1+1")
"""

        is_valid, issues, analysis = self.validator.validate_python_advanced(python_code)

        self.assertIsInstance(analysis, dict, "Should return analysis dictionary")
        self.assertIn('security_issues', analysis, "Should check for security issues")
        self.assertIn('performance_warnings', analysis, "Should check for performance issues")
        self.assertIn('style_violations', analysis, "Should check for style violations")

        print(f"   ✓ Advanced validation completed")
        print(f"   ✓ Found {len(issues)} total issues")
        print(f"   ✓ Security issues: {len(analysis['security_issues'])}")
        print(f"   ✓ Performance warnings: {len(analysis['performance_warnings'])}")
        print(f"   ✓ Style violations: {len(analysis['style_violations'])}")

    def test_advanced_javascript_validation(self):
        """Test advanced JavaScript validation capabilities"""
        print("\n🟨 Testing Advanced JavaScript Validation...")

        # Test JavaScript code with issues
        js_code = """
var username = "admin";
var password = "secret123";  // Security: hardcoded password

function processData(data) {
    // Security issue: potential XSS
    document.innerHTML = "<div>" + data + "</div>";

    // Performance issue: accessing length in loop
    for (var i = 0; i < array.length; i++) {
        console.log(array[i]);
    }

    // Style issue: using var instead of let/const
    var result = data;

    // Style issue: using == instead of ===
    if (result == "success") {
        return true;
    }

    return false
}  // Missing semicolon
"""

        is_valid, issues, analysis = self.validator.validate_javascript_advanced(js_code)

        self.assertIsInstance(analysis, dict, "Should return analysis dictionary")
        self.assertIn('security_issues', analysis, "Should check for security issues")

        print(f"   ✓ Advanced JavaScript validation completed")
        print(f"   ✓ Found {len(issues)} total issues")
        print(f"   ✓ Security issues: {len(analysis['security_issues'])}")

    def test_advanced_html_validation(self):
        """Test advanced HTML validation capabilities"""
        print("\n🌐 Testing Advanced HTML Validation...")

        # Test HTML with accessibility and SEO issues
        html_code = """
<!DOCTYPE html>
<html>
<head>
    <title></title>
    <style>
        body { color: red; }
    </style>
</head>
<body>
    <h2>Welcome</h2>
    <img src="logo.png">
    <form>
        <input type="text" placeholder="Name">
        <input type="submit" value="Submit">
    </form>
    <script>
        console.log("Inline script");
    </script>
</body>
</html>
"""

        is_valid, issues, analysis = self.validator.validate_html_advanced(html_code)

        self.assertIsInstance(analysis, dict, "Should return analysis dictionary")
        self.assertIn('accessibility_issues', analysis, "Should check accessibility")
        self.assertIn('seo_warnings', analysis, "Should check SEO")
        self.assertIn('performance_warnings', analysis, "Should check performance")

        print(f"   ✓ Advanced HTML validation completed")
        print(f"   ✓ Found {len(issues)} total issues")
        print(f"   ✓ Accessibility issues: {len(analysis['accessibility_issues'])}")
        print(f"   ✓ SEO warnings: {len(analysis['seo_warnings'])}")
        print(f"   ✓ Performance warnings: {len(analysis['performance_warnings'])}")

class TestPhase3Integration(unittest.TestCase):
    """Test Phase 0 Integration & Testing"""

    def setUp(self):
        """Set up test environment"""
        self.test_dir = Path(tempfile.mkdtemp())
        self.orchestrator = PrototypeOrchestrator(str(self.test_dir))

    def tearDown(self):
        """Clean up test environment"""
        if hasattr(self.orchestrator, 'error_monitor'):
            self.orchestrator.error_monitor.stop_monitoring()
        if self.test_dir.exists():
            shutil.rmtree(self.test_dir)

    def test_integration_workflow(self):
        """Test integration of all four core capabilities"""
        print("\n🔗 Testing Integration Workflow...")

        # Simple test request
        test_request = "Create a simple Python script that prints hello world"

        # Test the complete workflow
        result = self.orchestrator.create_project_from_natural_language(test_request)

        # Validate integration
        self.assertIsNotNone(result, "Should return result")
        self.assertIsNotNone(result.project_path, "Should create project")
        self.assertGreater(len(result.generated_files), 0, "Should generate files")

        print(f"   ✓ Integration workflow completed")
        print(f"   ✓ Project created at: {result.project_path}")
        print(f"   ✓ Files generated: {len(result.generated_files)}")

    def test_zero_coding_knowledge_requirement(self):
        """Test that users can create applications without coding knowledge"""
        print("\n👤 Testing Zero Coding Knowledge Requirement...")

        # Natural language request from a non-programmer
        user_request = """
        I want to make a website where people can write down their thoughts.
        It should be simple and easy to use. People should be able to:
        - Write a new thought
        - See all their thoughts
        - Delete thoughts they don't want anymore

        I don't know how to code, so please make it work automatically.
        """

        user_context = {
            'skill_level': 'beginner',
            'coding_experience': 'none',
            'preferred_interface': 'web'
        }

        # Test the workflow
        result = self.orchestrator.create_project_from_natural_language(user_request, user_context)

        # Validate zero coding knowledge requirements
        self.assertIsNotNone(result.explanation, "Should provide clear explanation")
        self.assertGreater(len(result.next_steps), 0, "Should provide next steps")

        # Check explanation is beginner-friendly
        explanation_lower = result.explanation.lower()
        beginner_terms = ['run', 'open', 'browser', 'click', 'start']
        has_beginner_terms = any(term in explanation_lower for term in beginner_terms)
        self.assertTrue(has_beginner_terms, "Explanation should be beginner-friendly")

        print(f"   ✓ Zero coding knowledge requirement validated")
        print(f"   ✓ Beginner-friendly explanation provided")
        print(f"   ✓ Clear next steps: {len(result.next_steps)}")

    def test_natural_conversation_interface(self):
        """Test natural conversation interface works effectively"""
        print("\n💬 Testing Natural Conversation Interface...")

        # Test various natural language styles
        test_requests = [
            "I need a simple calculator app",
            "Can you help me create a todo list?",
            "Make me a website for my small business",
            "I want to build an API for my mobile app"
        ]

        successful_requests = 0

        for request in test_requests:
            try:
                result = self.orchestrator.create_project_from_natural_language(request)
                if result and result.project_path:
                    successful_requests += 1
                    print(f"   ✓ Successfully processed: '{request[:30]}...'")
            except Exception as e:
                print(f"   ⚠️  Failed to process: '{request[:30]}...' - {str(e)}")

        # Should handle at least 75% of natural language requests
        success_rate = successful_requests / len(test_requests)
        self.assertGreaterEqual(success_rate, 0.75, "Should handle most natural language requests")

        print(f"   ✓ Natural conversation interface validated")
        print(f"   ✓ Success rate: {success_rate:.1%}")

def run_comprehensive_phase3_tests():
    """Run all Phase 3 tests with detailed reporting"""
    print("\n" + "="*100)
    print("🚀 RUNNING COMPREHENSIVE PHASE 3 TEST SUITE")
    print("="*100)

    # Create test suite
    test_suite = unittest.TestSuite()

    # Add all test classes
    test_classes = [
        TestPhase3ErrorDetectionEngine,
        TestPhase3CodeValidation,
        TestPhase3Integration
    ]

    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)

    # Run tests with detailed output
    runner = unittest.TextTestRunner(verbosity=2, stream=sys.stdout)
    start_time = time.time()
    result = runner.run(test_suite)
    end_time = time.time()

    # Print summary
    print("\n" + "="*100)
    print("📊 PHASE 3 TEST SUITE SUMMARY")
    print("="*100)
    print(f"⏱️  Total execution time: {end_time - start_time:.2f} seconds")
    print(f"✅ Tests run: {result.testsRun}")
    print(f"❌ Failures: {len(result.failures)}")
    print(f"⚠️  Errors: {len(result.errors)}")
    print(f"🎯 Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")

    if result.failures:
        print(f"\n❌ FAILURES:")
        for test, traceback in result.failures:
            print(f"   - {test}: {traceback.split('AssertionError:')[-1].strip()}")

    if result.errors:
        print(f"\n⚠️  ERRORS:")
        for test, traceback in result.errors:
            print(f"   - {test}: {traceback.split('Exception:')[-1].strip()}")

    # Overall assessment
    if len(result.failures) == 0 and len(result.errors) == 0:
        print(f"\n🎉 ALL PHASE 3 TESTS PASSED!")
        print(f"✅ Error Detection & Resolution Engine: WORKING")
        print(f"✅ Enhanced Code Validation Engine: WORKING")
        print(f"✅ Phase 0 Integration: WORKING")
        print(f"✅ Zero Coding Knowledge Requirement: VALIDATED")
        print(f"✅ Natural Conversation Interface: VALIDATED")
    else:
        print(f"\n⚠️  SOME TESTS FAILED - REVIEW REQUIRED")

    print("="*100)

    return result.wasSuccessful()

if __name__ == '__main__':
    # Run comprehensive test suite
    success = run_comprehensive_phase3_tests()

    if success:
        print("\n🎯 PHASE 3 IMPLEMENTATION: COMPLETE AND VALIDATED")
    else:
        print("\n⚠️  PHASE 3 IMPLEMENTATION: NEEDS ATTENTION")
        sys.exit(1)