# AI Coding Agent - Conversation Context Management
"""
Conversation context management for maintaining chat history and context
"""

from typing import List, Dict, Optional, Any
from dataclasses import dataclass, field
from datetime import datetime
import uuid
from src.logger import get_logger
from src.config import get_config

config = get_config()

@dataclass
class Message:
    """Represents a single message in a conversation"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    role: str = "user"  # user, assistant, system
    content: str = ""
    timestamp: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert message to dictionary"""
        return {
            "id": self.id,
            "role": self.role,
            "content": self.content,
            "timestamp": self.timestamp.isoformat(),
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Message':
        """Create message from dictionary"""
        timestamp = datetime.fromisoformat(data["timestamp"]) if "timestamp" in data else datetime.now()
        return cls(
            id=data.get("id", str(uuid.uuid4())),
            role=data.get("role", "user"),
            content=data.get("content", ""),
            timestamp=timestamp,
            metadata=data.get("metadata", {})
        )

@dataclass
class Conversation:
    """Represents a conversation with context management"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    title: str = "New Conversation"
    messages: List[Message] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)
    max_context_length: int = 4000  # Maximum context length in tokens (approximate)
    
    def add_message(self, role: str, content: str, metadata: Optional[Dict[str, Any]] = None) -> Message:
        """Add a message to the conversation"""
        message = Message(
            role=role,
            content=content,
            metadata=metadata or {}
        )
        self.messages.append(message)
        self.updated_at = datetime.now()
        
        # Auto-generate title from first user message
        if not self.title or self.title == "New Conversation":
            if role == "user" and content.strip():
                self.title = content[:50] + "..." if len(content) > 50 else content
        
        return message
    
    def get_context_messages(self, max_messages: Optional[int] = None) -> List[Message]:
        """
        Get messages for context, respecting token limits
        
        Args:
            max_messages: Maximum number of recent messages to include
            
        Returns:
            List of messages to use as context
        """
        if not self.messages:
            return []
        
        # Start with recent messages
        recent_messages = self.messages[-max_messages:] if max_messages else self.messages
        
        # Estimate token count (rough approximation: 1 token ≈ 4 characters)
        total_chars = 0
        context_messages = []
        
        # Add messages from most recent backwards until we hit the limit
        for message in reversed(recent_messages):
            message_chars = len(message.content) + len(message.role) + 50  # Extra for formatting
            if total_chars + message_chars > self.max_context_length * 4:
                break
            context_messages.insert(0, message)
            total_chars += message_chars
        
        return context_messages
    
    def get_formatted_context(self, max_messages: Optional[int] = None) -> str:
        """
        Get formatted context string for LLM input
        
        Args:
            max_messages: Maximum number of recent messages to include
            
        Returns:
            Formatted context string
        """
        context_messages = self.get_context_messages(max_messages)
        
        if not context_messages:
            return ""
        
        formatted_parts = []
        for message in context_messages:
            role_prefix = {
                "user": "Human",
                "assistant": "Assistant", 
                "system": "System"
            }.get(message.role, message.role.title())
            
            formatted_parts.append(f"{role_prefix}: {message.content}")
        
        return "\n\n".join(formatted_parts)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert conversation to dictionary"""
        return {
            "id": self.id,
            "title": self.title,
            "messages": [msg.to_dict() for msg in self.messages],
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "metadata": self.metadata,
            "max_context_length": self.max_context_length
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Conversation':
        """Create conversation from dictionary"""
        created_at = datetime.fromisoformat(data["created_at"]) if "created_at" in data else datetime.now()
        updated_at = datetime.fromisoformat(data["updated_at"]) if "updated_at" in data else datetime.now()
        
        messages = [Message.from_dict(msg_data) for msg_data in data.get("messages", [])]
        
        return cls(
            id=data.get("id", str(uuid.uuid4())),
            title=data.get("title", "New Conversation"),
            messages=messages,
            created_at=created_at,
            updated_at=updated_at,
            metadata=data.get("metadata", {}),
            max_context_length=data.get("max_context_length", 4000)
        )

class ConversationManager:
    """Manages multiple conversations and context"""
    
    def __init__(self):
        """Initialize conversation manager"""
        self.logger = get_logger(__name__)
        self.conversations: Dict[str, Conversation] = {}
        self.current_conversation_id: Optional[str] = None
        
    def create_conversation(self, title: str = "New Conversation") -> Conversation:
        """Create a new conversation"""
        conversation = Conversation(title=title)
        self.conversations[conversation.id] = conversation
        self.current_conversation_id = conversation.id
        
        self.logger.info(f"Created new conversation: {conversation.id} - {title}")
        return conversation
    
    def get_conversation(self, conversation_id: str) -> Optional[Conversation]:
        """Get conversation by ID"""
        return self.conversations.get(conversation_id)
    
    def get_current_conversation(self) -> Optional[Conversation]:
        """Get the current active conversation"""
        if self.current_conversation_id:
            return self.conversations.get(self.current_conversation_id)
        return None
    
    def set_current_conversation(self, conversation_id: str) -> bool:
        """Set the current active conversation"""
        if conversation_id in self.conversations:
            self.current_conversation_id = conversation_id
            self.logger.info(f"Switched to conversation: {conversation_id}")
            return True
        return False
    
    def add_message_to_current(self, role: str, content: str, metadata: Optional[Dict[str, Any]] = None) -> Optional[Message]:
        """Add message to current conversation"""
        conversation = self.get_current_conversation()
        if not conversation:
            # Create new conversation if none exists
            conversation = self.create_conversation()
        
        message = conversation.add_message(role, content, metadata)
        self.logger.info(f"Added {role} message to conversation {conversation.id}")
        return message
    
    def get_conversation_list(self) -> List[Dict[str, Any]]:
        """Get list of all conversations with basic info"""
        conversations = []
        for conv in self.conversations.values():
            conversations.append({
                "id": conv.id,
                "title": conv.title,
                "message_count": len(conv.messages),
                "created_at": conv.created_at.isoformat(),
                "updated_at": conv.updated_at.isoformat()
            })
        
        # Sort by updated_at descending (most recent first)
        conversations.sort(key=lambda x: x["updated_at"], reverse=True)
        return conversations
    
    def delete_conversation(self, conversation_id: str) -> bool:
        """Delete a conversation"""
        if conversation_id in self.conversations:
            del self.conversations[conversation_id]
            
            # If this was the current conversation, clear it
            if self.current_conversation_id == conversation_id:
                self.current_conversation_id = None
            
            self.logger.info(f"Deleted conversation: {conversation_id}")
            return True
        return False
