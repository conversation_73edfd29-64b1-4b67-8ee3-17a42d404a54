# AI Coding Agent - Modern GUI Interface
"""
Modern dual-pane GUI interface with communication panel and live project view.
Built with React/Next.js frontend and Flask backend for real-time interaction.
"""

import os
import json
import asyncio
import threading
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime

from flask import Flask, render_template, request, jsonify, send_from_directory
from flask_socketio import SocketIO, emit, join_room, leave_room
from flask_cors import CORS

from src.logger import get_logger
from src.database import DatabaseManager
from src.prototype_orchestrator import PrototypeOrchestrator
from src.enhanced_project_generators import EnhancedProjectGenerator

logger = get_logger(__name__)

@dataclass
class UIMessage:
    """Message structure for UI communication"""
    id: str
    type: str  # 'user', 'assistant', 'system', 'error'
    content: str
    timestamp: datetime
    metadata: Optional[Dict[str, Any]] = None

@dataclass
class ProjectState:
    """Current project state for UI"""
    name: Optional[str] = None
    path: Optional[str] = None
    framework: Optional[str] = None
    status: str = "idle"  # idle, generating, building, running, error
    files: List[str] = None
    preview_url: Optional[str] = None

class ModernGUIInterface:
    """Modern dual-pane GUI interface for the AI Coding Agent"""

    def __init__(self, host: str = "localhost", port: int = 3000):
        self.logger = get_logger(self.__class__.__name__)
        self.host = host
        self.port = port
        
        # Initialize components
        self.db = DatabaseManager()
        self.orchestrator = PrototypeOrchestrator()
        self.project_generator = EnhancedProjectGenerator()
        
        # UI state
        self.active_sessions: Dict[str, Dict] = {}
        self.current_project: ProjectState = ProjectState()
        self.message_history: List[UIMessage] = []
        
        # Initialize Flask app
        self.app = Flask(__name__, 
                        template_folder='../ui/templates',
                        static_folder='../ui/static')
        self.app.config['SECRET_KEY'] = 'ai-coding-agent-secret-key'
        
        # Enable CORS for development
        CORS(self.app, origins=["http://localhost:3000", "http://localhost:5173"])
        
        # Initialize SocketIO for real-time communication
        self.socketio = SocketIO(self.app, cors_allowed_origins="*", async_mode='threading')
        
        # Setup routes and socket handlers
        self._setup_routes()
        self._setup_socket_handlers()
        
        self.logger.info("Modern GUI Interface initialized")

    def _setup_routes(self):
        """Setup Flask routes for the web interface"""
        
        @self.app.route('/')
        def index():
            """Main interface route"""
            return render_template('index.html')
        
        @self.app.route('/api/health')
        def health_check():
            """Health check endpoint"""
            return jsonify({
                'status': 'healthy',
                'timestamp': datetime.now().isoformat(),
                'version': '1.0.0'
            })
        
        @self.app.route('/api/project/status')
        def project_status():
            """Get current project status"""
            return jsonify(asdict(self.current_project))
        
        @self.app.route('/api/messages')
        def get_messages():
            """Get message history"""
            return jsonify([asdict(msg) for msg in self.message_history[-50:]])
        
        @self.app.route('/api/project/generate', methods=['POST'])
        def generate_project():
            """Generate new project from requirements"""
            try:
                data = request.get_json()
                requirements = data.get('requirements', '')
                project_type = data.get('project_type', 'react_app')
                
                # Start project generation in background
                threading.Thread(
                    target=self._generate_project_async,
                    args=(requirements, project_type, request.sid if hasattr(request, 'sid') else 'default')
                ).start()
                
                return jsonify({'status': 'started', 'message': 'Project generation started'})
                
            except Exception as e:
                self.logger.error(f"Error starting project generation: {e}")
                return jsonify({'error': str(e)}), 500

    def _setup_socket_handlers(self):
        """Setup SocketIO event handlers for real-time communication"""
        
        @self.socketio.on('connect')
        def handle_connect():
            """Handle client connection"""
            session_id = request.sid
            self.active_sessions[session_id] = {
                'connected_at': datetime.now(),
                'user_id': f'user_{session_id[:8]}'
            }
            
            emit('connected', {
                'session_id': session_id,
                'message': 'Connected to AI Coding Agent'
            })
            
            # Send current project state
            emit('project_state', asdict(self.current_project))
            
            self.logger.info(f"Client connected: {session_id}")
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            """Handle client disconnection"""
            session_id = request.sid
            if session_id in self.active_sessions:
                del self.active_sessions[session_id]
            
            self.logger.info(f"Client disconnected: {session_id}")
        
        @self.socketio.on('send_message')
        def handle_message(data):
            """Handle incoming messages from UI"""
            try:
                message_content = data.get('message', '')
                session_id = request.sid
                
                # Create user message
                user_message = UIMessage(
                    id=f"msg_{datetime.now().timestamp()}",
                    type='user',
                    content=message_content,
                    timestamp=datetime.now()
                )
                
                self.message_history.append(user_message)
                
                # Broadcast to all clients
                emit('new_message', asdict(user_message), broadcast=True)
                
                # Process message and generate response
                threading.Thread(
                    target=self._process_message_async,
                    args=(message_content, session_id)
                ).start()
                
            except Exception as e:
                self.logger.error(f"Error handling message: {e}")
                emit('error', {'message': str(e)})
        
        @self.socketio.on('join_room')
        def handle_join_room(data):
            """Handle room joining for project collaboration"""
            room = data.get('room', 'default')
            join_room(room)
            emit('joined_room', {'room': room})

    def _generate_project_async(self, requirements: str, project_type: str, session_id: str):
        """Generate project asynchronously"""
        try:
            self.current_project.status = "generating"
            self.socketio.emit('project_state', asdict(self.current_project))
            
            # Use enhanced project generator
            result = self.project_generator.generate_project(
                project_type=project_type,
                project_name="ai_generated_project",
                project_path="./projects",
                requirements={"description": requirements}
            )
            
            if result['success']:
                self.current_project.name = "ai_generated_project"
                self.current_project.path = result['project_path']
                self.current_project.framework = result['framework']
                self.current_project.status = "ready"
                self.current_project.files = result['generated_files']
                
                # Send success message
                success_message = UIMessage(
                    id=f"msg_{datetime.now().timestamp()}",
                    type='assistant',
                    content=f"✅ Successfully generated {project_type} project with {len(result['generated_files'])} files!",
                    timestamp=datetime.now(),
                    metadata={'project_result': result}
                )
                
                self.message_history.append(success_message)
                self.socketio.emit('new_message', asdict(success_message))
                
            else:
                self.current_project.status = "error"
                error_message = UIMessage(
                    id=f"msg_{datetime.now().timestamp()}",
                    type='error',
                    content=f"❌ Project generation failed: {result.get('error', 'Unknown error')}",
                    timestamp=datetime.now()
                )
                
                self.message_history.append(error_message)
                self.socketio.emit('new_message', asdict(error_message))
            
            self.socketio.emit('project_state', asdict(self.current_project))
            
        except Exception as e:
            self.logger.error(f"Error in async project generation: {e}")
            self.current_project.status = "error"
            self.socketio.emit('project_state', asdict(self.current_project))

    def _process_message_async(self, message: str, session_id: str):
        """Process user message asynchronously"""
        try:
            # Simple response for now - can be enhanced with LLM integration
            response_content = f"I received your message: '{message}'. How can I help you build your project?"
            
            assistant_message = UIMessage(
                id=f"msg_{datetime.now().timestamp()}",
                type='assistant',
                content=response_content,
                timestamp=datetime.now()
            )
            
            self.message_history.append(assistant_message)
            self.socketio.emit('new_message', asdict(assistant_message))
            
        except Exception as e:
            self.logger.error(f"Error processing message: {e}")

    def run(self, debug: bool = True):
        """Run the GUI interface"""
        self.logger.info(f"Starting Modern GUI Interface on {self.host}:{self.port}")
        self.socketio.run(self.app, host=self.host, port=self.port, debug=debug)

    def stop(self):
        """Stop the GUI interface"""
        self.logger.info("Stopping Modern GUI Interface")
        # Cleanup code here if needed
