# AI Coding Agent - Modern GUI Interface
"""
Modern dual-pane GUI interface with communication panel and live project view.
Built with React/Next.js frontend and Flask backend for real-time interaction.
"""

import os
import json
import asyncio
import threading
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime

from flask import Flask, render_template, request, jsonify, send_from_directory
from flask_socketio import SocketIO, emit, join_room, leave_room
from flask_cors import CORS
import re
import base64
import mimetypes

from src.logger import get_logger
from src.database import DatabaseManager
from src.prototype_orchestrator import PrototypeOrchestrator
from src.enhanced_project_generators import EnhancedProjectGenerator

logger = get_logger(__name__)

@dataclass
class UIMessage:
    """Message structure for UI communication"""
    id: str
    type: str  # 'user', 'assistant', 'system', 'error'
    content: str
    timestamp: datetime
    metadata: Optional[Dict[str, Any]] = None

@dataclass
class ProjectState:
    """Current project state for UI"""
    name: Optional[str] = None
    path: Optional[str] = None
    framework: Optional[str] = None
    status: str = "idle"  # idle, generating, building, running, error
    files: Optional[List[str]] = None
    preview_url: Optional[str] = None

@dataclass
class FileAttachment:
    """File attachment structure"""
    filename: str
    content: str  # base64 encoded
    mime_type: str
    size: int

class ModernGUIInterface:
    """Modern dual-pane GUI interface for the AI Coding Agent"""

    def __init__(self, host: str = "localhost", port: int = 3000):
        self.logger = get_logger(self.__class__.__name__)
        self.host = host
        self.port = port
        
        # Initialize components
        self.db = DatabaseManager()
        self.orchestrator = PrototypeOrchestrator()
        self.project_generator = EnhancedProjectGenerator()
        
        # UI state
        self.active_sessions: Dict[str, Dict] = {}
        self.current_project: ProjectState = ProjectState()
        self.message_history: List[UIMessage] = []
        
        # Initialize Flask app
        self.app = Flask(__name__, 
                        template_folder='../ui/templates',
                        static_folder='../ui/static')
        self.app.config['SECRET_KEY'] = 'ai-coding-agent-secret-key'
        
        # Enable CORS for development
        CORS(self.app, origins=["http://localhost:3000", "http://localhost:5173"])
        
        # Initialize SocketIO for real-time communication
        self.socketio = SocketIO(self.app, cors_allowed_origins="*", async_mode='threading')
        
        # Setup routes and socket handlers
        self._setup_routes()
        self._setup_socket_handlers()
        
        self.logger.info("Modern GUI Interface initialized")

    def _setup_routes(self):
        """Setup Flask routes for the web interface"""
        
        @self.app.route('/')
        def index():
            """Main interface route"""
            return render_template('index.html')
        
        @self.app.route('/api/health')
        def health_check():
            """Health check endpoint"""
            return jsonify({
                'status': 'healthy',
                'timestamp': datetime.now().isoformat(),
                'version': '1.0.0'
            })
        
        @self.app.route('/api/project/status')
        def project_status():
            """Get current project status"""
            return jsonify(asdict(self.current_project))
        
        @self.app.route('/api/messages')
        def get_messages():
            """Get message history"""
            return jsonify([asdict(msg) for msg in self.message_history[-50:]])
        
        @self.app.route('/api/project/generate', methods=['POST'])
        def generate_project():
            """Generate new project from requirements"""
            try:
                data = request.get_json()
                requirements = data.get('requirements', '')
                project_type = data.get('project_type', 'react_app')

                # Start project generation in background
                threading.Thread(
                    target=self._generate_project_async,
                    args=(requirements, project_type, 'default')
                ).start()

                return jsonify({'status': 'started', 'message': 'Project generation started'})

            except Exception as e:
                self.logger.error(f"Error starting project generation: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/upload', methods=['POST'])
        def upload_file():
            """Handle file uploads"""
            try:
                if 'file' not in request.files:
                    return jsonify({'error': 'No file provided'}), 400

                file = request.files['file']
                if file.filename == '':
                    return jsonify({'error': 'No file selected'}), 400

                # Read file content
                content = file.read()
                encoded_content = base64.b64encode(content).decode('utf-8')

                # Create attachment
                attachment = FileAttachment(
                    filename=file.filename or 'unknown',
                    content=encoded_content,
                    mime_type=file.content_type or 'application/octet-stream',
                    size=len(content)
                )

                return jsonify({
                    'success': True,
                    'attachment': asdict(attachment)
                })

            except Exception as e:
                self.logger.error(f"Error uploading file: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/search/messages')
        def search_messages():
            """Search through message history"""
            try:
                query = request.args.get('q', '').lower()
                if not query:
                    return jsonify([])

                # Search through message history
                results = []
                for msg in self.message_history:
                    if query in msg.content.lower():
                        results.append(asdict(msg))

                return jsonify(results[-20:])  # Return last 20 matches

            except Exception as e:
                self.logger.error(f"Error searching messages: {e}")
                return jsonify({'error': str(e)}), 500

    def _setup_socket_handlers(self):
        """Setup SocketIO event handlers for real-time communication"""
        
        @self.socketio.on('connect')
        def handle_connect():
            """Handle client connection"""
            session_id = request.sid
            self.active_sessions[session_id] = {
                'connected_at': datetime.now(),
                'user_id': f'user_{session_id[:8]}'
            }
            
            emit('connected', {
                'session_id': session_id,
                'message': 'Connected to AI Coding Agent'
            })
            
            # Send current project state
            emit('project_state', asdict(self.current_project))
            
            self.logger.info(f"Client connected: {session_id}")
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            """Handle client disconnection"""
            session_id = request.sid
            if session_id in self.active_sessions:
                del self.active_sessions[session_id]
            
            self.logger.info(f"Client disconnected: {session_id}")
        
        @self.socketio.on('send_message')
        def handle_message(data):
            """Handle incoming messages from UI"""
            try:
                message_content = data.get('message', '')
                attachments = data.get('attachments', [])
                message_type = data.get('type', 'text')  # text, code, file

                # Create user message
                user_message = UIMessage(
                    id=f"msg_{datetime.now().timestamp()}",
                    type='user',
                    content=message_content,
                    timestamp=datetime.now(),
                    metadata={
                        'attachments': attachments,
                        'message_type': message_type
                    }
                )

                self.message_history.append(user_message)

                # Broadcast to all clients
                emit('new_message', asdict(user_message), broadcast=True)

                # Show typing indicator
                emit('typing_indicator', {'user': 'AI Assistant', 'typing': True}, broadcast=True)

                # Process message and generate response
                threading.Thread(
                    target=self._process_message_async,
                    args=(message_content, attachments, message_type)
                ).start()

            except Exception as e:
                self.logger.error(f"Error handling message: {e}")
                emit('error', {'message': str(e)})

        @self.socketio.on('typing')
        def handle_typing(data):
            """Handle typing indicators"""
            try:
                is_typing = data.get('typing', False)
                user_id = self.active_sessions.get(request.sid, {}).get('user_id', 'Unknown')

                # Broadcast typing status to other clients
                emit('typing_indicator', {
                    'user': user_id,
                    'typing': is_typing
                }, broadcast=True, include_self=False)

            except Exception as e:
                self.logger.error(f"Error handling typing indicator: {e}")

        @self.socketio.on('search_messages')
        def handle_search(data):
            """Handle message search requests"""
            try:
                query = data.get('query', '').lower()
                results = []

                for msg in self.message_history:
                    if query in msg.content.lower():
                        results.append(asdict(msg))

                emit('search_results', {'results': results[-20:]})

            except Exception as e:
                self.logger.error(f"Error searching messages: {e}")
                emit('error', {'message': str(e)})
        
        @self.socketio.on('join_room')
        def handle_join_room(data):
            """Handle room joining for project collaboration"""
            room = data.get('room', 'default')
            join_room(room)
            emit('joined_room', {'room': room})

    def _generate_project_async(self, requirements: str, project_type: str, session_id: str):
        """Generate project asynchronously"""
        try:
            self.current_project.status = "generating"
            self.socketio.emit('project_state', asdict(self.current_project))
            
            # Use enhanced project generator
            result = self.project_generator.generate_project(
                project_type=project_type,
                project_name="ai_generated_project",
                project_path="./projects",
                requirements={"description": requirements}
            )
            
            if result['success']:
                self.current_project.name = "ai_generated_project"
                self.current_project.path = result['project_path']
                self.current_project.framework = result['framework']
                self.current_project.status = "ready"
                self.current_project.files = result['generated_files']
                
                # Send success message
                success_message = UIMessage(
                    id=f"msg_{datetime.now().timestamp()}",
                    type='assistant',
                    content=f"✅ Successfully generated {project_type} project with {len(result['generated_files'])} files!",
                    timestamp=datetime.now(),
                    metadata={'project_result': result}
                )
                
                self.message_history.append(success_message)
                self.socketio.emit('new_message', asdict(success_message))
                
            else:
                self.current_project.status = "error"
                error_message = UIMessage(
                    id=f"msg_{datetime.now().timestamp()}",
                    type='error',
                    content=f"❌ Project generation failed: {result.get('error', 'Unknown error')}",
                    timestamp=datetime.now()
                )
                
                self.message_history.append(error_message)
                self.socketio.emit('new_message', asdict(error_message))
            
            self.socketio.emit('project_state', asdict(self.current_project))
            
        except Exception as e:
            self.logger.error(f"Error in async project generation: {e}")
            self.current_project.status = "error"
            self.socketio.emit('project_state', asdict(self.current_project))

    def _process_message_async(self, message: str, attachments: Optional[List[Dict]] = None, message_type: str = 'text'):
        """Process user message asynchronously with enhanced features"""
        try:
            # Hide typing indicator after processing
            self.socketio.emit('typing_indicator', {'user': 'AI Assistant', 'typing': False})

            # Process different message types
            if message_type == 'code':
                response_content = self._process_code_message(message)
            elif attachments:
                response_content = self._process_file_attachments(message, attachments)
            else:
                response_content = self._process_text_message(message)

            # Add syntax highlighting for code responses
            formatted_content = self._format_response_content(response_content)

            assistant_message = UIMessage(
                id=f"msg_{datetime.now().timestamp()}",
                type='assistant',
                content=formatted_content,
                timestamp=datetime.now(),
                metadata={
                    'has_code': '```' in response_content,
                    'response_type': 'enhanced'
                }
            )

            self.message_history.append(assistant_message)
            self.socketio.emit('new_message', asdict(assistant_message))

        except Exception as e:
            self.logger.error(f"Error processing message: {e}")
            error_message = UIMessage(
                id=f"msg_{datetime.now().timestamp()}",
                type='error',
                content=f"Sorry, I encountered an error processing your message: {str(e)}",
                timestamp=datetime.now()
            )
            self.message_history.append(error_message)
            self.socketio.emit('new_message', asdict(error_message))

    def _process_text_message(self, message: str) -> str:
        """Process regular text messages"""
        # Enhanced message processing with project context
        if any(keyword in message.lower() for keyword in ['create', 'build', 'generate', 'make']):
            return f"""🚀 I'd love to help you create something! Based on your message: "{message}"

I can help you build:
• **Web Applications**: React, Vue, Angular
• **APIs & Backends**: Node.js, Django, Spring Boot
• **Mobile Apps**: React Native, Flutter
• **Full-Stack Projects**: Complete end-to-end solutions

What type of project would you like to create? Just describe your idea and I'll generate the code for you!"""

        elif any(keyword in message.lower() for keyword in ['help', 'how', 'what', 'explain']):
            return f"""💡 I'm here to help! I can assist you with:

**Project Generation:**
- Create complete applications from natural language descriptions
- Support for 8+ modern frameworks and technologies
- Automatic dependency management and configuration

**Code Assistance:**
- Explain code snippets and concepts
- Debug and fix issues
- Suggest improvements and best practices

**File Management:**
- Upload and analyze your existing code
- Generate missing files and components
- Organize project structure

What specific help do you need?"""

        else:
            return f"""Thanks for your message! I understand you said: "{message}"

I'm an AI coding agent that specializes in creating web applications, APIs, and mobile apps. I can generate complete projects from your descriptions using modern frameworks like React, Vue, Angular, Node.js, Django, and more.

How can I help you build something amazing today? 🎯"""

    def _process_code_message(self, message: str) -> str:
        """Process code-related messages"""
        return f"""```
{message}
```

I can see you've shared some code! I can help you with:
- **Code Review**: Analyze and suggest improvements
- **Debugging**: Find and fix issues
- **Enhancement**: Add new features or optimize performance
- **Documentation**: Explain how the code works

What would you like me to do with this code?"""

    def _process_file_attachments(self, message: str, attachments: List[Dict]) -> str:
        """Process messages with file attachments"""
        file_info = []
        for attachment in attachments:
            filename = attachment.get('filename', 'unknown')
            size = attachment.get('size', 0)
            file_info.append(f"📎 {filename} ({self._format_file_size(size)})")

        files_text = "\n".join(file_info)

        return f"""📁 I received your files:
{files_text}

Message: "{message}"

I can help you:
- **Analyze** the uploaded files
- **Integrate** them into a new project
- **Modify** or enhance the code
- **Generate** additional files based on your uploads

What would you like me to do with these files?"""

    def _format_response_content(self, content: str) -> str:
        """Format response content with syntax highlighting markers"""
        # Add markers for frontend to apply syntax highlighting
        return content

    def _format_file_size(self, size_bytes: int) -> str:
        """Format file size in human readable format"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        else:
            return f"{size_bytes / (1024 * 1024):.1f} MB"

    def run(self, debug: bool = True):
        """Run the GUI interface"""
        self.logger.info(f"Starting Modern GUI Interface on {self.host}:{self.port}")
        self.socketio.run(self.app, host=self.host, port=self.port, debug=debug)

    def stop(self):
        """Stop the GUI interface"""
        self.logger.info("Stopping Modern GUI Interface")
        # Cleanup code here if needed
