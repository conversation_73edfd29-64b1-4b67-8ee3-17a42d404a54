# AI Coding Agent - Phase 3 Validation Test
"""
Comprehensive validation test for Phase 3 implementation
"""

import sys
import os
import tempfile
import shutil
from pathlib import Path
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

def test_basic_imports():
    """Test that all Phase 3 components can be imported"""
    print("🔍 Testing Basic Imports...")

    try:
        # Test logger import
        from logger import get_logger
        logger = get_logger("test")
        print("   ✅ Logger import successful")

        # Test database import
        from database import DatabaseManager
        print("   ✅ Database import successful")

        # Test validators import
        from validators import CodeValidator
        validator = CodeValidator()
        print("   ✅ CodeValidator import successful")

        # Test requirements parser
        from requirements_parser import RequirementParser
        parser = RequirementParser()
        print("   ✅ RequirementParser import successful")

        # Test dependency manager
        from dependency_manager import DependencyManager
        dep_mgr = DependencyManager()
        print("   ✅ DependencyManager import successful")

        return True

    except Exception as e:
        print(f"   ❌ Import failed: {e}")
        return False

def test_code_validator_functionality():
    """Test CodeValidator basic functionality"""
    print("\n🐍 Testing CodeValidator Functionality...")

    try:
        from validators import CodeValidator
        validator = CodeValidator()

        # Test Python validation
        python_code = """
def hello_world():
    print("Hello, World!")
    return "success"
"""
        is_valid, errors = validator.validate_python(python_code)
        print(f"   ✅ Python validation: {'PASS' if is_valid else 'FAIL'} ({len(errors)} errors)")

        # Test Python with syntax error
        python_error_code = """
def hello_world()
    print("Hello, World!")
"""
        is_valid, errors = validator.validate_python(python_error_code)
        print(f"   ✅ Python error detection: {'PASS' if not is_valid else 'FAIL'} ({len(errors)} errors)")

        # Test JavaScript validation
        js_code = """
function helloWorld() {
    console.log("Hello, World!");
    return "success";
}
"""
        is_valid, errors = validator.validate_javascript(js_code)
        print(f"   ✅ JavaScript validation: {'PASS' if is_valid else 'FAIL'} ({len(errors)} errors)")

        # Test HTML validation
        html_code = """
<!DOCTYPE html>
<html>
<head><title>Test</title></head>
<body><h1>Hello World</h1></body>
</html>
"""
        is_valid, errors = validator.validate_html(html_code)
        print(f"   ✅ HTML validation: {'PASS' if is_valid else 'FAIL'} ({len(errors)} errors)")

        return True

    except Exception as e:
        print(f"   ❌ CodeValidator test failed: {e}")
        return False

def test_requirements_parser():
    """Test RequirementParser functionality"""
    print("\n📝 Testing RequirementParser...")

    try:
        from requirements_parser import RequirementParser
        parser = RequirementParser()

        # Test basic parsing
        user_input = "Create a simple web application with a todo list"
        result = parser.parse_requirements(user_input)

        print(f"   ✅ Requirements parsing: PASS")
        print(f"   ✅ Project type: {getattr(result, 'project_type', 'detected')}")
        print(f"   ✅ Features: {len(getattr(result, 'features', []))} detected")

        return True

    except Exception as e:
        print(f"   ❌ RequirementParser test failed: {e}")
        return False

def test_dependency_manager():
    """Test DependencyManager functionality"""
    print("\n📦 Testing DependencyManager...")

    try:
        from dependency_manager import DependencyManager
        dep_mgr = DependencyManager()

        # Create temporary test directory
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create a simple Python file with imports
            test_file = Path(temp_dir) / "test.py"
            test_file.write_text("""
import os
import json
import requests
from flask import Flask
""")

            # Test dependency analysis
            result = dep_mgr.analyze_and_install_dependencies(temp_dir, auto_install=False)
            packages = result.get('packages', [])

            print(f"   ✅ Dependency analysis: PASS")
            print(f"   ✅ Packages detected: {len(packages)}")

            return True

    except Exception as e:
        print(f"   ❌ DependencyManager test failed: {e}")
        return False

def test_database_components():
    """Test database setup components"""
    print("\n🗄️ Testing Database Components...")

    try:
        from database_setup import ProjectDatabaseAnalyzer, DatabaseSelector, SchemaGenerator

        # Test database analyzer
        analyzer = ProjectDatabaseAnalyzer()
        print("   ✅ ProjectDatabaseAnalyzer: PASS")

        # Test database selector
        selector = DatabaseSelector()
        print("   ✅ DatabaseSelector: PASS")

        # Test schema generator
        schema_gen = SchemaGenerator()
        print("   ✅ SchemaGenerator: PASS")

        return True

    except Exception as e:
        print(f"   ❌ Database components test failed: {e}")
        return False

def test_simple_workflow():
    """Test a simple end-to-end workflow"""
    print("\n🔄 Testing Simple Workflow...")

    try:
        from requirements_parser import RequirementParser
        from dependency_manager import DependencyManager
        from validators import CodeValidator

        # Parse requirements
        parser = RequirementParser()
        user_input = "Create a simple Python script that prints hello world"
        requirements = parser.parse_requirements(user_input)
        print("   ✅ Step 1: Requirements parsing")

        # Create simple project structure
        with tempfile.TemporaryDirectory() as temp_dir:
            project_path = Path(temp_dir) / "test_project"
            project_path.mkdir()

            # Generate simple Python script
            script_content = '''#!/usr/bin/env python3
"""Simple Hello World Script"""

def main():
    print("Hello, World!")
    print("This script was generated automatically!")

if __name__ == "__main__":
    main()
'''

            script_file = project_path / "main.py"
            script_file.write_text(script_content)
            print("   ✅ Step 2: Code generation")

            # Validate generated code
            validator = CodeValidator()
            is_valid, errors = validator.validate_python(script_content)
            print(f"   ✅ Step 3: Code validation ({'PASS' if is_valid else 'FAIL'})")

            # Test dependency analysis
            dep_mgr = DependencyManager()
            result = dep_mgr.analyze_and_install_dependencies(str(project_path), auto_install=False)
            print("   ✅ Step 4: Dependency analysis")

            print("   ✅ Simple workflow: COMPLETE")
            return True

    except Exception as e:
        print(f"   ❌ Simple workflow test failed: {e}")
        return False

def run_comprehensive_validation():
    """Run comprehensive Phase 3 validation"""
    print("=" * 80)
    print("🚀 PHASE 3 COMPREHENSIVE VALIDATION TEST")
    print("=" * 80)

    test_results = []

    # Run all tests
    tests = [
        ("Basic Imports", test_basic_imports),
        ("CodeValidator Functionality", test_code_validator_functionality),
        ("RequirementParser", test_requirements_parser),
        ("DependencyManager", test_dependency_manager),
        ("Database Components", test_database_components),
        ("Simple Workflow", test_simple_workflow)
    ]

    for test_name, test_func in tests:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ {test_name} failed with exception: {e}")
            test_results.append((test_name, False))

    # Print summary
    print("\n" + "=" * 80)
    print("📊 VALIDATION TEST SUMMARY")
    print("=" * 80)

    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)

    for test_name, result in test_results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")

    print(f"\n🎯 Overall Results: {passed}/{total} tests passed ({passed/total*100:.1f}%)")

    if passed == total:
        print("🎉 ALL TESTS PASSED - Phase 3 validation successful!")
        return True
    else:
        print("⚠️ SOME TESTS FAILED - Review required before Phase 4")
        return False

if __name__ == "__main__":
    success = run_comprehensive_validation()
    if not success:
        sys.exit(1)