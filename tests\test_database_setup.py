# AI Coding Agent - Database Setup Tests
"""
Comprehensive test suite for the database setup automation system
covering project analysis, database selection, schema generation, and connection management.
"""

import json
import tempfile
import pytest
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

from src.database_setup import (
    ProjectDatabaseAnalyzer, DatabaseSelector, SchemaGenerator, MigrationManager,
    DatabaseConnectionManager, DatabaseRequirements, DatabaseSchema, DatabaseTable,
    DatabaseField, DatabaseRelationship, DatabaseType, FieldType, RelationshipType,
    DatabaseSetupError
)


class TestProjectDatabaseAnalyzer:
    """Test cases for ProjectDatabaseAnalyzer"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.analyzer = ProjectDatabaseAnalyzer()
        self.temp_dir = Path(tempfile.mkdtemp())
    
    def teardown_method(self):
        """Clean up test fixtures"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_analyze_python_models(self):
        """Test Python model analysis"""
        python_content = """
class User(db.Model):
    id = Column(Integer, primary_key=True)
    username = Column(String(80), unique=True, nullable=False)
    email = Column(String(120), unique=True, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)

class Post(db.Model):
    id = Column(Integer, primary_key=True)
    title = Column(String(200), nullable=False)
    content = Column(Text)
    user_id = Column(Integer, ForeignKey('user.id'))
"""
        
        analysis = self.analyzer._analyze_python_models(python_content)
        
        # Verify entities detected
        assert 'user' in analysis['entities']
        assert 'post' in analysis['entities']
        
        # Verify data types detected
        assert 'user.username' in analysis['data_types']
        assert 'post.title' in analysis['data_types']
        
        # Verify relationships detected
        assert len(analysis['relationships']) > 0
    
    def test_analyze_javascript_models(self):
        """Test JavaScript/TypeScript model analysis"""
        js_content = """
interface User {
    id: number;
    username: string;
    email: string;
    createdAt: Date;
}

interface Product {
    id: number;
    name: string;
    price: number;
    description: string;
}
"""
        
        analysis = self.analyzer._analyze_javascript_models(js_content)
        
        # Verify entities detected
        assert 'user' in analysis['entities']
        assert 'product' in analysis['entities']
        
        # Verify data types detected
        assert 'user.username' in analysis['data_types']
        assert analysis['data_types']['user.username'] == 'string'
        assert analysis['data_types']['product.price'] == 'integer'
    
    @patch.object(ProjectDatabaseAnalyzer, '_analyze_code_files')
    def test_analyze_project_requirements(self, mock_analyze_code):
        """Test complete project requirements analysis"""
        # Mock code analysis results
        mock_analyze_code.return_value = {
            'entities': ['user', 'product'],
            'relationships': [],
            'data_types': {'user.name': 'string'},
            'constraints': []
        }
        
        # Create test project
        (self.temp_dir / "app.py").write_text("class User: pass")
        
        # Analyze requirements
        requirements = self.analyzer.analyze_project_requirements(self.temp_dir)
        
        # Verify results
        assert isinstance(requirements, DatabaseRequirements)
        assert 'user' in requirements.entities
        assert 'product' in requirements.entities
    
    def test_extract_entities_from_text(self):
        """Test entity extraction from natural language"""
        text = "We need to store user information, product details, and order history"
        
        entities = self.analyzer._extract_entities_from_text(text)
        
        assert 'user' in entities
        assert 'product' in entities
        assert 'order' in entities


class TestDatabaseSelector:
    """Test cases for DatabaseSelector"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.selector = DatabaseSelector()
    
    def test_select_sqlite_for_small_project(self):
        """Test SQLite selection for small projects"""
        requirements = DatabaseRequirements(
            entities=['user', 'post'],
            scalability_needs='small',
            estimated_data_volume='small'
        )
        
        selected_db = self.selector.select_database_type(requirements)
        
        assert selected_db == DatabaseType.SQLITE
    
    def test_select_postgresql_for_complex_project(self):
        """Test PostgreSQL selection for complex projects"""
        requirements = DatabaseRequirements(
            entities=['user', 'product', 'order', 'payment', 'inventory'],
            relationships=[
                {'from': 'user', 'to': 'order', 'type': 'one_to_many'},
                {'from': 'order', 'to': 'product', 'type': 'many_to_many'}
            ],
            scalability_needs='large',
            performance_requirements={'read_heavy': True}
        )
        
        selected_db = self.selector.select_database_type(requirements)
        
        assert selected_db == DatabaseType.POSTGRESQL
    
    def test_get_selection_reasoning(self):
        """Test selection reasoning generation"""
        requirements = DatabaseRequirements(
            entities=['user'],
            scalability_needs='small'
        )
        
        reasoning = self.selector.get_selection_reasoning(
            requirements, DatabaseType.SQLITE
        )
        
        assert 'SQLite selected for:' in reasoning
        assert 'Small scale requirements' in reasoning


class TestSchemaGenerator:
    """Test cases for SchemaGenerator"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.generator = SchemaGenerator()
    
    def test_generate_schema_from_requirements(self):
        """Test schema generation from requirements"""
        requirements = DatabaseRequirements(
            entities=['user', 'post'],
            data_types={
                'user.username': 'string',
                'user.email': 'string',
                'post.title': 'string',
                'post.content': 'text'
            },
            relationships=[
                {'from': 'post', 'to': 'user', 'type': 'many_to_one'}
            ]
        )
        
        schema = self.generator.generate_schema_from_requirements(
            requirements, DatabaseType.SQLITE
        )
        
        # Verify schema structure
        assert isinstance(schema, DatabaseSchema)
        assert schema.database_type == DatabaseType.SQLITE
        assert len(schema.tables) == 2
        
        # Verify tables
        table_names = [table.name for table in schema.tables]
        assert 'user' in table_names
        assert 'post' in table_names
        
        # Verify fields
        user_table = next(table for table in schema.tables if table.name == 'user')
        field_names = [field.name for field in user_table.fields]
        assert 'id' in field_names
        assert 'username' in field_names
        assert 'created_at' in field_names
    
    def test_generate_sql_script(self):
        """Test SQL script generation"""
        # Create test schema
        user_table = DatabaseTable(
            name='user',
            fields=[
                DatabaseField(name='id', field_type=FieldType.INTEGER, primary_key=True),
                DatabaseField(name='username', field_type=FieldType.STRING, nullable=False)
            ]
        )
        
        schema = DatabaseSchema(
            name='test_schema',
            database_type=DatabaseType.SQLITE,
            tables=[user_table]
        )
        
        sql_script = self.generator.generate_sql_script(schema)
        
        # Verify SQL content
        assert 'CREATE TABLE user' in sql_script
        assert 'id INTEGER PRIMARY KEY AUTOINCREMENT' in sql_script
        assert 'username TEXT NOT NULL' in sql_script
    
    def test_map_field_type(self):
        """Test field type mapping"""
        assert self.generator._map_field_type('string') == FieldType.STRING
        assert self.generator._map_field_type('integer') == FieldType.INTEGER
        assert self.generator._map_field_type('boolean') == FieldType.BOOLEAN
        assert self.generator._map_field_type('unknown') == FieldType.STRING  # default


class TestMigrationManager:
    """Test cases for MigrationManager"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.migration_manager = MigrationManager(self.temp_dir)
    
    def teardown_method(self):
        """Clean up test fixtures"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_create_migration(self):
        """Test migration file creation"""
        schema_changes = {
            'create_tables': [
                {
                    'name': 'users',
                    'fields': [
                        {'name': 'id', 'type': 'INTEGER', 'primary_key': True},
                        {'name': 'username', 'type': 'TEXT', 'nullable': False}
                    ]
                }
            ]
        }
        
        migration_path = self.migration_manager.create_migration(
            'create_users_table', schema_changes, DatabaseType.SQLITE
        )
        
        # Verify migration file created
        assert Path(migration_path).exists()
        
        # Verify migration content
        with open(migration_path, 'r') as f:
            content = f.read()
        
        assert 'CREATE TABLE users' in content
        assert 'DOWN Migration' in content
    
    def test_get_pending_migrations(self):
        """Test pending migrations detection"""
        # Create test migration files
        (self.migration_manager.migrations_dir / "20240101_120000_initial.sql").write_text("-- test")
        (self.migration_manager.migrations_dir / "20240102_120000_add_users.sql").write_text("-- test")
        
        applied_migrations = {'20240101_120000_initial.sql'}
        pending = self.migration_manager._get_pending_migrations(applied_migrations)
        
        assert '20240102_120000_add_users.sql' in pending
        assert '20240101_120000_initial.sql' not in pending


class TestDatabaseConnectionManager:
    """Test cases for DatabaseConnectionManager"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.connection_manager = DatabaseConnectionManager(self.temp_dir)
    
    def teardown_method(self):
        """Clean up test fixtures"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_setup_database_connection(self):
        """Test database connection setup"""
        schema = DatabaseSchema(
            name='test_db',
            database_type=DatabaseType.SQLITE,
            tables=[]
        )
        
        connection_params = {
            'database': 'test.db',
            'host': 'localhost'
        }
        
        generated_files = self.connection_manager.setup_database_connection(
            schema, connection_params
        )
        
        # Verify files generated
        assert 'development' in generated_files
        assert 'testing' in generated_files
        assert 'production' in generated_files
        assert 'models' in generated_files
        assert 'utils' in generated_files
        
        # Verify files exist
        for file_path in generated_files.values():
            assert Path(file_path).exists()
    
    def test_create_sqlite_database(self):
        """Test SQLite database creation"""
        connection_params = {'database': 'test.db'}
        
        result = self.connection_manager.create_database_if_not_exists(
            connection_params, DatabaseType.SQLITE
        )
        
        assert result is True
        assert (self.temp_dir / 'test.db').exists()
    
    def test_test_sqlite_connection(self):
        """Test SQLite connection testing"""
        # Create test database
        connection_params = {'database': 'test.db'}
        self.connection_manager.create_database_if_not_exists(
            connection_params, DatabaseType.SQLITE
        )
        
        # Test connection
        result = self.connection_manager.test_connection(
            connection_params, DatabaseType.SQLITE
        )
        
        assert result is True
