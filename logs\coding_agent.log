2025-07-09 15:49:46,919 - agent - INFO - agent.py:31 - 🤖 Coding Agent initialized (Session: ef4dbd45)
2025-07-09 15:49:46,940 - agent - INFO - agent.py:31 - 🤖 Coding Agent initialized (Session: c5203508)
2025-07-09 15:49:46,941 - agent - INFO - agent.py:49 - Generating python code for: create a hello world function
2025-07-09 15:49:46,943 - agent - INFO - agent.py:49 - Generating javascript code for: create a hello world function
2025-07-09 15:49:46,948 - agent - INFO - agent.py:31 - 🤖 Coding Agent initialized (Session: 9c041892)
2025-07-09 15:49:46,952 - agent - INFO - agent.py:31 - 🤖 Coding Agent initialized (Session: 8cd1ddc9)
2025-07-09 15:50:12,375 - agent - INFO - agent.py:31 - 🤖 Coding Agent initialized (Session: 57376caf)
2025-07-09 15:50:12,391 - agent - INFO - agent.py:31 - 🤖 Coding Agent initialized (Session: 8d411751)
2025-07-09 15:50:12,394 - agent - INFO - agent.py:49 - Generating python code for: create a hello world function
2025-07-09 15:50:12,396 - agent - INFO - agent.py:49 - Generating javascript code for: create a hello world function
2025-07-09 15:50:12,405 - agent - INFO - agent.py:31 - 🤖 Coding Agent initialized (Session: 2e2d1e00)
2025-07-09 15:50:12,410 - agent - INFO - agent.py:31 - 🤖 Coding Agent initialized (Session: 6e786f88)
2025-07-09 15:50:39,489 - coding_agent - INFO - logger.py:78 - 🤖 AI Coding Agent Starting Up
2025-07-09 15:50:39,493 - coding_agent - INFO - logger.py:79 - Environment: development
2025-07-09 15:50:39,496 - coding_agent - INFO - logger.py:80 - Debug Mode: True
2025-07-09 15:50:39,498 - coding_agent - INFO - logger.py:81 - Log Level: DEBUG
2025-07-09 15:50:39,502 - coding_agent - INFO - logger.py:82 - Log File: E:\codingagent\logs\coding_agent.log
2025-07-09 15:50:39,505 - coding_agent - INFO - logger.py:83 - Ollama Host: http://localhost:11434
2025-07-09 15:50:39,512 - src.agent - INFO - agent.py:31 - 🤖 Coding Agent initialized (Session: b644ddd1)
2025-07-09 15:50:39,528 - src.agent - INFO - agent.py:49 - Generating python code for: create a hello world function
2025-07-09 15:50:39,592 - src.agent - INFO - agent.py:49 - Generating python code for: 
2025-07-09 15:50:39,607 - src.agent - ERROR - agent.py:57 - Code generation failed: Task description cannot be empty
2025-07-09 15:50:39,613 - coding_agent - INFO - logger.py:87 - 🛑 AI Coding Agent Shutting Down
2025-07-09 23:59:59,396 - models - INFO - models.py:76 - Ollama client initialized with host: http://localhost:11434
2025-07-09 23:59:59,843 - models - INFO - models.py:76 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:00:00,271 - models - INFO - models.py:76 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:00:00,699 - models - INFO - models.py:76 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:00:02,742 - models - INFO - models.py:92 - Ollama connection successful. Available models: 4
2025-07-10 00:00:02,748 - models - ERROR - models.py:111 - Failed to get available models: 'name'
2025-07-10 00:00:03,163 - models - INFO - models.py:76 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:00:05,216 - models - INFO - models.py:92 - Ollama connection successful. Available models: 4
2025-07-10 00:00:05,218 - models - INFO - models.py:138 - Generating response with phi3:mini for role: assistant
2025-07-10 00:00:39,382 - models - INFO - models.py:158 - Response generated in 34.16s, length: 6 chars
2025-07-10 00:00:39,813 - models - INFO - models.py:76 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:00:39,814 - models - INFO - models.py:138 - Generating response with phi3:mini for role: assistant
2025-07-10 00:00:41,877 - models - INFO - models.py:158 - Response generated in 2.06s, length: 0 chars
2025-07-10 00:01:10,854 - coding_agent - INFO - logger.py:78 - 🤖 AI Coding Agent Starting Up
2025-07-10 00:01:10,874 - coding_agent - INFO - logger.py:79 - Environment: development
2025-07-10 00:01:10,877 - coding_agent - INFO - logger.py:80 - Debug Mode: True
2025-07-10 00:01:10,879 - coding_agent - INFO - logger.py:81 - Log Level: DEBUG
2025-07-10 00:01:10,881 - coding_agent - INFO - logger.py:82 - Log File: E:\codingagent\logs\coding_agent.log
2025-07-10 00:01:10,883 - coding_agent - INFO - logger.py:83 - Ollama Host: http://localhost:11434
2025-07-10 00:01:13,110 - src.models - INFO - models.py:76 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:01:15,185 - src.models - INFO - models.py:92 - Ollama connection successful. Available models: 4
2025-07-10 00:01:15,192 - src.models - ERROR - models.py:111 - Failed to get available models: 'name'
2025-07-10 00:01:15,195 - src.models - INFO - models.py:138 - Generating response with phi3:mini for role: backend
2025-07-10 00:01:30,423 - src.models - INFO - models.py:158 - Response generated in 15.23s, length: 179 chars
2025-07-10 00:01:30,426 - src.models - INFO - models.py:138 - Generating response with phi3:mini for role: assistant
2025-07-10 00:05:18,090 - src.models - INFO - models.py:158 - Response generated in 227.66s, length: 4740 chars
2025-07-10 00:05:18,094 - src.models - INFO - models.py:138 - Generating response with phi3:mini for role: frontend
2025-07-10 00:05:35,802 - src.models - INFO - models.py:158 - Response generated in 17.71s, length: 302 chars
2025-07-10 00:05:35,805 - coding_agent - INFO - logger.py:87 - 🛑 AI Coding Agent Shutting Down
2025-07-10 00:09:38,755 - agent - INFO - agent.py:31 - 🤖 Coding Agent initialized (Session: 1ba45e33)
2025-07-10 00:09:38,763 - agent - INFO - agent.py:31 - 🤖 Coding Agent initialized (Session: c0ae8f41)
2025-07-10 00:09:38,765 - agent - INFO - agent.py:49 - Generating python code for: create a hello world function
2025-07-10 00:09:38,766 - agent - INFO - agent.py:49 - Generating javascript code for: create a hello world function
2025-07-10 00:09:38,770 - agent - INFO - agent.py:31 - 🤖 Coding Agent initialized (Session: 7f2a8e57)
2025-07-10 00:09:38,772 - agent - INFO - agent.py:31 - 🤖 Coding Agent initialized (Session: 1e42c125)
2025-07-10 00:09:40,392 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:09:40,831 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:09:41,290 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:09:41,718 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:09:43,752 - agent - INFO - models.py:91 - Ollama connection successful. Available models: 4
2025-07-10 00:09:43,758 - agent - INFO - models.py:114 - Available models: ["model='mistral:7b-instruct-q4_0' modified_at=datetime.datetime(2025, 7, 9, 23, 36, 27, 204717, tzinfo=TzInfo(-06:00)) digest='b17615239298ea5bacfc1c46aa1842737b833779c805542b78f6be29c516d2f4' size=4109865192 details=ModelDetails(parent_model='', format='gguf', family='llama', families=['llama'], parameter_size='7B', quantization_level='Q4_0')", "model='deepseek-coder:1.3b' modified_at=datetime.datetime(2025, 7, 9, 20, 29, 52, 158629, tzinfo=TzInfo(-06:00)) digest='3ddd2d3fc8d2b5fe039d18f859271132fd9c7960ef0be1864984442dc2a915d3' size=776080839 details=ModelDetails(parent_model='', format='gguf', family='llama', families=['llama'], parameter_size='1B', quantization_level='Q4_0')", "model='codellama:7b-code' modified_at=datetime.datetime(2025, 7, 9, 18, 11, 50, 881518, tzinfo=TzInfo(-06:00)) digest='8df0a30bb1e64c5796626dd2b67b538645a3e94f53eba44be125466cd19a38af' size=3825910537 details=ModelDetails(parent_model='', format='gguf', family='llama', families=None, parameter_size='7B', quantization_level='Q4_0')", "model='phi3:mini' modified_at=datetime.datetime(2025, 7, 9, 15, 59, 44, 944896, tzinfo=TzInfo(-06:00)) digest='4f222292793889a9a40a020799cfd28d53f3e01af25d48e06c5e708610fc47e9' size=2176178913 details=ModelDetails(parent_model='', format='gguf', family='phi3', families=['phi3'], parameter_size='3.8B', quantization_level='Q4_0')"]
2025-07-10 00:09:44,196 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:09:46,227 - agent - INFO - models.py:91 - Ollama connection successful. Available models: 4
2025-07-10 00:09:46,229 - agent - INFO - models.py:145 - Generating response with phi3:mini for role: assistant
2025-07-10 00:09:48,313 - agent - INFO - models.py:165 - Response generated in 2.08s, length: 6 chars
2025-07-10 00:09:48,753 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:09:48,754 - agent - INFO - models.py:145 - Generating response with phi3:mini for role: assistant
2025-07-10 00:09:50,781 - agent - INFO - models.py:165 - Response generated in 2.03s, length: 0 chars
2025-07-10 00:11:13,767 - conversation - INFO - conversation.py:179 - Created new conversation: 843a6479-8d3e-4b4b-970c-375b49d51414 - Test Conversation
2025-07-10 00:11:13,782 - conversation - INFO - conversation.py:179 - Created new conversation: a2aab9ea-141f-46b0-bb8f-436ae4305034 - First
2025-07-10 00:11:13,784 - conversation - INFO - conversation.py:179 - Created new conversation: c8795bfa-5cbe-49b4-9596-6f944a8da1d6 - Second
2025-07-10 00:11:13,787 - conversation - INFO - conversation.py:196 - Switched to conversation: a2aab9ea-141f-46b0-bb8f-436ae4305034
2025-07-10 00:11:13,794 - conversation - INFO - conversation.py:179 - Created new conversation: 8a21beac-c5b6-4d4e-9e90-293e2ca37b9a - New Conversation
2025-07-10 00:11:13,799 - conversation - INFO - conversation.py:208 - Added user message to conversation 8a21beac-c5b6-4d4e-9e90-293e2ca37b9a
2025-07-10 00:11:13,806 - conversation - INFO - conversation.py:179 - Created new conversation: 21a69463-9d74-4269-bb7c-c952308c9a68 - First
2025-07-10 00:11:13,808 - conversation - INFO - conversation.py:179 - Created new conversation: 3813913c-01e0-4b04-8001-2e490f52dbd3 - Second
2025-07-10 00:11:13,810 - conversation - INFO - conversation.py:196 - Switched to conversation: 21a69463-9d74-4269-bb7c-c952308c9a68
2025-07-10 00:11:13,814 - conversation - INFO - conversation.py:208 - Added user message to conversation 21a69463-9d74-4269-bb7c-c952308c9a68
2025-07-10 00:11:13,819 - conversation - INFO - conversation.py:179 - Created new conversation: cf4b5a84-8fd8-42c3-824e-6722e0a32eeb - To Delete
2025-07-10 00:11:13,822 - conversation - INFO - conversation.py:236 - Deleted conversation: cf4b5a84-8fd8-42c3-824e-6722e0a32eeb
2025-07-10 00:12:25,260 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:12:25,268 - agent - INFO - agent.py:37 - LLM and conversation management initialized successfully
2025-07-10 00:12:25,270 - agent - INFO - agent.py:43 - 🤖 Coding Agent initialized (Session: 7f4f5da8)
2025-07-10 00:12:25,708 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:12:25,709 - agent - INFO - agent.py:37 - LLM and conversation management initialized successfully
2025-07-10 00:12:25,711 - agent - INFO - agent.py:43 - 🤖 Coding Agent initialized (Session: c6f80a44)
2025-07-10 00:12:25,712 - agent - INFO - agent.py:61 - Generating python code for: create a hello world function
2025-07-10 00:12:25,716 - agent - INFO - conversation.py:179 - Created new conversation: 9fbde049-a697-4c0c-b1a4-2c39af4f320f - New Conversation
2025-07-10 00:12:25,717 - agent - INFO - conversation.py:208 - Added user message to conversation 9fbde049-a697-4c0c-b1a4-2c39af4f320f
2025-07-10 00:12:25,719 - agent - INFO - models.py:145 - Generating response with phi3:mini for role: backend
2025-07-10 00:13:14,885 - agent - INFO - models.py:165 - Response generated in 49.16s, length: 603 chars
2025-07-10 00:13:14,887 - agent - INFO - conversation.py:208 - Added assistant message to conversation 9fbde049-a697-4c0c-b1a4-2c39af4f320f
2025-07-10 00:13:14,889 - agent - INFO - agent.py:61 - Generating javascript code for: create a hello world function
2025-07-10 00:13:14,890 - agent - INFO - conversation.py:208 - Added user message to conversation 9fbde049-a697-4c0c-b1a4-2c39af4f320f
2025-07-10 00:13:14,892 - agent - INFO - models.py:145 - Generating response with phi3:mini for role: frontend
2025-07-10 00:14:18,588 - agent - INFO - models.py:165 - Response generated in 63.70s, length: 695 chars
2025-07-10 00:14:18,590 - agent - INFO - conversation.py:208 - Added assistant message to conversation 9fbde049-a697-4c0c-b1a4-2c39af4f320f
2025-07-10 00:14:19,012 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:14:19,015 - agent - INFO - agent.py:37 - LLM and conversation management initialized successfully
2025-07-10 00:14:19,017 - agent - INFO - agent.py:43 - 🤖 Coding Agent initialized (Session: 5821ccbb)
2025-07-10 00:14:19,421 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:14:19,423 - agent - INFO - agent.py:37 - LLM and conversation management initialized successfully
2025-07-10 00:14:19,424 - agent - INFO - agent.py:43 - 🤖 Coding Agent initialized (Session: 38e6d592)
2025-07-10 00:14:19,466 - agent - INFO - conversation.py:179 - Created new conversation: c03ce521-f936-4292-8c05-e148f01abe0e - Test Conversation
2025-07-10 00:14:19,472 - agent - INFO - conversation.py:179 - Created new conversation: 55bd652d-ab96-4998-a5f7-3887c4be6bd1 - First
2025-07-10 00:14:19,473 - agent - INFO - conversation.py:179 - Created new conversation: 57197253-0921-49bb-956e-6d36d8facfdf - Second
2025-07-10 00:14:19,475 - agent - INFO - conversation.py:196 - Switched to conversation: 55bd652d-ab96-4998-a5f7-3887c4be6bd1
2025-07-10 00:14:19,489 - agent - INFO - conversation.py:179 - Created new conversation: 68187bdb-3e7b-4a96-a426-3da54a46a91d - New Conversation
2025-07-10 00:14:19,492 - agent - INFO - conversation.py:208 - Added user message to conversation 68187bdb-3e7b-4a96-a426-3da54a46a91d
2025-07-10 00:14:19,497 - agent - INFO - conversation.py:179 - Created new conversation: 2e7c9faa-70dd-4d08-a895-e501242616bb - First
2025-07-10 00:14:19,504 - agent - INFO - conversation.py:179 - Created new conversation: 7691bbc1-aaa3-4955-a20d-bb27bc2dc9eb - Second
2025-07-10 00:14:19,506 - agent - INFO - conversation.py:196 - Switched to conversation: 2e7c9faa-70dd-4d08-a895-e501242616bb
2025-07-10 00:14:19,507 - agent - INFO - conversation.py:208 - Added user message to conversation 2e7c9faa-70dd-4d08-a895-e501242616bb
2025-07-10 00:14:19,513 - agent - INFO - conversation.py:179 - Created new conversation: 5328a45a-6a8b-43e1-875e-5d3cc51aaeee - To Delete
2025-07-10 00:14:19,519 - agent - INFO - conversation.py:236 - Deleted conversation: 5328a45a-6a8b-43e1-875e-5d3cc51aaeee
2025-07-10 00:14:19,943 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:14:20,368 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:14:20,865 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:14:21,276 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:14:23,286 - agent - INFO - models.py:91 - Ollama connection successful. Available models: 4
2025-07-10 00:14:23,291 - agent - INFO - models.py:114 - Available models: ["model='mistral:7b-instruct-q4_0' modified_at=datetime.datetime(2025, 7, 9, 23, 36, 27, 204717, tzinfo=TzInfo(-06:00)) digest='b17615239298ea5bacfc1c46aa1842737b833779c805542b78f6be29c516d2f4' size=4109865192 details=ModelDetails(parent_model='', format='gguf', family='llama', families=['llama'], parameter_size='7B', quantization_level='Q4_0')", "model='deepseek-coder:1.3b' modified_at=datetime.datetime(2025, 7, 9, 20, 29, 52, 158629, tzinfo=TzInfo(-06:00)) digest='3ddd2d3fc8d2b5fe039d18f859271132fd9c7960ef0be1864984442dc2a915d3' size=776080839 details=ModelDetails(parent_model='', format='gguf', family='llama', families=['llama'], parameter_size='1B', quantization_level='Q4_0')", "model='codellama:7b-code' modified_at=datetime.datetime(2025, 7, 9, 18, 11, 50, 881518, tzinfo=TzInfo(-06:00)) digest='8df0a30bb1e64c5796626dd2b67b538645a3e94f53eba44be125466cd19a38af' size=3825910537 details=ModelDetails(parent_model='', format='gguf', family='llama', families=None, parameter_size='7B', quantization_level='Q4_0')", "model='phi3:mini' modified_at=datetime.datetime(2025, 7, 9, 15, 59, 44, 944896, tzinfo=TzInfo(-06:00)) digest='4f222292793889a9a40a020799cfd28d53f3e01af25d48e06c5e708610fc47e9' size=2176178913 details=ModelDetails(parent_model='', format='gguf', family='phi3', families=['phi3'], parameter_size='3.8B', quantization_level='Q4_0')"]
2025-07-10 00:14:23,728 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:14:25,771 - agent - INFO - models.py:91 - Ollama connection successful. Available models: 4
2025-07-10 00:14:25,772 - agent - INFO - models.py:145 - Generating response with phi3:mini for role: assistant
2025-07-10 00:14:27,817 - agent - INFO - models.py:165 - Response generated in 2.04s, length: 6 chars
2025-07-10 00:14:28,232 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:14:28,233 - agent - INFO - models.py:145 - Generating response with phi3:mini for role: assistant
2025-07-10 00:14:30,259 - agent - INFO - models.py:165 - Response generated in 2.02s, length: 0 chars
2025-07-10 00:15:54,893 - models - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:15:55,344 - models - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:15:55,772 - models - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:15:56,206 - models - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:15:58,251 - models - INFO - models.py:91 - Ollama connection successful. Available models: 4
2025-07-10 00:15:58,256 - models - INFO - models.py:114 - Available models: ["model='mistral:7b-instruct-q4_0' modified_at=datetime.datetime(2025, 7, 9, 23, 36, 27, 204717, tzinfo=TzInfo(-06:00)) digest='b17615239298ea5bacfc1c46aa1842737b833779c805542b78f6be29c516d2f4' size=4109865192 details=ModelDetails(parent_model='', format='gguf', family='llama', families=['llama'], parameter_size='7B', quantization_level='Q4_0')", "model='deepseek-coder:1.3b' modified_at=datetime.datetime(2025, 7, 9, 20, 29, 52, 158629, tzinfo=TzInfo(-06:00)) digest='3ddd2d3fc8d2b5fe039d18f859271132fd9c7960ef0be1864984442dc2a915d3' size=776080839 details=ModelDetails(parent_model='', format='gguf', family='llama', families=['llama'], parameter_size='1B', quantization_level='Q4_0')", "model='codellama:7b-code' modified_at=datetime.datetime(2025, 7, 9, 18, 11, 50, 881518, tzinfo=TzInfo(-06:00)) digest='8df0a30bb1e64c5796626dd2b67b538645a3e94f53eba44be125466cd19a38af' size=3825910537 details=ModelDetails(parent_model='', format='gguf', family='llama', families=None, parameter_size='7B', quantization_level='Q4_0')", "model='phi3:mini' modified_at=datetime.datetime(2025, 7, 9, 15, 59, 44, 944896, tzinfo=TzInfo(-06:00)) digest='4f222292793889a9a40a020799cfd28d53f3e01af25d48e06c5e708610fc47e9' size=2176178913 details=ModelDetails(parent_model='', format='gguf', family='phi3', families=['phi3'], parameter_size='3.8B', quantization_level='Q4_0')"]
2025-07-10 00:15:58,694 - models - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:16:00,721 - models - INFO - models.py:91 - Ollama connection successful. Available models: 4
2025-07-10 00:16:00,722 - models - INFO - models.py:145 - Generating response with phi3:mini for role: assistant
2025-07-10 00:16:01,426 - models - INFO - models.py:165 - Response generated in 0.70s, length: 6 chars
2025-07-10 00:16:01,859 - models - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:16:01,860 - models - INFO - models.py:145 - Generating response with phi3:mini for role: assistant
2025-07-10 00:16:03,882 - models - INFO - models.py:165 - Response generated in 2.02s, length: 0 chars
2025-07-10 00:17:32,794 - conversation - INFO - conversation.py:179 - Created new conversation: 4efc0d5e-48dd-4d01-9cdf-69ad94f1d816 - Test Conversation
2025-07-10 00:17:32,811 - conversation - INFO - conversation.py:179 - Created new conversation: 2b1219c6-da00-439c-9120-1f0cedbd3465 - First
2025-07-10 00:17:32,813 - conversation - INFO - conversation.py:179 - Created new conversation: 614f96dd-c40f-44e7-be24-a3739d86b025 - Second
2025-07-10 00:17:32,819 - conversation - INFO - conversation.py:196 - Switched to conversation: 2b1219c6-da00-439c-9120-1f0cedbd3465
2025-07-10 00:17:32,826 - conversation - INFO - conversation.py:179 - Created new conversation: 5eb4e027-c3d8-46d2-8268-150cd4ff9541 - New Conversation
2025-07-10 00:17:32,829 - conversation - INFO - conversation.py:208 - Added user message to conversation 5eb4e027-c3d8-46d2-8268-150cd4ff9541
2025-07-10 00:17:32,835 - conversation - INFO - conversation.py:179 - Created new conversation: ee14439e-1603-4044-8544-d8d98b720eda - First
2025-07-10 00:17:32,842 - conversation - INFO - conversation.py:179 - Created new conversation: 35183fcb-73f1-44fc-a825-ba8702baf3bc - Second
2025-07-10 00:17:32,843 - conversation - INFO - conversation.py:196 - Switched to conversation: ee14439e-1603-4044-8544-d8d98b720eda
2025-07-10 00:17:32,844 - conversation - INFO - conversation.py:208 - Added user message to conversation ee14439e-1603-4044-8544-d8d98b720eda
2025-07-10 00:17:32,848 - conversation - INFO - conversation.py:179 - Created new conversation: 2bb9a96f-472c-43c9-a00a-462441a83389 - To Delete
2025-07-10 00:17:32,854 - conversation - INFO - conversation.py:236 - Deleted conversation: 2bb9a96f-472c-43c9-a00a-462441a83389
2025-07-10 00:17:45,920 - models - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:17:57,793 - models - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:17:59,844 - models - INFO - models.py:91 - Ollama connection successful. Available models: 4
2025-07-10 00:17:59,849 - models - INFO - models.py:114 - Available models: ["model='mistral:7b-instruct-q4_0' modified_at=datetime.datetime(2025, 7, 9, 23, 36, 27, 204717, tzinfo=TzInfo(-06:00)) digest='b17615239298ea5bacfc1c46aa1842737b833779c805542b78f6be29c516d2f4' size=4109865192 details=ModelDetails(parent_model='', format='gguf', family='llama', families=['llama'], parameter_size='7B', quantization_level='Q4_0')", "model='deepseek-coder:1.3b' modified_at=datetime.datetime(2025, 7, 9, 20, 29, 52, 158629, tzinfo=TzInfo(-06:00)) digest='3ddd2d3fc8d2b5fe039d18f859271132fd9c7960ef0be1864984442dc2a915d3' size=776080839 details=ModelDetails(parent_model='', format='gguf', family='llama', families=['llama'], parameter_size='1B', quantization_level='Q4_0')", "model='codellama:7b-code' modified_at=datetime.datetime(2025, 7, 9, 18, 11, 50, 881518, tzinfo=TzInfo(-06:00)) digest='8df0a30bb1e64c5796626dd2b67b538645a3e94f53eba44be125466cd19a38af' size=3825910537 details=ModelDetails(parent_model='', format='gguf', family='llama', families=None, parameter_size='7B', quantization_level='Q4_0')", "model='phi3:mini' modified_at=datetime.datetime(2025, 7, 9, 15, 59, 44, 944896, tzinfo=TzInfo(-06:00)) digest='4f222292793889a9a40a020799cfd28d53f3e01af25d48e06c5e708610fc47e9' size=2176178913 details=ModelDetails(parent_model='', format='gguf', family='phi3', families=['phi3'], parameter_size='3.8B', quantization_level='Q4_0')"]
2025-07-10 00:18:10,727 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:18:10,734 - agent - INFO - agent.py:37 - LLM and conversation management initialized successfully
2025-07-10 00:18:10,740 - agent - INFO - agent.py:43 - 🤖 Coding Agent initialized (Session: df82778f)
2025-07-10 00:18:11,176 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:18:11,177 - agent - INFO - agent.py:37 - LLM and conversation management initialized successfully
2025-07-10 00:18:11,178 - agent - INFO - agent.py:43 - 🤖 Coding Agent initialized (Session: 6cb0d027)
2025-07-10 00:18:11,180 - agent - INFO - agent.py:61 - Generating python code for: create a hello world function
2025-07-10 00:18:11,182 - agent - INFO - conversation.py:179 - Created new conversation: c03d6df6-a808-4e61-b432-7ff55b6ea2a9 - New Conversation
2025-07-10 00:18:11,186 - agent - INFO - conversation.py:208 - Added user message to conversation c03d6df6-a808-4e61-b432-7ff55b6ea2a9
2025-07-10 00:18:11,187 - agent - INFO - models.py:145 - Generating response with phi3:mini for role: backend
2025-07-10 00:18:52,856 - agent - INFO - models.py:165 - Response generated in 41.67s, length: 612 chars
2025-07-10 00:18:52,859 - agent - INFO - conversation.py:208 - Added assistant message to conversation c03d6df6-a808-4e61-b432-7ff55b6ea2a9
2025-07-10 00:18:52,861 - agent - INFO - agent.py:61 - Generating javascript code for: create a hello world function
2025-07-10 00:18:52,863 - agent - INFO - conversation.py:208 - Added user message to conversation c03d6df6-a808-4e61-b432-7ff55b6ea2a9
2025-07-10 00:18:52,864 - agent - INFO - models.py:145 - Generating response with phi3:mini for role: frontend
2025-07-10 00:20:29,125 - agent - INFO - models.py:165 - Response generated in 96.26s, length: 1300 chars
2025-07-10 00:20:29,127 - agent - INFO - conversation.py:208 - Added assistant message to conversation c03d6df6-a808-4e61-b432-7ff55b6ea2a9
2025-07-10 00:20:29,541 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:20:29,542 - agent - INFO - agent.py:37 - LLM and conversation management initialized successfully
2025-07-10 00:20:29,543 - agent - INFO - agent.py:43 - 🤖 Coding Agent initialized (Session: c30d3ff1)
2025-07-10 00:20:29,947 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:20:29,949 - agent - INFO - agent.py:37 - LLM and conversation management initialized successfully
2025-07-10 00:20:29,951 - agent - INFO - agent.py:43 - 🤖 Coding Agent initialized (Session: 51994a00)
2025-07-10 00:20:29,990 - agent - INFO - conversation.py:179 - Created new conversation: 07f09943-8ac3-43e4-a28e-75ad8315ddcc - Test Conversation
2025-07-10 00:20:29,994 - agent - INFO - conversation.py:179 - Created new conversation: 626bf250-7b6c-455e-8bb5-7aec95edb4c0 - First
2025-07-10 00:20:29,999 - agent - INFO - conversation.py:179 - Created new conversation: c35c14de-daa5-450e-b3cd-73a4eec97281 - Second
2025-07-10 00:20:30,003 - agent - INFO - conversation.py:196 - Switched to conversation: 626bf250-7b6c-455e-8bb5-7aec95edb4c0
2025-07-10 00:20:30,008 - agent - INFO - conversation.py:179 - Created new conversation: 8d03adb9-a8b9-4066-a2b9-0d44b4e63c75 - New Conversation
2025-07-10 00:20:30,010 - agent - INFO - conversation.py:208 - Added user message to conversation 8d03adb9-a8b9-4066-a2b9-0d44b4e63c75
2025-07-10 00:20:30,015 - agent - INFO - conversation.py:179 - Created new conversation: fd627676-f99b-4c0d-84ae-21df1fdf5c63 - First
2025-07-10 00:20:30,018 - agent - INFO - conversation.py:179 - Created new conversation: 88592907-6542-43c1-aae8-bb62f7a79ce1 - Second
2025-07-10 00:20:30,021 - agent - INFO - conversation.py:196 - Switched to conversation: fd627676-f99b-4c0d-84ae-21df1fdf5c63
2025-07-10 00:20:30,022 - agent - INFO - conversation.py:208 - Added user message to conversation fd627676-f99b-4c0d-84ae-21df1fdf5c63
2025-07-10 00:20:30,027 - agent - INFO - conversation.py:179 - Created new conversation: 4e692843-1abe-46d8-b3cc-fec84df80b87 - To Delete
2025-07-10 00:20:30,031 - agent - INFO - conversation.py:236 - Deleted conversation: 4e692843-1abe-46d8-b3cc-fec84df80b87
2025-07-10 00:20:30,468 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:20:30,884 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:20:31,311 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:20:31,724 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:20:33,743 - agent - INFO - models.py:91 - Ollama connection successful. Available models: 4
2025-07-10 00:20:33,749 - agent - INFO - models.py:114 - Available models: ["model='mistral:7b-instruct-q4_0' modified_at=datetime.datetime(2025, 7, 9, 23, 36, 27, 204717, tzinfo=TzInfo(-06:00)) digest='b17615239298ea5bacfc1c46aa1842737b833779c805542b78f6be29c516d2f4' size=4109865192 details=ModelDetails(parent_model='', format='gguf', family='llama', families=['llama'], parameter_size='7B', quantization_level='Q4_0')", "model='deepseek-coder:1.3b' modified_at=datetime.datetime(2025, 7, 9, 20, 29, 52, 158629, tzinfo=TzInfo(-06:00)) digest='3ddd2d3fc8d2b5fe039d18f859271132fd9c7960ef0be1864984442dc2a915d3' size=776080839 details=ModelDetails(parent_model='', format='gguf', family='llama', families=['llama'], parameter_size='1B', quantization_level='Q4_0')", "model='codellama:7b-code' modified_at=datetime.datetime(2025, 7, 9, 18, 11, 50, 881518, tzinfo=TzInfo(-06:00)) digest='8df0a30bb1e64c5796626dd2b67b538645a3e94f53eba44be125466cd19a38af' size=3825910537 details=ModelDetails(parent_model='', format='gguf', family='llama', families=None, parameter_size='7B', quantization_level='Q4_0')", "model='phi3:mini' modified_at=datetime.datetime(2025, 7, 9, 15, 59, 44, 944896, tzinfo=TzInfo(-06:00)) digest='4f222292793889a9a40a020799cfd28d53f3e01af25d48e06c5e708610fc47e9' size=2176178913 details=ModelDetails(parent_model='', format='gguf', family='phi3', families=['phi3'], parameter_size='3.8B', quantization_level='Q4_0')"]
2025-07-10 00:20:34,202 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:20:36,239 - agent - INFO - models.py:91 - Ollama connection successful. Available models: 4
2025-07-10 00:20:36,240 - agent - INFO - models.py:145 - Generating response with phi3:mini for role: assistant
2025-07-10 00:20:38,232 - agent - INFO - models.py:165 - Response generated in 1.99s, length: 6 chars
2025-07-10 00:20:38,641 - agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:20:38,642 - agent - INFO - models.py:145 - Generating response with phi3:mini for role: assistant
2025-07-10 00:20:40,694 - agent - INFO - models.py:165 - Response generated in 2.05s, length: 0 chars
2025-07-10 00:23:06,280 - coding_agent - INFO - logger.py:76 - 🤖 AI Coding Agent Starting Up
2025-07-10 00:23:06,286 - coding_agent - INFO - logger.py:77 - Environment: development
2025-07-10 00:23:06,289 - coding_agent - INFO - logger.py:78 - Debug Mode: True
2025-07-10 00:23:06,291 - coding_agent - INFO - logger.py:79 - Log Level: DEBUG
2025-07-10 00:23:06,292 - coding_agent - INFO - logger.py:80 - Log File: E:\codingagent\logs\coding_agent.log
2025-07-10 00:23:06,294 - coding_agent - INFO - logger.py:81 - Ollama Host: http://localhost:11434
2025-07-10 00:23:06,296 - coding_agent - INFO - <string>:1 - Testing logging infrastructure
2025-07-10 00:23:06,297 - coding_agent - WARNING - <string>:1 - This is a warning test
2025-07-10 00:23:06,299 - coding_agent - ERROR - <string>:1 - This is an error test
2025-07-10 00:24:59,649 - src.models - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:25:01,683 - src.models - INFO - models.py:91 - Ollama connection successful. Available models: 4
2025-07-10 00:25:01,689 - src.models - INFO - models.py:114 - Available models: ["model='mistral:7b-instruct-q4_0' modified_at=datetime.datetime(2025, 7, 9, 23, 36, 27, 204717, tzinfo=TzInfo(-06:00)) digest='b17615239298ea5bacfc1c46aa1842737b833779c805542b78f6be29c516d2f4' size=4109865192 details=ModelDetails(parent_model='', format='gguf', family='llama', families=['llama'], parameter_size='7B', quantization_level='Q4_0')", "model='deepseek-coder:1.3b' modified_at=datetime.datetime(2025, 7, 9, 20, 29, 52, 158629, tzinfo=TzInfo(-06:00)) digest='3ddd2d3fc8d2b5fe039d18f859271132fd9c7960ef0be1864984442dc2a915d3' size=776080839 details=ModelDetails(parent_model='', format='gguf', family='llama', families=['llama'], parameter_size='1B', quantization_level='Q4_0')", "model='codellama:7b-code' modified_at=datetime.datetime(2025, 7, 9, 18, 11, 50, 881518, tzinfo=TzInfo(-06:00)) digest='8df0a30bb1e64c5796626dd2b67b538645a3e94f53eba44be125466cd19a38af' size=3825910537 details=ModelDetails(parent_model='', format='gguf', family='llama', families=None, parameter_size='7B', quantization_level='Q4_0')", "model='phi3:mini' modified_at=datetime.datetime(2025, 7, 9, 15, 59, 44, 944896, tzinfo=TzInfo(-06:00)) digest='4f222292793889a9a40a020799cfd28d53f3e01af25d48e06c5e708610fc47e9' size=2176178913 details=ModelDetails(parent_model='', format='gguf', family='phi3', families=['phi3'], parameter_size='3.8B', quantization_level='Q4_0')"]
2025-07-10 00:25:12,289 - src.models - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:25:12,297 - src.models - INFO - models.py:145 - Generating response with phi3:mini for role: assistant
2025-07-10 00:25:20,307 - src.models - INFO - models.py:165 - Response generated in 8.00s, length: 64 chars
2025-07-10 00:26:11,692 - src.conversation - INFO - conversation.py:176 - Created new conversation: 128a2523-d453-44d1-abda-5715702c6b49 - Test Conversation
2025-07-10 00:26:11,696 - src.conversation - INFO - conversation.py:205 - Added user message to conversation 128a2523-d453-44d1-abda-5715702c6b49
2025-07-10 00:26:11,698 - src.conversation - INFO - conversation.py:205 - Added assistant message to conversation 128a2523-d453-44d1-abda-5715702c6b49
2025-07-10 00:26:39,009 - src.models - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:26:39,018 - src.models - INFO - conversation.py:176 - Created new conversation: 94aee67c-616c-413c-b937-88a44f9f5f3e - Test
2025-07-10 00:26:39,024 - src.models - INFO - conversation.py:205 - Added user message to conversation 94aee67c-616c-413c-b937-88a44f9f5f3e
2025-07-10 00:26:39,026 - src.models - INFO - models.py:145 - Generating response with phi3:mini for role: assistant
2025-07-10 00:26:48,817 - src.models - INFO - models.py:165 - Response generated in 9.79s, length: 50 chars
2025-07-10 00:29:49,587 - src.database - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:29:49,622 - src.database - INFO - database.py:162 - Created conversation: test-123 - Test Conversation
2025-07-10 00:29:49,668 - src.database - DEBUG - database.py:269 - Added user message to conversation test-123
2025-07-10 00:30:19,602 - src.database - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:30:31,348 - src.database - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:32:09,287 - src.database - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:32:09,336 - src.database - INFO - database.py:397 - Saved project: Test Project
2025-07-10 00:32:26,431 - src.database - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:34:24,166 - src.database - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:34:24,372 - src.database - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:34:24,382 - src.database - INFO - database.py:162 - Created conversation: c25f7f25-6b1e-44ac-b89e-7b4881738e44 - Test Conversation
2025-07-10 00:34:24,400 - src.database - INFO - database.py:225 - Deleted conversation: c25f7f25-6b1e-44ac-b89e-7b4881738e44
2025-07-10 00:34:24,468 - src.database - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:34:24,476 - src.database - INFO - database.py:162 - Created conversation: f6ab4648-e015-44a4-b53d-b754dd477f90 - Test Conversation
2025-07-10 00:34:24,486 - src.database - DEBUG - database.py:269 - Added user message to conversation f6ab4648-e015-44a4-b53d-b754dd477f90
2025-07-10 00:34:24,495 - src.database - DEBUG - database.py:269 - Added assistant message to conversation f6ab4648-e015-44a4-b53d-b754dd477f90
2025-07-10 00:34:24,571 - src.database - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:34:24,579 - src.database - INFO - database.py:162 - Created conversation: e98309f1-5637-4907-81c7-8e2e957e46ed - Test Conversation
2025-07-10 00:34:24,588 - src.database - INFO - database.py:341 - Saved code generation: python - 22 chars
2025-07-10 00:34:24,668 - src.database - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:34:24,677 - src.database - INFO - database.py:162 - Created conversation: a3fbc958-c1f4-4ec8-945d-d45db8804a66 - Test Conversation
2025-07-10 00:34:24,686 - src.database - INFO - database.py:397 - Saved project: Test Project
2025-07-10 00:34:24,753 - src.database - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:34:24,840 - src.database - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:36:33,276 - src.database - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:36:33,297 - src.database - INFO - database.py:162 - Created conversation: d37a2daa-9b5a-40d2-854f-abc70561aebf - Test Conversation
2025-07-10 00:36:33,355 - src.database - INFO - database.py:341 - Saved code generation: python - 22 chars
2025-07-10 00:36:51,906 - src.database - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:36:51,924 - src.database - INFO - database.py:162 - Created conversation: 4cd19e4b-625f-4d22-97be-495a3405e69d - Test Conversation
2025-07-10 00:36:51,937 - src.database - DEBUG - database.py:269 - Added user message to conversation 4cd19e4b-625f-4d22-97be-495a3405e69d
2025-07-10 00:36:51,950 - src.database - DEBUG - database.py:269 - Added assistant message to conversation 4cd19e4b-625f-4d22-97be-495a3405e69d
2025-07-10 00:37:13,817 - src.database - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:37:13,832 - src.database - INFO - database.py:162 - Created conversation: 81f8fad7-2101-4c3f-9861-f3fc16c3f614 - Test Conversation
2025-07-10 00:37:13,843 - src.database - DEBUG - database.py:269 - Added user message to conversation 81f8fad7-2101-4c3f-9861-f3fc16c3f614
2025-07-10 00:37:13,854 - src.database - DEBUG - database.py:269 - Added assistant message to conversation 81f8fad7-2101-4c3f-9861-f3fc16c3f614
2025-07-10 00:38:50,341 - src.database - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:38:50,401 - src.database - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:38:50,410 - src.database - INFO - database.py:162 - Created conversation: 9158db64-42ba-47d1-aee7-e1dd9fab1ece - Test Conversation
2025-07-10 00:38:50,427 - src.database - INFO - database.py:225 - Deleted conversation: 9158db64-42ba-47d1-aee7-e1dd9fab1ece
2025-07-10 00:38:50,482 - src.database - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:38:50,493 - src.database - INFO - database.py:162 - Created conversation: e72356f1-776f-4a75-b503-b0ce1c9dcc1e - Test Conversation
2025-07-10 00:38:50,505 - src.database - DEBUG - database.py:269 - Added user message to conversation e72356f1-776f-4a75-b503-b0ce1c9dcc1e
2025-07-10 00:38:50,515 - src.database - DEBUG - database.py:269 - Added assistant message to conversation e72356f1-776f-4a75-b503-b0ce1c9dcc1e
2025-07-10 00:38:50,589 - src.database - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:38:50,599 - src.database - INFO - database.py:162 - Created conversation: c8ecc544-343a-4d38-9dae-1d547de6e81c - Test Conversation
2025-07-10 00:38:50,609 - src.database - INFO - database.py:341 - Saved code generation: python - 22 chars
2025-07-10 00:38:50,673 - src.database - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:38:50,682 - src.database - INFO - database.py:162 - Created conversation: de61b2ae-d512-46f5-acdf-cb1c87dd0ebb - Test Conversation
2025-07-10 00:38:50,692 - src.database - INFO - database.py:397 - Saved project: Test Project
2025-07-10 00:38:50,759 - src.database - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:38:50,825 - src.database - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:39:46,059 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:39:46,068 - src.agent - INFO - agent.py:37 - LLM and conversation management initialized successfully
2025-07-10 00:39:46,074 - src.agent - INFO - agent.py:43 - 🤖 Coding Agent initialized (Session: 5b7cf4ee)
2025-07-10 00:39:46,500 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:39:46,502 - src.agent - INFO - agent.py:37 - LLM and conversation management initialized successfully
2025-07-10 00:39:46,503 - src.agent - INFO - agent.py:43 - 🤖 Coding Agent initialized (Session: 81833660)
2025-07-10 00:39:46,505 - src.agent - INFO - agent.py:61 - Generating python code for: create a hello world function
2025-07-10 00:39:46,506 - src.agent - INFO - conversation.py:176 - Created new conversation: d16dd758-9d29-4f7d-ac1a-b181031b3615 - New Conversation
2025-07-10 00:39:46,508 - src.agent - INFO - conversation.py:205 - Added user message to conversation d16dd758-9d29-4f7d-ac1a-b181031b3615
2025-07-10 00:39:46,509 - src.agent - INFO - models.py:145 - Generating response with phi3:mini for role: backend
2025-07-10 00:40:24,133 - src.agent - INFO - models.py:165 - Response generated in 37.62s, length: 512 chars
2025-07-10 00:40:24,134 - src.agent - INFO - conversation.py:205 - Added assistant message to conversation d16dd758-9d29-4f7d-ac1a-b181031b3615
2025-07-10 00:40:24,136 - src.agent - INFO - agent.py:61 - Generating javascript code for: create a hello world function
2025-07-10 00:40:24,137 - src.agent - INFO - conversation.py:205 - Added user message to conversation d16dd758-9d29-4f7d-ac1a-b181031b3615
2025-07-10 00:40:24,139 - src.agent - INFO - models.py:145 - Generating response with phi3:mini for role: frontend
2025-07-10 00:41:31,086 - src.agent - INFO - models.py:165 - Response generated in 66.95s, length: 757 chars
2025-07-10 00:41:31,088 - src.agent - INFO - conversation.py:205 - Added assistant message to conversation d16dd758-9d29-4f7d-ac1a-b181031b3615
2025-07-10 00:41:31,510 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:41:31,511 - src.agent - INFO - agent.py:37 - LLM and conversation management initialized successfully
2025-07-10 00:41:31,513 - src.agent - INFO - agent.py:43 - 🤖 Coding Agent initialized (Session: 482bc806)
2025-07-10 00:41:31,909 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:41:31,911 - src.agent - INFO - agent.py:37 - LLM and conversation management initialized successfully
2025-07-10 00:41:31,912 - src.agent - INFO - agent.py:43 - 🤖 Coding Agent initialized (Session: 8c3e4755)
2025-07-10 00:41:31,955 - src.agent - INFO - conversation.py:176 - Created new conversation: dcb02380-ba23-453c-9e2a-9d2b614cfff9 - Test Conversation
2025-07-10 00:41:31,959 - src.agent - INFO - conversation.py:176 - Created new conversation: f4b18a97-d893-4284-91d4-e450379b4d43 - First
2025-07-10 00:41:31,961 - src.agent - INFO - conversation.py:176 - Created new conversation: 8f54198a-df7e-4afa-801f-11394329e85c - Second
2025-07-10 00:41:31,965 - src.agent - INFO - conversation.py:193 - Switched to conversation: f4b18a97-d893-4284-91d4-e450379b4d43
2025-07-10 00:41:31,971 - src.agent - INFO - conversation.py:176 - Created new conversation: f917be8f-16cb-48d6-ba3f-6e4f93017f0d - New Conversation
2025-07-10 00:41:31,973 - src.agent - INFO - conversation.py:205 - Added user message to conversation f917be8f-16cb-48d6-ba3f-6e4f93017f0d
2025-07-10 00:41:31,977 - src.agent - INFO - conversation.py:176 - Created new conversation: 5c4cb202-6fe9-4ff8-91a5-c7d5a082c19a - First
2025-07-10 00:41:31,982 - src.agent - INFO - conversation.py:176 - Created new conversation: 003b009f-9eb1-4fe8-ab1d-b2b25cde4144 - Second
2025-07-10 00:41:31,985 - src.agent - INFO - conversation.py:193 - Switched to conversation: 5c4cb202-6fe9-4ff8-91a5-c7d5a082c19a
2025-07-10 00:41:31,987 - src.agent - INFO - conversation.py:205 - Added user message to conversation 5c4cb202-6fe9-4ff8-91a5-c7d5a082c19a
2025-07-10 00:41:31,992 - src.agent - INFO - conversation.py:176 - Created new conversation: 27ada4b8-2852-4f65-9ec0-cf3c3153fe9b - To Delete
2025-07-10 00:41:31,994 - src.agent - INFO - conversation.py:233 - Deleted conversation: 27ada4b8-2852-4f65-9ec0-cf3c3153fe9b
2025-07-10 00:41:32,099 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:41:32,224 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:41:32,237 - src.agent - INFO - database.py:162 - Created conversation: 6630439a-28bb-4828-8166-920a3466cacd - Test Conversation
2025-07-10 00:41:32,256 - src.agent - INFO - database.py:225 - Deleted conversation: 6630439a-28bb-4828-8166-920a3466cacd
2025-07-10 00:41:32,313 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:41:32,322 - src.agent - INFO - database.py:162 - Created conversation: 323ea8ee-ca41-4a85-8ac6-c9dafda1cbf5 - Test Conversation
2025-07-10 00:41:32,337 - src.agent - DEBUG - database.py:269 - Added user message to conversation 323ea8ee-ca41-4a85-8ac6-c9dafda1cbf5
2025-07-10 00:41:32,346 - src.agent - DEBUG - database.py:269 - Added assistant message to conversation 323ea8ee-ca41-4a85-8ac6-c9dafda1cbf5
2025-07-10 00:41:32,417 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:41:32,425 - src.agent - INFO - database.py:162 - Created conversation: 3247b566-c09a-41a6-80bf-416cb9f8422a - Test Conversation
2025-07-10 00:41:32,435 - src.agent - INFO - database.py:341 - Saved code generation: python - 22 chars
2025-07-10 00:41:32,492 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:41:32,501 - src.agent - INFO - database.py:162 - Created conversation: 7a5c6506-4286-4a37-a9a7-d868b96053ad - Test Conversation
2025-07-10 00:41:32,510 - src.agent - INFO - database.py:397 - Saved project: Test Project
2025-07-10 00:41:32,574 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:41:32,638 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:41:33,055 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:41:33,474 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:41:33,899 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:41:34,339 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:41:36,377 - src.agent - INFO - models.py:91 - Ollama connection successful. Available models: 4
2025-07-10 00:41:36,385 - src.agent - INFO - models.py:114 - Available models: ["model='mistral:7b-instruct-q4_0' modified_at=datetime.datetime(2025, 7, 9, 23, 36, 27, 204717, tzinfo=TzInfo(-06:00)) digest='b17615239298ea5bacfc1c46aa1842737b833779c805542b78f6be29c516d2f4' size=4109865192 details=ModelDetails(parent_model='', format='gguf', family='llama', families=['llama'], parameter_size='7B', quantization_level='Q4_0')", "model='deepseek-coder:1.3b' modified_at=datetime.datetime(2025, 7, 9, 20, 29, 52, 158629, tzinfo=TzInfo(-06:00)) digest='3ddd2d3fc8d2b5fe039d18f859271132fd9c7960ef0be1864984442dc2a915d3' size=776080839 details=ModelDetails(parent_model='', format='gguf', family='llama', families=['llama'], parameter_size='1B', quantization_level='Q4_0')", "model='codellama:7b-code' modified_at=datetime.datetime(2025, 7, 9, 18, 11, 50, 881518, tzinfo=TzInfo(-06:00)) digest='8df0a30bb1e64c5796626dd2b67b538645a3e94f53eba44be125466cd19a38af' size=3825910537 details=ModelDetails(parent_model='', format='gguf', family='llama', families=None, parameter_size='7B', quantization_level='Q4_0')", "model='phi3:mini' modified_at=datetime.datetime(2025, 7, 9, 15, 59, 44, 944896, tzinfo=TzInfo(-06:00)) digest='4f222292793889a9a40a020799cfd28d53f3e01af25d48e06c5e708610fc47e9' size=2176178913 details=ModelDetails(parent_model='', format='gguf', family='phi3', families=['phi3'], parameter_size='3.8B', quantization_level='Q4_0')"]
2025-07-10 00:41:36,811 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:41:38,845 - src.agent - INFO - models.py:91 - Ollama connection successful. Available models: 4
2025-07-10 00:41:38,847 - src.agent - INFO - models.py:145 - Generating response with phi3:mini for role: assistant
2025-07-10 00:41:40,894 - src.agent - INFO - models.py:165 - Response generated in 2.05s, length: 6 chars
2025-07-10 00:41:41,315 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:41:41,316 - src.agent - INFO - models.py:145 - Generating response with phi3:mini for role: assistant
2025-07-10 00:41:43,354 - src.agent - INFO - models.py:165 - Response generated in 2.04s, length: 0 chars
2025-07-10 00:53:46,242 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:53:46,259 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:53:46,263 - src.agent - INFO - agent.py:41 - All core components initialized successfully
2025-07-10 00:53:46,266 - src.agent - INFO - agent.py:49 - 🤖 Coding Agent initialized (Session: 782aae61)
2025-07-10 00:53:46,683 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 00:53:46,689 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 00:53:46,692 - src.agent - INFO - agent.py:41 - All core components initialized successfully
2025-07-10 00:53:46,695 - src.agent - INFO - agent.py:49 - 🤖 Coding Agent initialized (Session: 2d72b255)
2025-07-10 00:53:46,697 - src.agent - INFO - agent.py:67 - Generating python code for: create a hello world function
2025-07-10 00:53:46,700 - src.agent - INFO - conversation.py:176 - Created new conversation: befc0e28-15fe-413f-8055-e0fce529e89d - New Conversation
2025-07-10 00:53:46,705 - src.agent - INFO - conversation.py:205 - Added user message to conversation befc0e28-15fe-413f-8055-e0fce529e89d
2025-07-10 00:53:46,708 - src.agent - INFO - models.py:145 - Generating response with phi3:mini for role: backend
2025-07-10 00:57:05,051 - src.agent - INFO - models.py:165 - Response generated in 198.34s, length: 3955 chars
2025-07-10 00:57:05,056 - src.agent - WARNING - validators.py:61 - Syntax validation failed: Syntax Error: unterminated string literal (detected at line 12) (<unknown>, line 12)
2025-07-10 00:57:05,062 - src.agent - WARNING - agent.py:184 - Code validation failed for python: ['Syntax Error: unterminated string literal (detected at line 12) (<unknown>, line 12)']
2025-07-10 00:57:05,069 - src.agent - INFO - conversation.py:205 - Added assistant message to conversation befc0e28-15fe-413f-8055-e0fce529e89d
2025-07-10 00:57:05,073 - src.agent - INFO - agent.py:67 - Generating javascript code for: create a hello world function
2025-07-10 00:57:05,078 - src.agent - INFO - conversation.py:205 - Added user message to conversation befc0e28-15fe-413f-8055-e0fce529e89d
2025-07-10 00:57:05,085 - src.agent - INFO - models.py:145 - Generating response with phi3:mini for role: frontend
2025-07-10 01:03:47,019 - src.agent - INFO - models.py:165 - Response generated in 401.93s, length: 4976 chars
2025-07-10 01:03:47,023 - src.agent - INFO - validators.py:163 - JavaScript validation completed: passed
2025-07-10 01:03:47,026 - src.agent - INFO - agent.py:178 - Code validation passed for javascript
2025-07-10 01:03:47,029 - src.agent - INFO - conversation.py:205 - Added assistant message to conversation befc0e28-15fe-413f-8055-e0fce529e89d
2025-07-10 01:03:47,542 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 01:03:47,548 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 01:03:47,554 - src.agent - INFO - agent.py:41 - All core components initialized successfully
2025-07-10 01:03:47,559 - src.agent - INFO - agent.py:49 - 🤖 Coding Agent initialized (Session: a56294df)
2025-07-10 01:03:48,011 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 01:03:48,015 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 01:03:48,017 - src.agent - INFO - agent.py:41 - All core components initialized successfully
2025-07-10 01:03:48,023 - src.agent - INFO - agent.py:49 - 🤖 Coding Agent initialized (Session: 20b6dbe0)
2025-07-10 01:03:48,224 - src.agent - INFO - conversation.py:176 - Created new conversation: 811aed18-5bb9-4507-bfe3-f91f72809cc7 - Test Conversation
2025-07-10 01:03:48,251 - src.agent - INFO - conversation.py:176 - Created new conversation: 1769068e-32d6-47b9-9d75-9f1817b1491b - First
2025-07-10 01:03:48,263 - src.agent - INFO - conversation.py:176 - Created new conversation: db24684a-2a9c-4e95-b509-ca135b47a627 - Second
2025-07-10 01:03:48,268 - src.agent - INFO - conversation.py:193 - Switched to conversation: 1769068e-32d6-47b9-9d75-9f1817b1491b
2025-07-10 01:03:48,280 - src.agent - INFO - conversation.py:176 - Created new conversation: 6452ed1c-adfd-42b8-ac93-1aac7ebfb59c - New Conversation
2025-07-10 01:03:48,284 - src.agent - INFO - conversation.py:205 - Added user message to conversation 6452ed1c-adfd-42b8-ac93-1aac7ebfb59c
2025-07-10 01:03:48,297 - src.agent - INFO - conversation.py:176 - Created new conversation: 861d1819-f38e-4c5f-9d6d-5ab92e978b54 - First
2025-07-10 01:03:48,305 - src.agent - INFO - conversation.py:176 - Created new conversation: a976e860-3fdc-43f1-8ca6-d8b28b5a54a7 - Second
2025-07-10 01:03:48,309 - src.agent - INFO - conversation.py:193 - Switched to conversation: 861d1819-f38e-4c5f-9d6d-5ab92e978b54
2025-07-10 01:03:48,313 - src.agent - INFO - conversation.py:205 - Added user message to conversation 861d1819-f38e-4c5f-9d6d-5ab92e978b54
2025-07-10 01:03:48,324 - src.agent - INFO - conversation.py:176 - Created new conversation: 40e1b344-1a44-43f4-97ae-ea8ac0cdf060 - To Delete
2025-07-10 01:03:48,328 - src.agent - INFO - conversation.py:233 - Deleted conversation: 40e1b344-1a44-43f4-97ae-ea8ac0cdf060
2025-07-10 01:03:48,392 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 01:03:48,450 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 01:03:48,463 - src.agent - INFO - database.py:162 - Created conversation: 8240c282-8a97-4d38-a844-8f36b9861c7a - Test Conversation
2025-07-10 01:03:48,484 - src.agent - INFO - database.py:225 - Deleted conversation: 8240c282-8a97-4d38-a844-8f36b9861c7a
2025-07-10 01:03:48,550 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 01:03:48,564 - src.agent - INFO - database.py:162 - Created conversation: ba259f1d-b558-4d24-9533-85ecbdacb9a1 - Test Conversation
2025-07-10 01:03:48,575 - src.agent - DEBUG - database.py:269 - Added user message to conversation ba259f1d-b558-4d24-9533-85ecbdacb9a1
2025-07-10 01:03:48,586 - src.agent - DEBUG - database.py:269 - Added assistant message to conversation ba259f1d-b558-4d24-9533-85ecbdacb9a1
2025-07-10 01:03:48,662 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 01:03:48,674 - src.agent - INFO - database.py:162 - Created conversation: 418e62b0-e83e-4b13-8d13-fd18f7a33665 - Test Conversation
2025-07-10 01:03:48,684 - src.agent - INFO - database.py:341 - Saved code generation: python - 22 chars
2025-07-10 01:03:48,747 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 01:03:48,761 - src.agent - INFO - database.py:162 - Created conversation: a80bba0f-ca58-4b13-b3ff-c4b33330fbbe - Test Conversation
2025-07-10 01:03:48,771 - src.agent - INFO - database.py:397 - Saved project: Test Project
2025-07-10 01:03:48,836 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 01:03:48,910 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 01:03:49,332 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 01:03:49,336 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 01:03:49,340 - src.agent - INFO - agent.py:41 - All core components initialized successfully
2025-07-10 01:03:49,342 - src.agent - INFO - agent.py:49 - 🤖 Coding Agent initialized (Session: 809e73d0)
2025-07-10 01:03:49,774 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 01:03:49,778 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 01:03:49,780 - src.agent - INFO - agent.py:41 - All core components initialized successfully
2025-07-10 01:03:49,783 - src.agent - INFO - agent.py:49 - 🤖 Coding Agent initialized (Session: faaf283f)
2025-07-10 01:03:49,786 - src.agent - INFO - agent.py:67 - Generating python code for: create a simple hello world function
2025-07-10 01:03:49,789 - src.agent - INFO - conversation.py:176 - Created new conversation: 3b359208-6a62-4658-9015-5442ebe4f386 - New Conversation
2025-07-10 01:03:49,793 - src.agent - INFO - conversation.py:205 - Added user message to conversation 3b359208-6a62-4658-9015-5442ebe4f386
2025-07-10 01:03:49,796 - src.agent - INFO - models.py:145 - Generating response with phi3:mini for role: backend
2025-07-10 01:04:56,443 - src.agent - INFO - models.py:165 - Response generated in 66.64s, length: 1022 chars
2025-07-10 01:04:56,448 - src.agent - WARNING - validators.py:61 - Syntax validation failed: Syntax Error: unterminated string literal (detected at line 15) (<unknown>, line 15)
2025-07-10 01:04:56,451 - src.agent - WARNING - agent.py:184 - Code validation failed for python: ['Syntax Error: unterminated string literal (detected at line 15) (<unknown>, line 15)']
2025-07-10 01:04:56,457 - src.agent - INFO - conversation.py:205 - Added assistant message to conversation 3b359208-6a62-4658-9015-5442ebe4f386
2025-07-10 01:04:56,915 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 01:04:56,919 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 01:04:56,923 - src.agent - INFO - agent.py:41 - All core components initialized successfully
2025-07-10 01:04:56,925 - src.agent - INFO - agent.py:49 - 🤖 Coding Agent initialized (Session: 648cf171)
2025-07-10 01:04:56,929 - src.agent - INFO - agent.py:67 - Generating python code for: create a function that adds two numbers
2025-07-10 01:04:56,931 - src.agent - INFO - conversation.py:176 - Created new conversation: 384aa859-39a8-465b-9cfe-d6d9c56a4ded - New Conversation
2025-07-10 01:04:56,934 - src.agent - INFO - conversation.py:205 - Added user message to conversation 384aa859-39a8-465b-9cfe-d6d9c56a4ded
2025-07-10 01:04:56,937 - src.agent - INFO - models.py:145 - Generating response with phi3:mini for role: backend
2025-07-10 01:05:31,001 - src.agent - INFO - models.py:165 - Response generated in 34.06s, length: 414 chars
2025-07-10 01:05:31,005 - src.agent - WARNING - validators.py:61 - Syntax validation failed: Syntax Error: invalid syntax (<unknown>, line 1)
2025-07-10 01:05:31,007 - src.agent - WARNING - agent.py:184 - Code validation failed for python: ['Syntax Error: invalid syntax (<unknown>, line 1)']
2025-07-10 01:05:31,011 - src.agent - INFO - conversation.py:205 - Added assistant message to conversation 384aa859-39a8-465b-9cfe-d6d9c56a4ded
2025-07-10 01:05:31,016 - src.agent - INFO - agent.py:67 - Generating python code for: now create a function that multiplies two numbers
2025-07-10 01:05:31,020 - src.agent - INFO - conversation.py:205 - Added user message to conversation 384aa859-39a8-465b-9cfe-d6d9c56a4ded
2025-07-10 01:05:31,023 - src.agent - INFO - models.py:145 - Generating response with phi3:mini for role: backend
2025-07-10 01:07:24,618 - src.agent - INFO - models.py:165 - Response generated in 113.59s, length: 1331 chars
2025-07-10 01:07:24,624 - src.agent - WARNING - validators.py:61 - Syntax validation failed: Syntax Error: unterminated string literal (detected at line 1) (<unknown>, line 1)
2025-07-10 01:07:24,627 - src.agent - WARNING - agent.py:184 - Code validation failed for python: ['Syntax Error: unterminated string literal (detected at line 1) (<unknown>, line 1)']
2025-07-10 01:07:24,631 - src.agent - INFO - conversation.py:205 - Added assistant message to conversation 384aa859-39a8-465b-9cfe-d6d9c56a4ded
2025-07-10 01:07:25,068 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 01:07:25,074 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 01:07:25,077 - src.agent - INFO - agent.py:41 - All core components initialized successfully
2025-07-10 01:07:25,079 - src.agent - INFO - agent.py:49 - 🤖 Coding Agent initialized (Session: bbf4a1e4)
2025-07-10 01:07:25,082 - src.agent - INFO - agent.py:67 - Generating python code for: create a test function
2025-07-10 01:07:25,085 - src.agent - INFO - conversation.py:176 - Created new conversation: 813429ef-f3ff-4d53-80a2-df8896048f4f - New Conversation
2025-07-10 01:07:25,089 - src.agent - INFO - conversation.py:205 - Added user message to conversation 813429ef-f3ff-4d53-80a2-df8896048f4f
2025-07-10 01:07:25,091 - src.agent - INFO - models.py:145 - Generating response with phi3:mini for role: backend
2025-07-10 01:09:45,594 - src.agent - INFO - models.py:165 - Response generated in 140.50s, length: 2865 chars
2025-07-10 01:09:45,598 - src.agent - WARNING - validators.py:61 - Syntax validation failed: Syntax Error: unterminated string literal (detected at line 28) (<unknown>, line 28)
2025-07-10 01:09:45,602 - src.agent - WARNING - agent.py:184 - Code validation failed for python: ['Syntax Error: unterminated string literal (detected at line 28) (<unknown>, line 28)']
2025-07-10 01:09:45,606 - src.agent - INFO - conversation.py:205 - Added assistant message to conversation 813429ef-f3ff-4d53-80a2-df8896048f4f
2025-07-10 01:09:45,664 - src.agent - INFO - database.py:162 - Created conversation: 813429ef-f3ff-4d53-80a2-df8896048f4f - create a test function
2025-07-10 01:09:45,701 - src.agent - DEBUG - database.py:269 - Added user message to conversation 813429ef-f3ff-4d53-80a2-df8896048f4f
2025-07-10 01:09:45,739 - src.agent - DEBUG - database.py:269 - Added assistant message to conversation 813429ef-f3ff-4d53-80a2-df8896048f4f
2025-07-10 01:09:46,144 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 01:09:46,149 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 01:09:46,152 - src.agent - INFO - agent.py:41 - All core components initialized successfully
2025-07-10 01:09:46,154 - src.agent - INFO - agent.py:49 - 🤖 Coding Agent initialized (Session: cd2f0a6f)
2025-07-10 01:09:46,158 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 01:09:46,163 - src.agent - INFO - validators.py:76 - Python code validation passed with 1 warnings
2025-07-10 01:09:46,166 - src.agent - INFO - validators.py:163 - JavaScript validation completed: passed
2025-07-10 01:09:46,168 - src.agent - INFO - validators.py:98 - HTML validation completed
2025-07-10 01:09:46,558 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 01:09:46,562 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 01:09:46,565 - src.agent - INFO - agent.py:41 - All core components initialized successfully
2025-07-10 01:09:46,577 - src.agent - INFO - agent.py:49 - 🤖 Coding Agent initialized (Session: e3404b2f)
2025-07-10 01:09:46,580 - src.agent - INFO - agent.py:67 - Generating python code for: My name is Alice
2025-07-10 01:09:46,583 - src.agent - INFO - conversation.py:176 - Created new conversation: bf6d0fb5-d54a-4971-bf9a-282ea22e7c67 - New Conversation
2025-07-10 01:09:46,586 - src.agent - INFO - conversation.py:205 - Added user message to conversation bf6d0fb5-d54a-4971-bf9a-282ea22e7c67
2025-07-10 01:09:46,588 - src.agent - INFO - models.py:145 - Generating response with phi3:mini for role: backend
2025-07-10 01:10:18,736 - src.agent - INFO - models.py:165 - Response generated in 32.15s, length: 601 chars
2025-07-10 01:10:18,739 - src.agent - WARNING - validators.py:61 - Syntax validation failed: Syntax Error: invalid syntax (<unknown>, line 1)
2025-07-10 01:10:18,742 - src.agent - WARNING - agent.py:184 - Code validation failed for python: ['Syntax Error: invalid syntax (<unknown>, line 1)']
2025-07-10 01:10:18,745 - src.agent - INFO - conversation.py:205 - Added assistant message to conversation bf6d0fb5-d54a-4971-bf9a-282ea22e7c67
2025-07-10 01:10:18,747 - src.agent - INFO - agent.py:67 - Generating python code for: What is my name?
2025-07-10 01:10:18,750 - src.agent - INFO - conversation.py:205 - Added user message to conversation bf6d0fb5-d54a-4971-bf9a-282ea22e7c67
2025-07-10 01:10:18,752 - src.agent - INFO - models.py:145 - Generating response with phi3:mini for role: backend
2025-07-10 01:11:29,970 - src.agent - INFO - models.py:165 - Response generated in 71.22s, length: 702 chars
2025-07-10 01:11:29,973 - src.agent - WARNING - validators.py:61 - Syntax validation failed: Syntax Error: unterminated string literal (detected at line 1) (<unknown>, line 1)
2025-07-10 01:11:29,977 - src.agent - WARNING - agent.py:184 - Code validation failed for python: ['Syntax Error: unterminated string literal (detected at line 1) (<unknown>, line 1)']
2025-07-10 01:11:29,981 - src.agent - INFO - conversation.py:205 - Added assistant message to conversation bf6d0fb5-d54a-4971-bf9a-282ea22e7c67
2025-07-10 01:11:30,389 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 01:11:30,393 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 01:11:30,396 - src.agent - INFO - agent.py:41 - All core components initialized successfully
2025-07-10 01:11:30,399 - src.agent - INFO - agent.py:49 - 🤖 Coding Agent initialized (Session: 82c80986)
2025-07-10 01:11:30,403 - src.agent - INFO - agent.py:67 - Generating python code for: 
2025-07-10 01:11:30,405 - src.agent - ERROR - agent.py:122 - Code generation failed: Task description cannot be empty
2025-07-10 01:11:30,408 - src.agent - INFO - agent.py:67 - Generating invalid_language code for: create a function
2025-07-10 01:11:30,410 - src.agent - INFO - conversation.py:176 - Created new conversation: d9b75bf7-52e7-4c32-b5c6-9c530015a6e4 - New Conversation
2025-07-10 01:11:30,413 - src.agent - INFO - conversation.py:205 - Added user message to conversation d9b75bf7-52e7-4c32-b5c6-9c530015a6e4
2025-07-10 01:11:30,415 - src.agent - INFO - models.py:145 - Generating response with phi3:mini for role: assistant
2025-07-10 01:14:30,511 - src.agent - INFO - models.py:165 - Response generated in 180.09s, length: 2897 chars
2025-07-10 01:14:30,516 - src.agent - INFO - agent.py:174 - Validation not supported for invalid_language, returning code as-is
2025-07-10 01:14:30,519 - src.agent - INFO - conversation.py:205 - Added assistant message to conversation d9b75bf7-52e7-4c32-b5c6-9c530015a6e4
2025-07-10 01:14:31,017 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 01:14:31,024 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 01:14:31,029 - src.agent - INFO - agent.py:41 - All core components initialized successfully
2025-07-10 01:14:31,031 - src.agent - INFO - agent.py:49 - 🤖 Coding Agent initialized (Session: 4dbb8c84)
2025-07-10 01:14:31,035 - src.agent - INFO - conversation.py:176 - Created new conversation: 4377fe25-d086-4f90-aec5-77f221680ae7 - Test
2025-07-10 01:14:31,039 - src.agent - INFO - conversation.py:205 - Added user message to conversation 4377fe25-d086-4f90-aec5-77f221680ae7
2025-07-10 01:14:31,045 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 01:14:31,048 - src.agent - INFO - validators.py:76 - Python code validation passed with 1 warnings
2025-07-10 01:14:31,513 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 01:14:31,520 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 01:14:31,526 - src.agent - INFO - agent.py:41 - All core components initialized successfully
2025-07-10 01:14:31,528 - src.agent - INFO - agent.py:49 - 🤖 Coding Agent initialized (Session: f386e5ff)
2025-07-10 01:14:31,532 - src.agent - INFO - agent.py:67 - Generating python code for: create a simple function
2025-07-10 01:14:31,534 - src.agent - INFO - conversation.py:176 - Created new conversation: b27a7722-0d5b-4a11-91df-6678ccb4e799 - New Conversation
2025-07-10 01:14:31,537 - src.agent - INFO - conversation.py:205 - Added user message to conversation b27a7722-0d5b-4a11-91df-6678ccb4e799
2025-07-10 01:14:31,544 - src.agent - INFO - models.py:145 - Generating response with phi3:mini for role: backend
2025-07-10 01:16:02,384 - src.agent - INFO - models.py:165 - Response generated in 90.83s, length: 1505 chars
2025-07-10 01:16:02,388 - src.agent - WARNING - validators.py:61 - Syntax validation failed: Syntax Error: unterminated string literal (detected at line 22) (<unknown>, line 22)
2025-07-10 01:16:02,395 - src.agent - WARNING - agent.py:184 - Code validation failed for python: ['Syntax Error: unterminated string literal (detected at line 22) (<unknown>, line 22)']
2025-07-10 01:16:02,402 - src.agent - INFO - conversation.py:205 - Added assistant message to conversation b27a7722-0d5b-4a11-91df-6678ccb4e799
2025-07-10 01:16:03,079 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 01:16:03,084 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 01:16:03,087 - src.agent - INFO - agent.py:41 - All core components initialized successfully
2025-07-10 01:16:03,090 - src.agent - INFO - agent.py:49 - 🤖 Coding Agent initialized (Session: a738ddcc)
2025-07-10 01:16:03,093 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 01:16:03,095 - src.agent - INFO - validators.py:76 - Python code validation passed with 1 warnings
2025-07-10 01:16:03,532 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 01:16:03,536 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 01:16:03,540 - src.agent - INFO - agent.py:41 - All core components initialized successfully
2025-07-10 01:16:03,542 - src.agent - INFO - agent.py:49 - 🤖 Coding Agent initialized (Session: 784c796b)
2025-07-10 01:16:03,546 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 01:16:03,549 - src.agent - WARNING - validators.py:78 - Python code validation failed with 1 errors
2025-07-10 01:16:03,553 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 01:16:03,558 - src.agent - WARNING - validators.py:78 - Python code validation failed with 1 errors
2025-07-10 01:16:03,561 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 01:16:03,564 - src.agent - WARNING - validators.py:78 - Python code validation failed with 1 errors
2025-07-10 01:16:03,567 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 01:16:03,569 - src.agent - WARNING - validators.py:78 - Python code validation failed with 1 errors
2025-07-10 01:16:03,992 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 01:16:03,996 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 01:16:03,999 - src.agent - INFO - agent.py:41 - All core components initialized successfully
2025-07-10 01:16:04,002 - src.agent - INFO - agent.py:49 - 🤖 Coding Agent initialized (Session: e7fd9ac5)
2025-07-10 01:16:04,011 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 01:16:04,015 - src.agent - INFO - validators.py:76 - Python code validation passed with 0 warnings
2025-07-10 01:16:04,020 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 01:16:04,026 - src.agent - INFO - validators.py:76 - Python code validation passed with 1 warnings
2025-07-10 01:16:04,029 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 01:16:04,032 - src.agent - INFO - validators.py:76 - Python code validation passed with 0 warnings
2025-07-10 01:16:04,035 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 01:16:04,037 - src.agent - INFO - validators.py:76 - Python code validation passed with 1 warnings
2025-07-10 01:16:04,466 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 01:16:04,888 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 01:16:05,311 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 01:16:05,730 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 01:16:07,753 - src.agent - INFO - models.py:91 - Ollama connection successful. Available models: 4
2025-07-10 01:16:07,760 - src.agent - INFO - models.py:114 - Available models: ["model='mistral:7b-instruct-q4_0' modified_at=datetime.datetime(2025, 7, 9, 23, 36, 27, 204717, tzinfo=TzInfo(-06:00)) digest='b17615239298ea5bacfc1c46aa1842737b833779c805542b78f6be29c516d2f4' size=4109865192 details=ModelDetails(parent_model='', format='gguf', family='llama', families=['llama'], parameter_size='7B', quantization_level='Q4_0')", "model='deepseek-coder:1.3b' modified_at=datetime.datetime(2025, 7, 9, 20, 29, 52, 158629, tzinfo=TzInfo(-06:00)) digest='3ddd2d3fc8d2b5fe039d18f859271132fd9c7960ef0be1864984442dc2a915d3' size=776080839 details=ModelDetails(parent_model='', format='gguf', family='llama', families=['llama'], parameter_size='1B', quantization_level='Q4_0')", "model='codellama:7b-code' modified_at=datetime.datetime(2025, 7, 9, 18, 11, 50, 881518, tzinfo=TzInfo(-06:00)) digest='8df0a30bb1e64c5796626dd2b67b538645a3e94f53eba44be125466cd19a38af' size=3825910537 details=ModelDetails(parent_model='', format='gguf', family='llama', families=None, parameter_size='7B', quantization_level='Q4_0')", "model='phi3:mini' modified_at=datetime.datetime(2025, 7, 9, 15, 59, 44, 944896, tzinfo=TzInfo(-06:00)) digest='4f222292793889a9a40a020799cfd28d53f3e01af25d48e06c5e708610fc47e9' size=2176178913 details=ModelDetails(parent_model='', format='gguf', family='phi3', families=['phi3'], parameter_size='3.8B', quantization_level='Q4_0')"]
2025-07-10 01:16:08,208 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 01:16:10,228 - src.agent - INFO - models.py:91 - Ollama connection successful. Available models: 4
2025-07-10 01:16:10,231 - src.agent - INFO - models.py:145 - Generating response with phi3:mini for role: assistant
2025-07-10 01:16:12,242 - src.agent - INFO - models.py:165 - Response generated in 2.01s, length: 6 chars
2025-07-10 01:16:12,671 - src.agent - INFO - models.py:75 - Ollama client initialized with host: http://localhost:11434
2025-07-10 01:16:12,674 - src.agent - INFO - models.py:145 - Generating response with phi3:mini for role: assistant
2025-07-10 01:16:14,710 - src.agent - INFO - models.py:165 - Response generated in 2.03s, length: 0 chars
2025-07-10 08:30:07,028 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 08:30:07,058 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:30:07,061 - src.agent - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 08:30:07,065 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 08:30:07,069 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: 7bbdd92c)
2025-07-10 08:30:07,545 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 08:30:07,551 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:30:07,554 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 08:30:07,557 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: 13ce20df)
2025-07-10 08:30:07,560 - src.agent - WARNING - security.py:77 - Dangerous pattern detected: (union|select|insert|update|delete|drop|create|alter)\s+
2025-07-10 08:30:07,563 - src.agent - ERROR - agent.py:154 - Code generation failed: Input contains potentially dangerous content
2025-07-10 08:30:08,109 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 08:30:08,116 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:30:08,123 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 08:30:08,130 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: b8272267)
2025-07-10 08:30:08,582 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 08:30:08,589 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:30:08,593 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 08:30:08,598 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: 1b20a52d)
2025-07-10 08:30:08,661 - src.agent - INFO - conversation.py:176 - Created new conversation: 57e5c285-7ff5-4913-8a66-c4a05c4fd06c - Test Conversation
2025-07-10 08:30:08,676 - src.agent - INFO - conversation.py:176 - Created new conversation: 48f3ae18-4976-4e8a-8526-a4cb7a41516f - First
2025-07-10 08:30:08,681 - src.agent - INFO - conversation.py:176 - Created new conversation: 9398dc03-19db-492f-bf0b-9b1c027ad262 - Second
2025-07-10 08:30:08,689 - src.agent - INFO - conversation.py:193 - Switched to conversation: 48f3ae18-4976-4e8a-8526-a4cb7a41516f
2025-07-10 08:30:08,699 - src.agent - INFO - conversation.py:176 - Created new conversation: a6aa36e0-8d4e-4788-a8aa-ebbbd0f4461c - New Conversation
2025-07-10 08:30:08,706 - src.agent - INFO - conversation.py:205 - Added user message to conversation a6aa36e0-8d4e-4788-a8aa-ebbbd0f4461c
2025-07-10 08:30:08,716 - src.agent - INFO - conversation.py:176 - Created new conversation: 8fa56dd4-f5be-4dc5-a4eb-ec4b2a2c20d9 - First
2025-07-10 08:30:08,724 - src.agent - INFO - conversation.py:176 - Created new conversation: 630fe727-f54a-471d-ab47-9ca06ebc9023 - Second
2025-07-10 08:30:08,730 - src.agent - INFO - conversation.py:193 - Switched to conversation: 8fa56dd4-f5be-4dc5-a4eb-ec4b2a2c20d9
2025-07-10 08:30:08,739 - src.agent - INFO - conversation.py:205 - Added user message to conversation 8fa56dd4-f5be-4dc5-a4eb-ec4b2a2c20d9
2025-07-10 08:30:08,746 - src.agent - INFO - conversation.py:176 - Created new conversation: b90f42ee-b182-4ac3-b27b-3ba9c0434efa - To Delete
2025-07-10 08:30:08,748 - src.agent - INFO - conversation.py:233 - Deleted conversation: b90f42ee-b182-4ac3-b27b-3ba9c0434efa
2025-07-10 08:30:08,852 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:30:08,927 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:30:08,941 - src.agent - INFO - database.py:162 - Created conversation: c87ce201-63ae-4667-bbb7-91667adc80a4 - Test Conversation
2025-07-10 08:30:08,961 - src.agent - INFO - database.py:225 - Deleted conversation: c87ce201-63ae-4667-bbb7-91667adc80a4
2025-07-10 08:30:09,027 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:30:09,040 - src.agent - INFO - database.py:162 - Created conversation: bf85a0f8-635a-49a1-abe1-d42042e3e81c - Test Conversation
2025-07-10 08:30:09,050 - src.agent - DEBUG - database.py:269 - Added user message to conversation bf85a0f8-635a-49a1-abe1-d42042e3e81c
2025-07-10 08:30:09,060 - src.agent - DEBUG - database.py:269 - Added assistant message to conversation bf85a0f8-635a-49a1-abe1-d42042e3e81c
2025-07-10 08:30:09,133 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:30:09,150 - src.agent - INFO - database.py:162 - Created conversation: 94938069-fe78-4cb2-a5be-2c181da9f100 - Test Conversation
2025-07-10 08:30:09,164 - src.agent - INFO - database.py:341 - Saved code generation: python - 22 chars
2025-07-10 08:30:09,229 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:30:09,243 - src.agent - INFO - database.py:162 - Created conversation: 3ab5143b-daee-455c-85b4-f95ecb55d670 - Test Conversation
2025-07-10 08:30:09,256 - src.agent - INFO - database.py:397 - Saved project: Test Project
2025-07-10 08:30:09,318 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:30:09,389 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:30:09,850 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 08:30:09,854 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:30:09,857 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 08:30:09,859 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: 974421ff)
2025-07-10 08:30:10,312 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 08:30:10,317 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:30:10,320 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 08:30:10,322 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: cccc828a)
2025-07-10 08:30:10,326 - src.agent - WARNING - security.py:77 - Dangerous pattern detected: (union|select|insert|update|delete|drop|create|alter)\s+
2025-07-10 08:30:10,329 - src.agent - ERROR - agent.py:154 - Code generation failed: Input contains potentially dangerous content
2025-07-10 08:30:10,842 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 08:30:10,846 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:30:10,848 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 08:30:10,851 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: 08ee945c)
2025-07-10 08:30:10,857 - src.agent - WARNING - security.py:77 - Dangerous pattern detected: (union|select|insert|update|delete|drop|create|alter)\s+
2025-07-10 08:30:10,860 - src.agent - ERROR - agent.py:154 - Code generation failed: Input contains potentially dangerous content
2025-07-10 08:30:11,363 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 08:30:11,369 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:30:11,372 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 08:30:11,375 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: 343879d5)
2025-07-10 08:30:11,379 - src.agent - WARNING - security.py:77 - Dangerous pattern detected: (union|select|insert|update|delete|drop|create|alter)\s+
2025-07-10 08:30:11,382 - src.agent - ERROR - agent.py:154 - Code generation failed: Input contains potentially dangerous content
2025-07-10 08:30:11,890 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 08:30:11,895 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:30:11,898 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 08:30:11,900 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: efb433b6)
2025-07-10 08:30:11,905 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 08:30:11,908 - src.agent - INFO - validators.py:76 - Python code validation passed with 1 warnings
2025-07-10 08:30:11,911 - src.agent - INFO - validators.py:163 - JavaScript validation completed: passed
2025-07-10 08:30:11,915 - src.agent - INFO - validators.py:98 - HTML validation completed
2025-07-10 08:30:12,367 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 08:30:12,373 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:30:12,376 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 08:30:12,379 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: 46b962e4)
2025-07-10 08:30:12,384 - src.agent - INFO - security.py:322 - SECURITY: code_generation_request - {'task_length': 16, 'language': 'python', 'session_id': '46b962e4-6c49-4c64-a73b-a6d14d5a863c'}
2025-07-10 08:30:12,388 - src.agent - INFO - agent.py:88 - Generating python code for: My name is Alice
2025-07-10 08:30:12,391 - src.agent - INFO - conversation.py:176 - Created new conversation: 31870cc0-c306-4159-af26-05a6112ee225 - New Conversation
2025-07-10 08:30:12,394 - src.agent - INFO - conversation.py:205 - Added user message to conversation 31870cc0-c306-4159-af26-05a6112ee225
2025-07-10 08:30:12,400 - src.agent - INFO - models.py:178 - Generating response with phi3:mini for role: backend
2025-07-10 08:30:49,634 - src.agent - INFO - models.py:198 - Response generated in 37.23s, length: 312 chars
2025-07-10 08:30:49,680 - src.agent - WARNING - validators.py:61 - Syntax validation failed: Syntax Error: invalid syntax (<unknown>, line 1)
2025-07-10 08:30:49,684 - src.agent - WARNING - agent.py:216 - Code validation failed for python: ['Syntax Error: invalid syntax (<unknown>, line 1)']
2025-07-10 08:30:49,727 - src.agent - WARNING - monitoring.py:164 - High CPU usage: 100.0%
2025-07-10 08:30:49,731 - src.agent - INFO - conversation.py:205 - Added assistant message to conversation 31870cc0-c306-4159-af26-05a6112ee225
2025-07-10 08:30:49,739 - src.agent - INFO - security.py:322 - SECURITY: code_generation_request - {'task_length': 16, 'language': 'python', 'session_id': '46b962e4-6c49-4c64-a73b-a6d14d5a863c'}
2025-07-10 08:30:49,745 - src.agent - INFO - agent.py:88 - Generating python code for: What is my name?
2025-07-10 08:30:49,757 - src.agent - INFO - conversation.py:205 - Added user message to conversation 31870cc0-c306-4159-af26-05a6112ee225
2025-07-10 08:30:49,764 - src.agent - INFO - models.py:178 - Generating response with phi3:mini for role: backend
2025-07-10 08:31:47,178 - src.agent - INFO - models.py:198 - Response generated in 57.41s, length: 558 chars
2025-07-10 08:31:47,231 - src.agent - WARNING - validators.py:61 - Syntax validation failed: Syntax Error: unterminated string literal (detected at line 1) (<unknown>, line 1)
2025-07-10 08:31:47,236 - src.agent - WARNING - agent.py:216 - Code validation failed for python: ['Syntax Error: unterminated string literal (detected at line 1) (<unknown>, line 1)']
2025-07-10 08:31:47,276 - src.agent - INFO - conversation.py:205 - Added assistant message to conversation 31870cc0-c306-4159-af26-05a6112ee225
2025-07-10 08:31:47,813 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 08:31:47,822 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:31:47,828 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 08:31:47,833 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: 6587ad2b)
2025-07-10 08:31:47,841 - src.agent - INFO - security.py:322 - SECURITY: code_generation_request - {'task_length': 0, 'language': 'python', 'session_id': '6587ad2b-8429-4425-8dca-b8e6b7c6658a'}
2025-07-10 08:31:47,845 - src.agent - INFO - agent.py:88 - Generating python code for: 
2025-07-10 08:31:47,848 - src.agent - ERROR - agent.py:154 - Code generation failed: Task description cannot be empty
2025-07-10 08:31:47,851 - src.agent - WARNING - security.py:77 - Dangerous pattern detected: (union|select|insert|update|delete|drop|create|alter)\s+
2025-07-10 08:31:47,858 - src.agent - ERROR - agent.py:154 - Code generation failed: Input contains potentially dangerous content
2025-07-10 08:31:48,389 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 08:31:48,393 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:31:48,396 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 08:31:48,398 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: 2a77f319)
2025-07-10 08:31:48,403 - src.agent - INFO - conversation.py:176 - Created new conversation: bc999670-04a0-46e9-8278-34d7f60d4243 - Test
2025-07-10 08:31:48,406 - src.agent - INFO - conversation.py:205 - Added user message to conversation bc999670-04a0-46e9-8278-34d7f60d4243
2025-07-10 08:31:48,410 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 08:31:48,413 - src.agent - INFO - validators.py:76 - Python code validation passed with 1 warnings
2025-07-10 08:31:48,832 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 08:31:48,837 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:31:48,840 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 08:31:48,843 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: 66eadcb5)
2025-07-10 08:31:48,847 - src.agent - WARNING - security.py:77 - Dangerous pattern detected: (union|select|insert|update|delete|drop|create|alter)\s+
2025-07-10 08:31:48,850 - src.agent - ERROR - agent.py:154 - Code generation failed: Input contains potentially dangerous content
2025-07-10 08:31:49,330 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 08:31:49,334 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:31:49,339 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 08:31:49,343 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: 2f7cf745)
2025-07-10 08:31:49,349 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 08:31:49,353 - src.agent - INFO - validators.py:76 - Python code validation passed with 1 warnings
2025-07-10 08:31:49,774 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 08:31:49,778 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:31:49,781 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 08:31:49,783 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: d047ce3f)
2025-07-10 08:31:49,789 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 08:31:49,791 - src.agent - WARNING - validators.py:78 - Python code validation failed with 1 errors
2025-07-10 08:31:49,794 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 08:31:49,796 - src.agent - WARNING - validators.py:78 - Python code validation failed with 1 errors
2025-07-10 08:31:49,799 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 08:31:49,802 - src.agent - WARNING - validators.py:78 - Python code validation failed with 1 errors
2025-07-10 08:31:49,805 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 08:31:49,808 - src.agent - WARNING - validators.py:78 - Python code validation failed with 1 errors
2025-07-10 08:31:50,237 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 08:31:50,240 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:31:50,243 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 08:31:50,245 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: badbfc45)
2025-07-10 08:31:50,249 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 08:31:50,252 - src.agent - INFO - validators.py:76 - Python code validation passed with 0 warnings
2025-07-10 08:31:50,255 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 08:31:50,258 - src.agent - INFO - validators.py:76 - Python code validation passed with 1 warnings
2025-07-10 08:31:50,261 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 08:31:50,264 - src.agent - INFO - validators.py:76 - Python code validation passed with 0 warnings
2025-07-10 08:31:50,267 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 08:31:50,271 - src.agent - INFO - validators.py:76 - Python code validation passed with 1 warnings
2025-07-10 08:31:50,705 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 08:31:51,131 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 08:31:51,562 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 08:31:51,983 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 08:31:54,058 - src.agent - INFO - models.py:99 - Ollama connection successful. Available models: 4
2025-07-10 08:31:54,066 - src.agent - INFO - models.py:122 - Available models: ["model='mistral:7b-instruct-q4_0' modified_at=datetime.datetime(2025, 7, 9, 23, 36, 27, 204717, tzinfo=TzInfo(-06:00)) digest='b17615239298ea5bacfc1c46aa1842737b833779c805542b78f6be29c516d2f4' size=4109865192 details=ModelDetails(parent_model='', format='gguf', family='llama', families=['llama'], parameter_size='7B', quantization_level='Q4_0')", "model='deepseek-coder:1.3b' modified_at=datetime.datetime(2025, 7, 9, 20, 29, 52, 158629, tzinfo=TzInfo(-06:00)) digest='3ddd2d3fc8d2b5fe039d18f859271132fd9c7960ef0be1864984442dc2a915d3' size=776080839 details=ModelDetails(parent_model='', format='gguf', family='llama', families=['llama'], parameter_size='1B', quantization_level='Q4_0')", "model='codellama:7b-code' modified_at=datetime.datetime(2025, 7, 9, 18, 11, 50, 881518, tzinfo=TzInfo(-06:00)) digest='8df0a30bb1e64c5796626dd2b67b538645a3e94f53eba44be125466cd19a38af' size=3825910537 details=ModelDetails(parent_model='', format='gguf', family='llama', families=None, parameter_size='7B', quantization_level='Q4_0')", "model='phi3:mini' modified_at=datetime.datetime(2025, 7, 9, 15, 59, 44, 944896, tzinfo=TzInfo(-06:00)) digest='4f222292793889a9a40a020799cfd28d53f3e01af25d48e06c5e708610fc47e9' size=2176178913 details=ModelDetails(parent_model='', format='gguf', family='phi3', families=['phi3'], parameter_size='3.8B', quantization_level='Q4_0')"]
2025-07-10 08:31:54,547 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 08:31:56,573 - src.agent - INFO - models.py:99 - Ollama connection successful. Available models: 4
2025-07-10 08:31:56,576 - src.agent - INFO - models.py:178 - Generating response with phi3:mini for role: assistant
2025-07-10 08:32:18,136 - src.agent - INFO - models.py:198 - Response generated in 21.56s, length: 398 chars
2025-07-10 08:32:18,607 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 08:32:18,610 - src.agent - INFO - models.py:178 - Generating response with phi3:mini for role: assistant
2025-07-10 08:32:20,651 - src.agent - INFO - models.py:198 - Response generated in 2.04s, length: 0 chars
2025-07-10 08:32:20,753 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:32:20,757 - src.agent - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 08:32:20,826 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:32:20,830 - src.agent - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 08:32:21,020 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:32:21,023 - src.agent - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 08:32:21,105 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:32:21,108 - src.agent - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 08:32:21,328 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:32:21,331 - src.agent - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 08:32:24,259 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:32:24,262 - src.agent - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 08:32:26,365 - src.agent - WARNING - monitoring.py:327 - Circuit breaker opened after 3 failures
2025-07-10 08:32:26,375 - src.agent - WARNING - monitoring.py:327 - Circuit breaker opened after 3 failures
2025-07-10 08:32:27,479 - src.agent - INFO - monitoring.py:297 - Circuit breaker attempting reset
2025-07-10 08:32:27,563 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:32:27,567 - src.agent - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 08:32:27,702 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:32:27,772 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:32:27,776 - src.agent - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 08:32:27,842 - src.agent - INFO - database.py:144 - Database schema initialized successfully
2025-07-10 08:32:27,846 - src.agent - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 08:32:27,850 - src.agent - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 08:32:27,857 - src.agent - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 08:32:27,925 - src.agent - WARNING - monitoring.py:154 - Performance threshold exceeded: test_operation took 62.23ms (threshold: 50ms)
2025-07-10 08:32:27,932 - src.agent - WARNING - security.py:77 - Dangerous pattern detected: (union|select|insert|update|delete|drop|create|alter)\s+
2025-07-10 08:32:27,982 - src.agent - WARNING - security.py:77 - Dangerous pattern detected: <script[^>]*>.*?</script>
2025-07-10 08:32:28,095 - src.agent - WARNING - security.py:270 - Rate limit exceeded for client: test_client
2025-07-10 08:32:28,109 - src.agent - WARNING - security.py:270 - Rate limit exceeded for client: client_1
2025-07-10 08:32:28,113 - src.agent - WARNING - security.py:270 - Rate limit exceeded for client: client_2
2025-07-10 08:32:28,122 - src.agent - INFO - security.py:322 - SECURITY: test_event - {'detail': 'test_detail'}
2025-07-10 08:32:28,126 - src.agent - CRITICAL - security.py:316 - SECURITY: critical_event - {'detail': 'critical_detail'}
2025-07-10 08:32:28,156 - src.agent - WARNING - security.py:77 - Dangerous pattern detected: (eval|exec|system|popen|subprocess)\s*\(
2025-07-10 08:32:28,161 - src.agent - CRITICAL - security.py:316 - SECURITY: dangerous_code_detected - {'code': "import os; os.system('rm -rf /')", 'issues': ['Blocked module import: os']}
2025-07-10 08:55:25,045 - src.security - WARNING - security.py:81 - Dangerous pattern detected: ;\s*(drop|delete)\s+(table|database|from)
2025-07-10 08:55:25,053 - src.security - WARNING - security.py:81 - Dangerous pattern detected: \.\./+(etc|windows|system32)
2025-07-10 08:56:10,439 - src.security - WARNING - security.py:81 - Dangerous pattern detected: (rm|del|format|shutdown|reboot|kill|pkill)\s+.*?/
2025-07-10 08:56:10,445 - src.security - WARNING - security.py:81 - Dangerous pattern detected: ;\s*(drop|delete)\s+(table|database|from)
2025-07-10 08:56:10,447 - src.security - WARNING - security.py:81 - Dangerous pattern detected: (eval|exec)\s*\(
2025-07-10 08:56:10,449 - src.security - WARNING - security.py:81 - Dangerous pattern detected: \.\./+(etc|windows|system32)
2025-07-10 08:56:50,007 - src.security - WARNING - security.py:81 - Dangerous pattern detected: (rm|del|format|shutdown|reboot|kill|pkill)\s+.*?/
2025-07-10 08:56:50,012 - src.security - WARNING - security.py:81 - Dangerous pattern detected: ;\s*(drop|delete)\s+(table|database|from)
2025-07-10 08:56:50,015 - src.security - WARNING - security.py:81 - Dangerous pattern detected: (eval|exec)\s*\(
2025-07-10 08:56:50,017 - src.security - WARNING - security.py:81 - Dangerous pattern detected: \.\./+(etc|windows|system32)
2025-07-10 08:56:50,021 - src.security - WARNING - security.py:81 - Dangerous pattern detected: (system|popen|subprocess)\s*[\.\(]
2025-07-10 08:57:07,705 - src.security - WARNING - security.py:81 - Dangerous pattern detected: (rm|del|format|shutdown|reboot|kill|pkill)\s+.*?/
2025-07-10 08:57:07,712 - src.security - WARNING - security.py:81 - Dangerous pattern detected: ;\s*(drop|delete)\s+(table|database|from)
2025-07-10 08:57:07,718 - src.security - WARNING - security.py:81 - Dangerous pattern detected: (eval|exec)\s*\(
2025-07-10 08:57:07,720 - src.security - WARNING - security.py:81 - Dangerous pattern detected: \.\./+(etc|windows|system32)
2025-07-10 08:57:07,722 - src.security - WARNING - security.py:81 - Dangerous pattern detected: (system|popen|subprocess)\s*[\.\(]
2025-07-10 08:57:27,061 - src.security - WARNING - security.py:81 - Dangerous pattern detected: <script[^>]*>.*?</script>
2025-07-10 09:00:07,984 - src.security - WARNING - security.py:276 - Rate limit exceeded for client: integration_test_client
2025-07-10 09:00:07,994 - src.security - INFO - security.py:328 - SECURITY: code_execution_request - {'client_id': 'integration_test_client', 'code_length': 35, 'is_safe': True}
2025-07-10 09:00:32,247 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 09:00:32,266 - src.agent - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 09:00:32,271 - src.agent - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 09:00:32,277 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 09:00:32,280 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: e69a5458)
2025-07-10 09:00:32,711 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 09:00:32,715 - src.agent - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 09:00:32,718 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 09:00:32,721 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: 9c1ed2c6)
2025-07-10 09:00:32,724 - src.agent - INFO - security.py:328 - SECURITY: code_generation_request - {'task_length': 29, 'language': 'python', 'session_id': '9c1ed2c6-e8b3-44f6-9d86-342d8d1e4594'}
2025-07-10 09:00:32,728 - src.agent - INFO - agent.py:88 - Generating python code for: create a hello world function
2025-07-10 09:00:32,730 - src.agent - INFO - conversation.py:176 - Created new conversation: eed59526-3e2c-4f3a-8f27-f42c92998bbf - New Conversation
2025-07-10 09:00:32,733 - src.agent - INFO - conversation.py:205 - Added user message to conversation eed59526-3e2c-4f3a-8f27-f42c92998bbf
2025-07-10 09:00:32,739 - src.agent - INFO - models.py:178 - Generating response with phi3:mini for role: backend
2025-07-10 09:01:51,172 - src.agent - INFO - models.py:198 - Response generated in 78.43s, length: 1213 chars
2025-07-10 09:01:51,224 - src.agent - WARNING - validators.py:61 - Syntax validation failed: Syntax Error: invalid syntax (<unknown>, line 1)
2025-07-10 09:01:51,228 - src.agent - WARNING - agent.py:216 - Code validation failed for python: ['Syntax Error: invalid syntax (<unknown>, line 1)']
2025-07-10 09:01:51,277 - src.agent - INFO - conversation.py:205 - Added assistant message to conversation eed59526-3e2c-4f3a-8f27-f42c92998bbf
2025-07-10 09:01:51,280 - src.agent - INFO - security.py:328 - SECURITY: code_generation_request - {'task_length': 29, 'language': 'javascript', 'session_id': '9c1ed2c6-e8b3-44f6-9d86-342d8d1e4594'}
2025-07-10 09:01:51,285 - src.agent - INFO - agent.py:88 - Generating javascript code for: create a hello world function
2025-07-10 09:01:51,291 - src.agent - INFO - conversation.py:205 - Added user message to conversation eed59526-3e2c-4f3a-8f27-f42c92998bbf
2025-07-10 09:01:51,299 - src.agent - INFO - models.py:178 - Generating response with phi3:mini for role: frontend
2025-07-10 09:03:33,696 - src.agent - INFO - models.py:83 - Ollama client initialized with host: http://localhost:11434
2025-07-10 09:03:33,707 - src.agent - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 09:03:33,711 - src.agent - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 09:03:33,714 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 09:03:33,715 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: 7ccc1af3)
2025-07-10 09:03:33,717 - src.agent - INFO - security.py:328 - SECURITY: code_generation_request - {'task_length': 29, 'language': 'python', 'session_id': '7ccc1af3-083b-4803-b55e-9b2676e3c949'}
2025-07-10 09:03:33,720 - src.agent - INFO - agent.py:88 - Generating python code for: create a hello world function
2025-07-10 09:03:33,722 - src.agent - INFO - conversation.py:176 - Created new conversation: 63a701b9-a87a-4676-8824-b0844c9c83db - New Conversation
2025-07-10 09:03:33,723 - src.agent - INFO - conversation.py:205 - Added user message to conversation 63a701b9-a87a-4676-8824-b0844c9c83db
2025-07-10 09:03:33,726 - src.agent - INFO - models.py:178 - Generating response with phi3:mini for role: backend
2025-07-10 09:04:54,610 - src.security - WARNING - security.py:80 - Dangerous pattern detected: (rm|del|format|shutdown|reboot|kill|pkill)\s+.*?/
2025-07-10 09:04:54,616 - src.security - WARNING - security.py:80 - Dangerous pattern detected: ;\s*(drop|delete)\s+(table|database|from)
2025-07-10 09:04:54,622 - src.security - WARNING - security.py:80 - Dangerous pattern detected: (eval|exec)\s*\(
2025-07-10 09:04:54,624 - src.security - WARNING - security.py:80 - Dangerous pattern detected: \.\./+(etc|windows|system32)
2025-07-10 09:04:54,628 - src.security - WARNING - security.py:80 - Dangerous pattern detected: (system|popen|subprocess)\s*[\.\(]
2025-07-10 09:04:54,756 - src.security - WARNING - security.py:276 - Rate limit exceeded for client: test_client
2025-07-10 09:04:54,763 - src.security - WARNING - security.py:276 - Rate limit exceeded for client: client_1
2025-07-10 09:04:54,764 - src.security - WARNING - security.py:276 - Rate limit exceeded for client: client_2
2025-07-10 09:04:54,774 - src.security - INFO - security.py:328 - SECURITY: test_event - {'detail': 'test_detail'}
2025-07-10 09:04:54,776 - src.security - CRITICAL - security.py:322 - SECURITY: critical_event - {'detail': 'critical_detail'}
2025-07-10 09:04:54,780 - src.security - WARNING - security.py:276 - Rate limit exceeded for client: integration_test_client
2025-07-10 09:04:54,783 - src.security - INFO - security.py:328 - SECURITY: code_execution_request - {'client_id': 'integration_test_client', 'code_length': 35, 'is_safe': True}
2025-07-10 09:04:54,790 - src.security - WARNING - security.py:80 - Dangerous pattern detected: (rm|del|format|shutdown|reboot|kill|pkill)\s+.*?/
2025-07-10 09:04:54,792 - src.security - CRITICAL - security.py:322 - SECURITY: dangerous_code_detected - {'code': "import os; os.system('rm -rf /')", 'issues': ['Blocked module import: os']}
2025-07-10 09:07:27,651 - src.agent - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 09:07:27,669 - src.agent - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 09:07:27,672 - src.agent - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 09:07:27,679 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 09:07:27,683 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: 840c768a)
2025-07-10 09:07:28,135 - src.agent - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 09:07:28,140 - src.agent - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 09:07:28,143 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 09:07:28,145 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: ba4522cb)
2025-07-10 09:07:28,148 - src.agent - INFO - security.py:328 - SECURITY: code_generation_request - {'task_length': 29, 'language': 'python', 'session_id': 'ba4522cb-41bc-48fb-85f4-791726ff1418'}
2025-07-10 09:07:28,151 - src.agent - INFO - agent.py:88 - Generating python code for: create a hello world function
2025-07-10 09:07:28,155 - src.agent - INFO - conversation.py:176 - Created new conversation: b4715b8f-66e0-4bbd-bbb6-a59c33e872a1 - New Conversation
2025-07-10 09:07:28,158 - src.agent - INFO - conversation.py:205 - Added user message to conversation b4715b8f-66e0-4bbd-bbb6-a59c33e872a1
2025-07-10 09:07:28,163 - src.agent - INFO - models.py:203 - Generating response with phi3:mini for role: backend
2025-07-10 09:08:22,829 - src.agent - INFO - models.py:223 - Response generated in 54.66s, length: 994 chars
2025-07-10 09:08:22,881 - src.agent - WARNING - validators.py:61 - Syntax validation failed: Syntax Error: unterminated string literal (detected at line 17) (<unknown>, line 17)
2025-07-10 09:08:22,887 - src.agent - WARNING - agent.py:216 - Code validation failed for python: ['Syntax Error: unterminated string literal (detected at line 17) (<unknown>, line 17)']
2025-07-10 09:08:22,940 - src.agent - INFO - conversation.py:205 - Added assistant message to conversation b4715b8f-66e0-4bbd-bbb6-a59c33e872a1
2025-07-10 09:08:22,943 - src.agent - INFO - security.py:328 - SECURITY: code_generation_request - {'task_length': 29, 'language': 'javascript', 'session_id': 'ba4522cb-41bc-48fb-85f4-791726ff1418'}
2025-07-10 09:08:22,947 - src.agent - INFO - agent.py:88 - Generating javascript code for: create a hello world function
2025-07-10 09:08:22,949 - src.agent - INFO - conversation.py:205 - Added user message to conversation b4715b8f-66e0-4bbd-bbb6-a59c33e872a1
2025-07-10 09:08:22,954 - src.agent - INFO - models.py:203 - Generating response with phi3:mini for role: frontend
2025-07-10 09:10:29,211 - src.agent - INFO - models.py:223 - Response generated in 126.25s, length: 1523 chars
2025-07-10 09:10:29,269 - src.agent - INFO - validators.py:163 - JavaScript validation completed: passed
2025-07-10 09:10:29,272 - src.agent - INFO - agent.py:210 - Code validation passed for javascript
2025-07-10 09:10:29,319 - src.agent - INFO - conversation.py:205 - Added assistant message to conversation b4715b8f-66e0-4bbd-bbb6-a59c33e872a1
2025-07-10 09:10:29,749 - src.agent - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 09:10:29,754 - src.agent - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 09:10:29,757 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 09:10:29,763 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: 6317d24b)
2025-07-10 09:10:30,209 - src.agent - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 09:10:30,213 - src.agent - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 09:10:30,216 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 09:10:30,220 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: c4d166df)
2025-07-10 09:10:30,299 - src.agent - INFO - conversation.py:176 - Created new conversation: d6b0b5b3-4f77-4a85-80eb-d8c7e24d704c - Test Conversation
2025-07-10 09:10:30,312 - src.agent - INFO - conversation.py:176 - Created new conversation: 8729f982-f237-4ca6-8879-8c32ec42eedf - First
2025-07-10 09:10:30,324 - src.agent - INFO - conversation.py:176 - Created new conversation: 604c5b02-5b53-42bc-b5aa-4a0460515f76 - Second
2025-07-10 09:10:30,330 - src.agent - INFO - conversation.py:193 - Switched to conversation: 8729f982-f237-4ca6-8879-8c32ec42eedf
2025-07-10 09:10:30,343 - src.agent - INFO - conversation.py:176 - Created new conversation: 47048fcc-881b-4d3d-8efd-dc028dac2b9f - New Conversation
2025-07-10 09:10:30,350 - src.agent - INFO - conversation.py:205 - Added user message to conversation 47048fcc-881b-4d3d-8efd-dc028dac2b9f
2025-07-10 09:10:30,367 - src.agent - INFO - conversation.py:176 - Created new conversation: 190dd0f7-0ea9-4e7e-a135-97d598f29e37 - First
2025-07-10 09:10:30,376 - src.agent - INFO - conversation.py:176 - Created new conversation: 5bd35bcc-5a9a-46c1-a563-c14766d39095 - Second
2025-07-10 09:10:30,379 - src.agent - INFO - conversation.py:193 - Switched to conversation: 190dd0f7-0ea9-4e7e-a135-97d598f29e37
2025-07-10 09:10:30,383 - src.agent - INFO - conversation.py:205 - Added user message to conversation 190dd0f7-0ea9-4e7e-a135-97d598f29e37
2025-07-10 09:10:30,393 - src.agent - INFO - conversation.py:176 - Created new conversation: 8a4106f3-dcea-453f-8784-ecffba7fd661 - To Delete
2025-07-10 09:10:30,396 - src.agent - INFO - conversation.py:233 - Deleted conversation: 8a4106f3-dcea-453f-8784-ecffba7fd661
2025-07-10 09:10:30,556 - src.agent - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 09:10:30,719 - src.agent - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 09:10:30,743 - src.agent - INFO - database.py:192 - Created conversation: ca9afffd-740a-40f5-ab3d-18e9a8608b33 - Test Conversation
2025-07-10 09:10:30,785 - src.agent - INFO - database.py:255 - Deleted conversation: ca9afffd-740a-40f5-ab3d-18e9a8608b33
2025-07-10 09:10:30,958 - src.agent - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 09:10:30,978 - src.agent - INFO - database.py:192 - Created conversation: 1cf6cf73-2740-4d6b-8e04-9c030f34c43c - Test Conversation
2025-07-10 09:10:30,994 - src.agent - DEBUG - database.py:299 - Added user message to conversation 1cf6cf73-2740-4d6b-8e04-9c030f34c43c
2025-07-10 09:10:31,020 - src.agent - DEBUG - database.py:299 - Added assistant message to conversation 1cf6cf73-2740-4d6b-8e04-9c030f34c43c
2025-07-10 09:10:31,212 - src.agent - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 09:10:31,237 - src.agent - INFO - database.py:192 - Created conversation: bfbf73c5-520a-4fe4-8994-37ee6a395aae - Test Conversation
2025-07-10 09:10:31,254 - src.agent - INFO - database.py:371 - Saved code generation: python - 22 chars
2025-07-10 09:10:31,423 - src.agent - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 09:10:31,439 - src.agent - INFO - database.py:192 - Created conversation: 4e2e7b76-f964-404a-a60e-b0100bad0702 - Test Conversation
2025-07-10 09:10:31,458 - src.agent - INFO - database.py:427 - Saved project: Test Project
2025-07-10 09:10:31,635 - src.agent - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 09:10:31,812 - src.agent - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 09:10:32,255 - src.agent - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 09:10:32,260 - src.agent - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 09:10:32,262 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 09:10:32,265 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: bf72a5b7)
2025-07-10 09:10:32,697 - src.agent - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 09:10:32,701 - src.agent - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 09:10:32,705 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 09:10:32,707 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: cb3bf425)
2025-07-10 09:10:32,711 - src.agent - INFO - security.py:328 - SECURITY: code_generation_request - {'task_length': 36, 'language': 'python', 'session_id': 'cb3bf425-8787-4213-b60c-70a8976e2fce'}
2025-07-10 09:10:32,714 - src.agent - INFO - agent.py:88 - Generating python code for: create a simple hello world function
2025-07-10 09:10:32,716 - src.agent - INFO - conversation.py:176 - Created new conversation: d6176eed-4c38-4473-9757-5b9a608291ba - New Conversation
2025-07-10 09:10:32,720 - src.agent - INFO - conversation.py:205 - Added user message to conversation d6176eed-4c38-4473-9757-5b9a608291ba
2025-07-10 09:10:32,725 - src.agent - INFO - models.py:203 - Generating response with phi3:mini for role: backend
2025-07-10 09:10:59,294 - src.agent - INFO - models.py:223 - Response generated in 26.57s, length: 378 chars
2025-07-10 09:10:59,338 - src.agent - WARNING - validators.py:61 - Syntax validation failed: Syntax Error: invalid syntax (<unknown>, line 1)
2025-07-10 09:10:59,341 - src.agent - WARNING - agent.py:216 - Code validation failed for python: ['Syntax Error: invalid syntax (<unknown>, line 1)']
2025-07-10 09:10:59,380 - src.agent - INFO - conversation.py:205 - Added assistant message to conversation d6176eed-4c38-4473-9757-5b9a608291ba
2025-07-10 09:10:59,807 - src.agent - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 09:10:59,811 - src.agent - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 09:10:59,814 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 09:10:59,817 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: 57bde5b4)
2025-07-10 09:10:59,822 - src.agent - INFO - security.py:328 - SECURITY: code_generation_request - {'task_length': 39, 'language': 'python', 'session_id': '57bde5b4-ffab-4490-876e-fc8a6c9b024e'}
2025-07-10 09:10:59,826 - src.agent - INFO - agent.py:88 - Generating python code for: create a function that adds two numbers
2025-07-10 09:10:59,829 - src.agent - INFO - conversation.py:176 - Created new conversation: 2793967f-75cf-4321-8bcb-454362f87725 - New Conversation
2025-07-10 09:10:59,832 - src.agent - INFO - conversation.py:205 - Added user message to conversation 2793967f-75cf-4321-8bcb-454362f87725
2025-07-10 09:10:59,838 - src.agent - INFO - models.py:203 - Generating response with phi3:mini for role: backend
2025-07-10 09:11:58,039 - src.agent - INFO - models.py:223 - Response generated in 58.20s, length: 780 chars
2025-07-10 09:11:58,086 - src.agent - WARNING - validators.py:61 - Syntax validation failed: Syntax Error: unterminated string literal (detected at line 16) (<unknown>, line 16)
2025-07-10 09:11:58,091 - src.agent - WARNING - agent.py:216 - Code validation failed for python: ['Syntax Error: unterminated string literal (detected at line 16) (<unknown>, line 16)']
2025-07-10 09:11:58,181 - src.agent - INFO - conversation.py:205 - Added assistant message to conversation 2793967f-75cf-4321-8bcb-454362f87725
2025-07-10 09:11:58,188 - src.agent - INFO - security.py:328 - SECURITY: code_generation_request - {'task_length': 49, 'language': 'python', 'session_id': '57bde5b4-ffab-4490-876e-fc8a6c9b024e'}
2025-07-10 09:11:58,192 - src.agent - INFO - agent.py:88 - Generating python code for: now create a function that multiplies two numbers
2025-07-10 09:11:58,195 - src.agent - INFO - conversation.py:205 - Added user message to conversation 2793967f-75cf-4321-8bcb-454362f87725
2025-07-10 09:11:58,200 - src.agent - INFO - models.py:203 - Generating response with phi3:mini for role: backend
2025-07-10 09:13:28,185 - src.agent - INFO - models.py:223 - Response generated in 89.98s, length: 869 chars
2025-07-10 09:13:28,259 - src.agent - WARNING - validators.py:61 - Syntax validation failed: Syntax Error: unterminated string literal (detected at line 16) (<unknown>, line 16)
2025-07-10 09:13:28,273 - src.agent - WARNING - agent.py:216 - Code validation failed for python: ['Syntax Error: unterminated string literal (detected at line 16) (<unknown>, line 16)']
2025-07-10 09:13:28,340 - src.agent - INFO - conversation.py:205 - Added assistant message to conversation 2793967f-75cf-4321-8bcb-454362f87725
2025-07-10 09:13:28,766 - src.agent - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 09:13:28,772 - src.agent - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 09:13:28,774 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 09:13:28,777 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: c408b7ef)
2025-07-10 09:13:28,781 - src.agent - INFO - security.py:328 - SECURITY: code_generation_request - {'task_length': 22, 'language': 'python', 'session_id': 'c408b7ef-a998-44f3-b0da-8246554ba14d'}
2025-07-10 09:13:28,786 - src.agent - INFO - agent.py:88 - Generating python code for: create a test function
2025-07-10 09:13:28,790 - src.agent - INFO - conversation.py:176 - Created new conversation: 2cb4149a-c2e3-4bd6-9a0f-c7ea2c5f2aa6 - New Conversation
2025-07-10 09:13:28,795 - src.agent - INFO - conversation.py:205 - Added user message to conversation 2cb4149a-c2e3-4bd6-9a0f-c7ea2c5f2aa6
2025-07-10 09:13:28,800 - src.agent - INFO - models.py:203 - Generating response with phi3:mini for role: backend
2025-07-10 09:16:42,964 - src.agent - INFO - models.py:223 - Response generated in 194.16s, length: 3154 chars
2025-07-10 09:16:43,039 - src.agent - WARNING - validators.py:61 - Syntax validation failed: Syntax Error: unterminated string literal (detected at line 42) (<unknown>, line 42)
2025-07-10 09:16:43,043 - src.agent - WARNING - agent.py:216 - Code validation failed for python: ['Syntax Error: unterminated string literal (detected at line 42) (<unknown>, line 42)']
2025-07-10 09:16:43,111 - src.agent - INFO - conversation.py:205 - Added assistant message to conversation 2cb4149a-c2e3-4bd6-9a0f-c7ea2c5f2aa6
2025-07-10 09:16:43,140 - src.agent - INFO - database.py:192 - Created conversation: 2cb4149a-c2e3-4bd6-9a0f-c7ea2c5f2aa6 - create a test function
2025-07-10 09:16:43,206 - src.agent - DEBUG - database.py:299 - Added user message to conversation 2cb4149a-c2e3-4bd6-9a0f-c7ea2c5f2aa6
2025-07-10 09:16:43,281 - src.agent - DEBUG - database.py:299 - Added assistant message to conversation 2cb4149a-c2e3-4bd6-9a0f-c7ea2c5f2aa6
2025-07-10 09:16:43,708 - src.agent - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 09:16:43,712 - src.agent - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 09:16:43,715 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 09:16:43,717 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: ddabcff4)
2025-07-10 09:16:43,723 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 09:16:43,726 - src.agent - INFO - validators.py:76 - Python code validation passed with 1 warnings
2025-07-10 09:16:43,729 - src.agent - INFO - validators.py:163 - JavaScript validation completed: passed
2025-07-10 09:16:43,731 - src.agent - INFO - validators.py:98 - HTML validation completed
2025-07-10 09:16:44,198 - src.agent - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 09:16:44,203 - src.agent - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 09:16:44,206 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 09:16:44,208 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: d921a9f2)
2025-07-10 09:16:44,212 - src.agent - INFO - security.py:328 - SECURITY: code_generation_request - {'task_length': 16, 'language': 'python', 'session_id': 'd921a9f2-6542-4952-a71d-97871cc35cff'}
2025-07-10 09:16:44,215 - src.agent - INFO - agent.py:88 - Generating python code for: My name is Alice
2025-07-10 09:16:44,218 - src.agent - INFO - conversation.py:176 - Created new conversation: 6866f279-b9b4-4e5d-9137-18d7bc7ec78d - New Conversation
2025-07-10 09:16:44,222 - src.agent - INFO - conversation.py:205 - Added user message to conversation 6866f279-b9b4-4e5d-9137-18d7bc7ec78d
2025-07-10 09:16:44,226 - src.agent - INFO - models.py:203 - Generating response with phi3:mini for role: backend
2025-07-10 09:17:22,998 - src.agent - INFO - models.py:223 - Response generated in 38.77s, length: 512 chars
2025-07-10 09:17:23,071 - src.agent - WARNING - validators.py:61 - Syntax validation failed: Syntax Error: invalid syntax (<unknown>, line 1)
2025-07-10 09:17:23,075 - src.agent - WARNING - agent.py:216 - Code validation failed for python: ['Syntax Error: invalid syntax (<unknown>, line 1)']
2025-07-10 09:17:23,142 - src.agent - INFO - conversation.py:205 - Added assistant message to conversation 6866f279-b9b4-4e5d-9137-18d7bc7ec78d
2025-07-10 09:17:23,145 - src.agent - INFO - security.py:328 - SECURITY: code_generation_request - {'task_length': 16, 'language': 'python', 'session_id': 'd921a9f2-6542-4952-a71d-97871cc35cff'}
2025-07-10 09:17:23,149 - src.agent - INFO - agent.py:88 - Generating python code for: What is my name?
2025-07-10 09:17:23,151 - src.agent - INFO - conversation.py:205 - Added user message to conversation 6866f279-b9b4-4e5d-9137-18d7bc7ec78d
2025-07-10 09:17:23,158 - src.agent - INFO - models.py:203 - Generating response with phi3:mini for role: backend
2025-07-10 09:18:36,096 - src.agent - INFO - models.py:223 - Response generated in 72.94s, length: 648 chars
2025-07-10 09:18:36,159 - src.agent - WARNING - validators.py:61 - Syntax validation failed: Syntax Error: unterminated string literal (detected at line 1) (<unknown>, line 1)
2025-07-10 09:18:36,162 - src.agent - WARNING - agent.py:216 - Code validation failed for python: ['Syntax Error: unterminated string literal (detected at line 1) (<unknown>, line 1)']
2025-07-10 09:18:36,205 - src.agent - INFO - conversation.py:205 - Added assistant message to conversation 6866f279-b9b4-4e5d-9137-18d7bc7ec78d
2025-07-10 09:18:36,628 - src.agent - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 09:18:36,632 - src.agent - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 09:18:36,635 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 09:18:36,639 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: 9990807f)
2025-07-10 09:18:36,642 - src.agent - INFO - security.py:328 - SECURITY: code_generation_request - {'task_length': 0, 'language': 'python', 'session_id': '9990807f-96d4-4aba-acc1-d36e9dcfadc9'}
2025-07-10 09:18:36,646 - src.agent - INFO - agent.py:88 - Generating python code for: 
2025-07-10 09:18:36,648 - src.agent - ERROR - agent.py:154 - Code generation failed: Task description cannot be empty
2025-07-10 09:18:36,651 - src.agent - INFO - security.py:328 - SECURITY: code_generation_request - {'task_length': 17, 'language': 'invalid_language', 'session_id': '9990807f-96d4-4aba-acc1-d36e9dcfadc9'}
2025-07-10 09:18:36,656 - src.agent - INFO - agent.py:88 - Generating invalid_language code for: create a function
2025-07-10 09:18:36,663 - src.agent - INFO - conversation.py:176 - Created new conversation: a22c517c-1c98-4a04-a2af-cdf26e0b4c5e - New Conversation
2025-07-10 09:18:36,666 - src.agent - INFO - conversation.py:205 - Added user message to conversation a22c517c-1c98-4a04-a2af-cdf26e0b4c5e
2025-07-10 09:18:36,672 - src.agent - INFO - models.py:203 - Generating response with phi3:mini for role: assistant
2025-07-10 09:21:25,039 - src.agent - INFO - models.py:223 - Response generated in 168.36s, length: 2343 chars
2025-07-10 09:21:25,083 - src.agent - INFO - agent.py:206 - Validation not supported for invalid_language, returning code as-is
2025-07-10 09:21:25,131 - src.agent - INFO - conversation.py:205 - Added assistant message to conversation a22c517c-1c98-4a04-a2af-cdf26e0b4c5e
2025-07-10 09:21:25,579 - src.agent - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 09:21:25,583 - src.agent - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 09:21:25,585 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 09:21:25,589 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: d5e4f78c)
2025-07-10 09:21:25,594 - src.agent - INFO - conversation.py:176 - Created new conversation: d91e1c2e-8ea5-45ce-b53d-46b45781a900 - Test
2025-07-10 09:21:25,598 - src.agent - INFO - conversation.py:205 - Added user message to conversation d91e1c2e-8ea5-45ce-b53d-46b45781a900
2025-07-10 09:21:25,601 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 09:21:25,604 - src.agent - INFO - validators.py:76 - Python code validation passed with 1 warnings
2025-07-10 09:21:26,023 - src.agent - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 09:21:26,027 - src.agent - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 09:21:26,030 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 09:21:26,033 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: d73417e3)
2025-07-10 09:21:26,038 - src.agent - INFO - security.py:328 - SECURITY: code_generation_request - {'task_length': 24, 'language': 'python', 'session_id': 'd73417e3-f9f9-4104-b70b-fbc2f6b30aae'}
2025-07-10 09:21:26,041 - src.agent - INFO - agent.py:88 - Generating python code for: create a simple function
2025-07-10 09:21:26,044 - src.agent - INFO - conversation.py:176 - Created new conversation: 2851a123-67c0-4ba5-9e42-f579acd7a8c8 - New Conversation
2025-07-10 09:21:26,046 - src.agent - INFO - conversation.py:205 - Added user message to conversation 2851a123-67c0-4ba5-9e42-f579acd7a8c8
2025-07-10 09:21:26,051 - src.agent - INFO - models.py:203 - Generating response with phi3:mini for role: backend
2025-07-10 09:23:24,914 - src.agent - INFO - models.py:223 - Response generated in 118.86s, length: 2045 chars
2025-07-10 09:23:24,964 - src.agent - WARNING - validators.py:61 - Syntax validation failed: Syntax Error: unterminated string literal (detected at line 25) (<unknown>, line 25)
2025-07-10 09:23:24,967 - src.agent - WARNING - agent.py:216 - Code validation failed for python: ['Syntax Error: unterminated string literal (detected at line 25) (<unknown>, line 25)']
2025-07-10 09:23:25,008 - src.agent - INFO - conversation.py:205 - Added assistant message to conversation 2851a123-67c0-4ba5-9e42-f579acd7a8c8
2025-07-10 09:23:25,584 - src.agent - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 09:23:25,591 - src.agent - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 09:23:25,594 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 09:23:25,596 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: a3737993)
2025-07-10 09:23:25,600 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 09:23:25,602 - src.agent - INFO - validators.py:76 - Python code validation passed with 1 warnings
2025-07-10 09:23:26,047 - src.agent - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 09:23:26,051 - src.agent - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 09:23:26,054 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 09:23:26,057 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: d2e92b89)
2025-07-10 09:23:26,060 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 09:23:26,063 - src.agent - WARNING - validators.py:78 - Python code validation failed with 1 errors
2025-07-10 09:23:26,065 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 09:23:26,067 - src.agent - WARNING - validators.py:78 - Python code validation failed with 1 errors
2025-07-10 09:23:26,072 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 09:23:26,074 - src.agent - WARNING - validators.py:78 - Python code validation failed with 1 errors
2025-07-10 09:23:26,077 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 09:23:26,079 - src.agent - WARNING - validators.py:78 - Python code validation failed with 1 errors
2025-07-10 09:23:26,491 - src.agent - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 09:23:26,496 - src.agent - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 09:23:26,499 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 09:23:26,502 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: c0780f09)
2025-07-10 09:23:26,508 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 09:23:26,510 - src.agent - INFO - validators.py:76 - Python code validation passed with 0 warnings
2025-07-10 09:23:26,513 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 09:23:26,515 - src.agent - INFO - validators.py:76 - Python code validation passed with 1 warnings
2025-07-10 09:23:26,518 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 09:23:26,521 - src.agent - INFO - validators.py:76 - Python code validation passed with 0 warnings
2025-07-10 09:23:26,524 - src.agent - DEBUG - validators.py:57 - Python syntax validation passed
2025-07-10 09:23:26,526 - src.agent - INFO - validators.py:76 - Python code validation passed with 1 warnings
2025-07-10 09:23:26,944 - src.agent - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 09:23:27,368 - src.agent - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 09:23:27,778 - src.agent - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 09:23:28,245 - src.agent - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 09:23:30,275 - src.agent - INFO - models.py:103 - Ollama connection successful. Available models: 4
2025-07-10 09:23:30,282 - src.agent - INFO - models.py:126 - Available models: ["model='mistral:7b-instruct-q4_0' modified_at=datetime.datetime(2025, 7, 9, 23, 36, 27, 204717, tzinfo=TzInfo(-06:00)) digest='b17615239298ea5bacfc1c46aa1842737b833779c805542b78f6be29c516d2f4' size=4109865192 details=ModelDetails(parent_model='', format='gguf', family='llama', families=['llama'], parameter_size='7B', quantization_level='Q4_0')", "model='deepseek-coder:1.3b' modified_at=datetime.datetime(2025, 7, 9, 20, 29, 52, 158629, tzinfo=TzInfo(-06:00)) digest='3ddd2d3fc8d2b5fe039d18f859271132fd9c7960ef0be1864984442dc2a915d3' size=776080839 details=ModelDetails(parent_model='', format='gguf', family='llama', families=['llama'], parameter_size='1B', quantization_level='Q4_0')", "model='codellama:7b-code' modified_at=datetime.datetime(2025, 7, 9, 18, 11, 50, 881518, tzinfo=TzInfo(-06:00)) digest='8df0a30bb1e64c5796626dd2b67b538645a3e94f53eba44be125466cd19a38af' size=3825910537 details=ModelDetails(parent_model='', format='gguf', family='llama', families=None, parameter_size='7B', quantization_level='Q4_0')", "model='phi3:mini' modified_at=datetime.datetime(2025, 7, 9, 15, 59, 44, 944896, tzinfo=TzInfo(-06:00)) digest='4f222292793889a9a40a020799cfd28d53f3e01af25d48e06c5e708610fc47e9' size=2176178913 details=ModelDetails(parent_model='', format='gguf', family='phi3', families=['phi3'], parameter_size='3.8B', quantization_level='Q4_0')"]
2025-07-10 09:23:30,726 - src.agent - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 09:23:32,758 - src.agent - INFO - models.py:103 - Ollama connection successful. Available models: 4
2025-07-10 09:23:32,761 - src.agent - INFO - models.py:203 - Generating response with phi3:mini for role: assistant
2025-07-10 09:23:34,748 - src.agent - INFO - models.py:223 - Response generated in 1.98s, length: 6 chars
2025-07-10 09:23:35,182 - src.agent - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 09:23:35,185 - src.agent - INFO - models.py:203 - Generating response with phi3:mini for role: assistant
2025-07-10 09:23:37,218 - src.agent - INFO - models.py:223 - Response generated in 2.03s, length: 0 chars
2025-07-10 09:23:37,413 - src.agent - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 09:23:37,416 - src.agent - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 09:23:37,584 - src.agent - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 09:23:37,590 - src.agent - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 09:23:37,881 - src.agent - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 09:23:37,884 - src.agent - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 09:23:38,079 - src.agent - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 09:23:38,082 - src.agent - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 09:23:38,419 - src.agent - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 09:23:38,423 - src.agent - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 09:23:41,263 - src.agent - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 09:23:41,266 - src.agent - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 09:23:43,335 - src.agent - WARNING - monitoring.py:327 - Circuit breaker opened after 3 failures
2025-07-10 09:23:43,345 - src.agent - WARNING - monitoring.py:327 - Circuit breaker opened after 3 failures
2025-07-10 09:23:44,449 - src.agent - INFO - monitoring.py:297 - Circuit breaker attempting reset
2025-07-10 09:23:44,626 - src.agent - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 09:23:44,630 - src.agent - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 09:23:44,870 - src.agent - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 09:23:45,043 - src.agent - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 09:23:45,047 - src.agent - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 09:23:45,212 - src.agent - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 09:23:45,216 - src.agent - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 09:23:45,223 - src.agent - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 09:23:45,228 - src.agent - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 09:23:45,296 - src.agent - WARNING - monitoring.py:154 - Performance threshold exceeded: test_operation took 62.47ms (threshold: 50ms)
2025-07-10 09:23:45,312 - src.agent - WARNING - security.py:80 - Dangerous pattern detected: (rm|del|format|shutdown|reboot|kill|pkill)\s+.*?/
2025-07-10 09:23:45,316 - src.agent - WARNING - security.py:80 - Dangerous pattern detected: ;\s*(drop|delete)\s+(table|database|from)
2025-07-10 09:23:45,322 - src.agent - WARNING - security.py:80 - Dangerous pattern detected: (eval|exec)\s*\(
2025-07-10 09:23:45,325 - src.agent - WARNING - security.py:80 - Dangerous pattern detected: \.\./+(etc|windows|system32)
2025-07-10 09:23:45,329 - src.agent - WARNING - security.py:80 - Dangerous pattern detected: (system|popen|subprocess)\s*[\.\(]
2025-07-10 09:23:45,378 - src.agent - WARNING - security.py:276 - Rate limit exceeded for client: test_client
2025-07-10 09:23:45,393 - src.agent - WARNING - security.py:276 - Rate limit exceeded for client: client_1
2025-07-10 09:23:45,398 - src.agent - WARNING - security.py:276 - Rate limit exceeded for client: client_2
2025-07-10 09:23:45,409 - src.agent - INFO - security.py:328 - SECURITY: test_event - {'detail': 'test_detail'}
2025-07-10 09:23:45,415 - src.agent - CRITICAL - security.py:322 - SECURITY: critical_event - {'detail': 'critical_detail'}
2025-07-10 09:23:45,426 - src.agent - WARNING - security.py:276 - Rate limit exceeded for client: integration_test_client
2025-07-10 09:23:45,429 - src.agent - INFO - security.py:328 - SECURITY: code_execution_request - {'client_id': 'integration_test_client', 'code_length': 35, 'is_safe': True}
2025-07-10 09:23:45,440 - src.agent - WARNING - security.py:80 - Dangerous pattern detected: (rm|del|format|shutdown|reboot|kill|pkill)\s+.*?/
2025-07-10 09:23:45,446 - src.agent - CRITICAL - security.py:322 - SECURITY: dangerous_code_detected - {'code': "import os; os.system('rm -rf /')", 'issues': ['Blocked module import: os']}
2025-07-10 09:35:23,821 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:35:23,829 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:35:23,840 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:35:23,848 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:35:23,851 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:35:23,857 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:35:23,862 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:35:23,867 - src.requirements_parser - INFO - requirements_parser.py:228 - Created new user profile for test_user_123
2025-07-10 09:35:23,873 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:35:23,881 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:35:23,887 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:35:23,892 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:35:23,895 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user test_user
2025-07-10 09:35:23,900 - src.requirements_parser - INFO - requirements_parser.py:228 - Created new user profile for test_user
2025-07-10 09:35:23,903 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:35:23,905 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.70
2025-07-10 09:35:23,912 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:35:23,922 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:35:23,925 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user test_user
2025-07-10 09:35:23,928 - src.requirements_parser - INFO - requirements_parser.py:228 - Created new user profile for test_user
2025-07-10 09:35:23,929 - src.requirements_parser - WARNING - requirements_parser.py:319 - LLM analysis failed, using fallback: LLM Error
2025-07-10 09:35:23,931 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:35:23,933 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.20
2025-07-10 09:39:40,544 - src.question_generator - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:39:40,551 - src.question_generator - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:39:40,559 - src.question_generator - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:39:40,567 - src.question_generator - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:39:40,571 - src.question_generator - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:39:40,577 - src.question_generator - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:39:40,584 - src.question_generator - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:39:40,591 - src.question_generator - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:39:40,597 - src.question_generator - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:39:40,732 - src.question_generator - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:39:40,736 - src.question_generator - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:39:40,741 - src.question_generator - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:39:40,744 - src.question_generator - INFO - question_generator.py:75 - Generating questions for web_app project
2025-07-10 09:39:40,747 - src.question_generator - INFO - question_generator.py:101 - Generated 3 contextual questions
2025-07-10 09:39:40,751 - src.question_generator - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:39:40,754 - src.question_generator - INFO - question_generator.py:75 - Generating questions for web_app project
2025-07-10 09:39:40,757 - src.question_generator - WARNING - question_generator.py:142 - LLM question generation failed: LLM Error
2025-07-10 09:39:40,759 - src.question_generator - INFO - question_generator.py:101 - Generated 3 contextual questions
2025-07-10 09:40:03,137 - src.question_generator - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:40:14,264 - src.question_generator - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:40:14,282 - src.question_generator - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:40:14,306 - src.question_generator - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:40:14,311 - src.question_generator - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:40:14,318 - src.question_generator - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:40:14,323 - src.question_generator - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:40:14,328 - src.question_generator - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:40:14,335 - src.question_generator - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:40:14,342 - src.question_generator - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:40:14,346 - src.question_generator - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:40:14,356 - src.question_generator - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:40:14,362 - src.question_generator - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:40:14,367 - src.question_generator - INFO - question_generator.py:75 - Generating questions for web_app project
2025-07-10 09:40:14,370 - src.question_generator - INFO - question_generator.py:101 - Generated 3 contextual questions
2025-07-10 09:40:14,377 - src.question_generator - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:40:14,379 - src.question_generator - INFO - question_generator.py:75 - Generating questions for web_app project
2025-07-10 09:40:14,381 - src.question_generator - WARNING - question_generator.py:142 - LLM question generation failed: LLM Error
2025-07-10 09:40:14,389 - src.question_generator - INFO - question_generator.py:101 - Generated 3 contextual questions
2025-07-10 09:40:23,672 - src.question_generator - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:40:23,690 - src.question_generator - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:40:23,717 - src.question_generator - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:40:23,724 - src.question_generator - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:40:23,728 - src.question_generator - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:40:23,732 - src.question_generator - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:40:23,737 - src.question_generator - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:40:23,742 - src.question_generator - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:40:23,750 - src.question_generator - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:40:23,755 - src.question_generator - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:40:23,760 - src.question_generator - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:40:23,767 - src.question_generator - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:40:23,771 - src.question_generator - INFO - question_generator.py:75 - Generating questions for web_app project
2025-07-10 09:40:23,773 - src.question_generator - INFO - question_generator.py:101 - Generated 3 contextual questions
2025-07-10 09:40:23,777 - src.question_generator - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:40:23,781 - src.question_generator - INFO - question_generator.py:75 - Generating questions for web_app project
2025-07-10 09:40:23,783 - src.question_generator - WARNING - question_generator.py:142 - LLM question generation failed: LLM Error
2025-07-10 09:40:23,785 - src.question_generator - INFO - question_generator.py:101 - Generated 3 contextual questions
2025-07-10 09:43:42,601 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:43:42,610 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:43:42,618 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:43:42,623 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:43:42,627 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:43:42,635 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:43:42,641 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:43:42,650 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:43:42,655 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:43:42,659 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:43:42,800 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:43:42,805 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:43:42,809 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:43:42,814 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:43:42,818 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:43:42,824 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:43:42,828 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:43:42,839 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:43:42,844 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:43:42,850 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:43:42,858 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:43:42,861 - src.explanation_system - INFO - explanation_system.py:104 - Generating explanation for topic: API
2025-07-10 09:43:42,864 - src.explanation_system - INFO - explanation_system.py:146 - Generated explanation with confidence: 0.70
2025-07-10 09:43:42,868 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:43:42,876 - src.explanation_system - INFO - explanation_system.py:104 - Generating explanation for topic: database
2025-07-10 09:43:42,877 - src.explanation_system - WARNING - explanation_system.py:222 - LLM explanation generation failed: LLM Error
2025-07-10 09:43:42,878 - src.explanation_system - INFO - explanation_system.py:146 - Generated explanation with confidence: 0.70
2025-07-10 09:44:15,353 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:44:39,908 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:44:39,914 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:44:39,918 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:44:39,923 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:44:39,926 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:44:39,934 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:44:39,939 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:44:39,946 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:44:39,955 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:44:39,960 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:44:39,968 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:44:39,973 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:44:39,979 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:44:39,989 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:44:39,994 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:44:39,999 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:44:40,007 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:44:40,011 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:44:40,018 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:44:40,026 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:44:40,032 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:44:40,035 - src.explanation_system - INFO - explanation_system.py:104 - Generating explanation for topic: API
2025-07-10 09:44:40,038 - src.explanation_system - INFO - explanation_system.py:146 - Generated explanation with confidence: 0.70
2025-07-10 09:44:40,045 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:44:40,048 - src.explanation_system - INFO - explanation_system.py:104 - Generating explanation for topic: database
2025-07-10 09:44:40,051 - src.explanation_system - WARNING - explanation_system.py:222 - LLM explanation generation failed: LLM Error
2025-07-10 09:44:40,057 - src.explanation_system - ERROR - explanation_system.py:150 - Failed to generate explanation: LLM Error
2025-07-10 09:46:13,040 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:46:13,049 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:46:13,055 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:46:13,059 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:46:13,068 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:46:13,073 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:46:13,078 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:46:13,087 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:46:13,092 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:46:13,100 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:46:13,105 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:46:13,110 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:46:13,118 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:46:13,123 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:46:13,128 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:46:13,138 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:46:13,144 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:46:13,148 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:46:13,159 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:46:13,163 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:46:13,168 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:46:13,172 - src.explanation_system - INFO - explanation_system.py:104 - Generating explanation for topic: API
2025-07-10 09:46:13,175 - src.explanation_system - INFO - explanation_system.py:146 - Generated explanation with confidence: 0.70
2025-07-10 09:46:13,178 - src.explanation_system - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:46:13,181 - src.explanation_system - INFO - explanation_system.py:104 - Generating explanation for topic: database
2025-07-10 09:46:13,183 - src.explanation_system - WARNING - explanation_system.py:222 - LLM explanation generation failed: LLM Error
2025-07-10 09:46:13,184 - src.explanation_system - ERROR - explanation_system.py:150 - Failed to generate explanation: LLM Error
2025-07-10 09:49:33,524 - src.personality_system - INFO - personality_system.py:176 - PersonalitySystem initialized with agent 'Alex'
2025-07-10 09:49:33,533 - src.personality_system - INFO - personality_system.py:176 - PersonalitySystem initialized with agent 'Alex'
2025-07-10 09:49:33,535 - src.personality_system - DEBUG - personality_system.py:282 - Created new conversation memory for user test_user_123
2025-07-10 09:49:33,540 - src.personality_system - INFO - personality_system.py:176 - PersonalitySystem initialized with agent 'Alex'
2025-07-10 09:49:33,543 - src.personality_system - DEBUG - personality_system.py:282 - Created new conversation memory for user test_user_123
2025-07-10 09:49:33,547 - src.personality_system - INFO - personality_system.py:176 - PersonalitySystem initialized with agent 'Alex'
2025-07-10 09:49:33,550 - src.personality_system - INFO - personality_system.py:176 - PersonalitySystem initialized with agent 'Alex'
2025-07-10 09:49:33,556 - src.personality_system - INFO - personality_system.py:176 - PersonalitySystem initialized with agent 'Alex'
2025-07-10 09:49:33,561 - src.personality_system - INFO - personality_system.py:176 - PersonalitySystem initialized with agent 'Alex'
2025-07-10 09:49:33,682 - src.personality_system - INFO - personality_system.py:176 - PersonalitySystem initialized with agent 'Alex'
2025-07-10 09:49:33,687 - src.personality_system - INFO - personality_system.py:176 - PersonalitySystem initialized with agent 'Alex'
2025-07-10 09:49:33,700 - src.personality_system - INFO - personality_system.py:176 - PersonalitySystem initialized with agent 'Alex'
2025-07-10 09:49:33,705 - src.personality_system - INFO - personality_system.py:176 - PersonalitySystem initialized with agent 'Alex'
2025-07-10 09:49:33,708 - src.personality_system - DEBUG - personality_system.py:282 - Created new conversation memory for user new_user
2025-07-10 09:49:33,711 - src.personality_system - INFO - personality_system.py:176 - PersonalitySystem initialized with agent 'Alex'
2025-07-10 09:49:33,714 - src.personality_system - DEBUG - personality_system.py:282 - Created new conversation memory for user returning_user
2025-07-10 09:49:33,721 - src.personality_system - INFO - personality_system.py:176 - PersonalitySystem initialized with agent 'Alex'
2025-07-10 09:49:33,724 - src.personality_system - DEBUG - personality_system.py:282 - Created new conversation memory for user familiar_user
2025-07-10 09:49:33,728 - src.personality_system - INFO - personality_system.py:176 - PersonalitySystem initialized with agent 'Alex'
2025-07-10 09:49:33,732 - src.personality_system - DEBUG - personality_system.py:282 - Created new conversation memory for user test_user
2025-07-10 09:49:33,734 - src.personality_system - INFO - personality_system.py:255 - Remembered preference for user test_user: language = Python
2025-07-10 09:49:33,738 - src.personality_system - INFO - personality_system.py:176 - PersonalitySystem initialized with agent 'Alex'
2025-07-10 09:49:33,741 - src.personality_system - DEBUG - personality_system.py:282 - Created new conversation memory for user test_user
2025-07-10 09:49:33,746 - src.personality_system - INFO - personality_system.py:176 - PersonalitySystem initialized with agent 'Alex'
2025-07-10 09:49:33,749 - src.personality_system - DEBUG - personality_system.py:282 - Created new conversation memory for user test_user
2025-07-10 09:49:33,751 - src.personality_system - DEBUG - personality_system.py:211 - Generated personalized response for user test_user
2025-07-10 09:49:33,771 - src.personality_system - INFO - personality_system.py:176 - PersonalitySystem initialized with agent 'Alex'
2025-07-10 09:49:33,774 - src.personality_system - DEBUG - personality_system.py:282 - Created new conversation memory for user test_user
2025-07-10 09:49:33,776 - src.personality_system - ERROR - personality_system.py:215 - Failed to personalize response: Test error
2025-07-10 09:49:33,780 - src.personality_system - INFO - personality_system.py:176 - PersonalitySystem initialized with agent 'Alex'
2025-07-10 09:49:33,785 - src.personality_system - INFO - personality_system.py:176 - PersonalitySystem initialized with agent 'Alex'
2025-07-10 09:50:56,364 - src.personality_system - INFO - personality_system.py:176 - PersonalitySystem initialized with agent 'Alex'
2025-07-10 09:51:45,564 - src.personality_system - INFO - personality_system.py:176 - PersonalitySystem initialized with agent 'Alex'
2025-07-10 09:51:45,577 - src.personality_system - INFO - personality_system.py:176 - PersonalitySystem initialized with agent 'Alex'
2025-07-10 09:51:45,582 - src.personality_system - DEBUG - personality_system.py:282 - Created new conversation memory for user test_user_123
2025-07-10 09:51:45,586 - src.personality_system - INFO - personality_system.py:176 - PersonalitySystem initialized with agent 'Alex'
2025-07-10 09:51:45,589 - src.personality_system - DEBUG - personality_system.py:282 - Created new conversation memory for user test_user_123
2025-07-10 09:51:45,593 - src.personality_system - INFO - personality_system.py:176 - PersonalitySystem initialized with agent 'Alex'
2025-07-10 09:51:45,599 - src.personality_system - INFO - personality_system.py:176 - PersonalitySystem initialized with agent 'Alex'
2025-07-10 09:51:45,605 - src.personality_system - INFO - personality_system.py:176 - PersonalitySystem initialized with agent 'Alex'
2025-07-10 09:51:45,611 - src.personality_system - INFO - personality_system.py:176 - PersonalitySystem initialized with agent 'Alex'
2025-07-10 09:51:45,618 - src.personality_system - INFO - personality_system.py:176 - PersonalitySystem initialized with agent 'Alex'
2025-07-10 09:51:45,624 - src.personality_system - INFO - personality_system.py:176 - PersonalitySystem initialized with agent 'Alex'
2025-07-10 09:51:45,759 - src.personality_system - INFO - personality_system.py:176 - PersonalitySystem initialized with agent 'Alex'
2025-07-10 09:51:45,764 - src.personality_system - INFO - personality_system.py:176 - PersonalitySystem initialized with agent 'Alex'
2025-07-10 09:51:45,766 - src.personality_system - DEBUG - personality_system.py:282 - Created new conversation memory for user new_user
2025-07-10 09:51:45,770 - src.personality_system - INFO - personality_system.py:176 - PersonalitySystem initialized with agent 'Alex'
2025-07-10 09:51:45,773 - src.personality_system - DEBUG - personality_system.py:282 - Created new conversation memory for user returning_user
2025-07-10 09:51:45,779 - src.personality_system - INFO - personality_system.py:176 - PersonalitySystem initialized with agent 'Alex'
2025-07-10 09:51:45,782 - src.personality_system - DEBUG - personality_system.py:282 - Created new conversation memory for user familiar_user
2025-07-10 09:51:45,791 - src.personality_system - INFO - personality_system.py:176 - PersonalitySystem initialized with agent 'Alex'
2025-07-10 09:51:45,793 - src.personality_system - DEBUG - personality_system.py:282 - Created new conversation memory for user test_user
2025-07-10 09:51:45,795 - src.personality_system - INFO - personality_system.py:255 - Remembered preference for user test_user: language = Python
2025-07-10 09:51:45,799 - src.personality_system - INFO - personality_system.py:176 - PersonalitySystem initialized with agent 'Alex'
2025-07-10 09:51:45,801 - src.personality_system - DEBUG - personality_system.py:282 - Created new conversation memory for user test_user
2025-07-10 09:51:45,807 - src.personality_system - INFO - personality_system.py:176 - PersonalitySystem initialized with agent 'Alex'
2025-07-10 09:51:45,810 - src.personality_system - DEBUG - personality_system.py:282 - Created new conversation memory for user test_user
2025-07-10 09:51:45,812 - src.personality_system - DEBUG - personality_system.py:211 - Generated personalized response for user test_user
2025-07-10 09:51:45,816 - src.personality_system - INFO - personality_system.py:176 - PersonalitySystem initialized with agent 'Alex'
2025-07-10 09:51:45,818 - src.personality_system - DEBUG - personality_system.py:282 - Created new conversation memory for user test_user
2025-07-10 09:51:45,822 - src.personality_system - ERROR - personality_system.py:215 - Failed to personalize response: Test error
2025-07-10 09:51:45,826 - src.personality_system - INFO - personality_system.py:176 - PersonalitySystem initialized with agent 'Alex'
2025-07-10 09:51:45,831 - src.personality_system - INFO - personality_system.py:176 - PersonalitySystem initialized with agent 'Alex'
2025-07-10 09:53:46,818 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:53:46,824 - src.requirements_parser - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:53:46,826 - src.requirements_parser - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:53:46,828 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user casual_user
2025-07-10 09:53:46,830 - src.requirements_parser - INFO - requirements_parser.py:228 - Created new user profile for casual_user
2025-07-10 09:53:46,839 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:53:46,841 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.70
2025-07-10 09:53:46,845 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user casual_user
2025-07-10 09:53:46,847 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:53:46,848 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.75
2025-07-10 09:53:46,849 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user casual_user
2025-07-10 09:53:46,851 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:53:46,856 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.75
2025-07-10 09:53:46,859 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user casual_user
2025-07-10 09:53:46,862 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:53:46,864 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.70
2025-07-10 09:53:46,866 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user casual_user
2025-07-10 09:53:46,867 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:53:46,868 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.70
2025-07-10 09:53:46,876 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:53:46,879 - src.requirements_parser - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:53:46,881 - src.requirements_parser - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:53:46,883 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user technical_user
2025-07-10 09:53:46,885 - src.requirements_parser - INFO - requirements_parser.py:228 - Created new user profile for technical_user
2025-07-10 09:53:46,890 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:53:46,892 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.74
2025-07-10 09:53:46,893 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user technical_user
2025-07-10 09:53:46,895 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:53:46,896 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.77
2025-07-10 09:53:46,898 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user technical_user
2025-07-10 09:53:46,899 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:53:46,900 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.74
2025-07-10 09:53:46,901 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user technical_user
2025-07-10 09:53:46,904 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:53:46,907 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.74
2025-07-10 09:53:46,910 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user technical_user
2025-07-10 09:53:46,912 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:53:46,915 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.74
2025-07-10 09:53:46,920 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:53:46,923 - src.requirements_parser - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:53:46,925 - src.requirements_parser - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:53:46,927 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user beginner_user
2025-07-10 09:53:46,930 - src.requirements_parser - INFO - requirements_parser.py:228 - Created new user profile for beginner_user
2025-07-10 09:53:46,932 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:53:46,934 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.65
2025-07-10 09:53:46,935 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user beginner_user
2025-07-10 09:53:46,939 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:53:46,941 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.65
2025-07-10 09:53:46,945 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user beginner_user
2025-07-10 09:53:46,947 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:53:46,948 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.65
2025-07-10 09:53:46,949 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user beginner_user
2025-07-10 09:53:46,951 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:53:46,953 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.65
2025-07-10 09:53:46,956 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user beginner_user
2025-07-10 09:53:46,958 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:53:46,962 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.65
2025-07-10 09:53:46,966 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:53:46,968 - src.requirements_parser - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:53:46,969 - src.requirements_parser - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:53:46,975 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user domain_user_0
2025-07-10 09:53:46,976 - src.requirements_parser - INFO - requirements_parser.py:228 - Created new user profile for domain_user_0
2025-07-10 09:53:46,977 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:53:46,979 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.70
2025-07-10 09:53:46,980 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user domain_user_1
2025-07-10 09:53:46,982 - src.requirements_parser - INFO - requirements_parser.py:228 - Created new user profile for domain_user_1
2025-07-10 09:53:46,983 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:53:46,984 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.70
2025-07-10 09:53:46,985 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user domain_user_2
2025-07-10 09:53:46,989 - src.requirements_parser - INFO - requirements_parser.py:228 - Created new user profile for domain_user_2
2025-07-10 09:53:46,992 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:53:46,996 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.70
2025-07-10 09:53:46,998 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user domain_user_3
2025-07-10 09:53:46,999 - src.requirements_parser - INFO - requirements_parser.py:228 - Created new user profile for domain_user_3
2025-07-10 09:53:47,000 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:53:47,002 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.70
2025-07-10 09:53:47,004 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user domain_user_4
2025-07-10 09:53:47,006 - src.requirements_parser - INFO - requirements_parser.py:228 - Created new user profile for domain_user_4
2025-07-10 09:53:47,008 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:53:47,012 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.70
2025-07-10 09:53:47,016 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:53:47,017 - src.requirements_parser - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:53:47,020 - src.requirements_parser - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:53:47,025 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user vague_user
2025-07-10 09:53:47,026 - src.requirements_parser - INFO - requirements_parser.py:228 - Created new user profile for vague_user
2025-07-10 09:53:47,028 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:53:47,029 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.20
2025-07-10 09:53:47,031 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user vague_user
2025-07-10 09:53:47,032 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:53:47,033 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.20
2025-07-10 09:53:47,034 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user vague_user
2025-07-10 09:53:47,036 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:53:47,039 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.20
2025-07-10 09:53:47,042 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user vague_user
2025-07-10 09:53:47,045 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:53:47,047 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.20
2025-07-10 09:53:47,049 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user vague_user
2025-07-10 09:53:47,050 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:53:47,052 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.20
2025-07-10 09:53:47,057 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:53:47,061 - src.requirements_parser - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:53:47,066 - src.requirements_parser - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:53:47,072 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user mixed_user
2025-07-10 09:53:47,073 - src.requirements_parser - INFO - requirements_parser.py:228 - Created new user profile for mixed_user
2025-07-10 09:53:47,075 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:53:47,076 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.57
2025-07-10 09:53:47,078 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user mixed_user
2025-07-10 09:53:47,079 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:53:47,081 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.57
2025-07-10 09:53:47,084 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user mixed_user
2025-07-10 09:53:47,089 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:53:47,099 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.57
2025-07-10 09:53:47,101 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user mixed_user
2025-07-10 09:53:47,102 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:53:47,107 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.57
2025-07-10 09:53:47,109 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user mixed_user
2025-07-10 09:53:47,111 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:53:47,112 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.57
2025-07-10 09:53:47,116 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:53:47,118 - src.requirements_parser - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:53:47,127 - src.requirements_parser - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:53:47,130 - src.requirements_parser - INFO - question_generator.py:75 - Generating questions for web_app project
2025-07-10 09:53:47,132 - src.requirements_parser - INFO - question_generator.py:101 - Generated 3 contextual questions
2025-07-10 09:53:47,134 - src.requirements_parser - INFO - question_generator.py:75 - Generating questions for api project
2025-07-10 09:53:47,135 - src.requirements_parser - INFO - question_generator.py:101 - Generated 3 contextual questions
2025-07-10 09:53:47,140 - src.requirements_parser - INFO - question_generator.py:75 - Generating questions for mobile_app project
2025-07-10 09:53:47,141 - src.requirements_parser - INFO - question_generator.py:101 - Generated 3 contextual questions
2025-07-10 09:53:47,145 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:53:47,147 - src.requirements_parser - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:53:47,148 - src.requirements_parser - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:53:47,150 - src.requirements_parser - INFO - explanation_system.py:104 - Generating explanation for topic: API
2025-07-10 09:53:47,154 - src.requirements_parser - INFO - explanation_system.py:146 - Generated explanation with confidence: 0.70
2025-07-10 09:53:47,156 - src.requirements_parser - INFO - explanation_system.py:104 - Generating explanation for topic: microservices
2025-07-10 09:53:47,158 - src.requirements_parser - INFO - explanation_system.py:146 - Generated explanation with confidence: 0.70
2025-07-10 09:53:47,160 - src.requirements_parser - INFO - explanation_system.py:104 - Generating explanation for topic: database
2025-07-10 09:53:47,161 - src.requirements_parser - INFO - explanation_system.py:146 - Generated explanation with confidence: 0.70
2025-07-10 09:53:47,165 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:53:47,169 - src.requirements_parser - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:53:47,173 - src.requirements_parser - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:53:47,176 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user test_user
2025-07-10 09:53:47,178 - src.requirements_parser - INFO - requirements_parser.py:228 - Created new user profile for test_user
2025-07-10 09:53:47,181 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:53:47,183 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.20
2025-07-10 09:53:47,191 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user test_user
2025-07-10 09:53:47,194 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:53:47,196 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.20
2025-07-10 09:53:47,199 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user test_user
2025-07-10 09:53:47,212 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:53:47,213 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.20
2025-07-10 09:53:47,215 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user test_user
2025-07-10 09:53:47,216 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:53:47,217 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.20
2025-07-10 09:53:47,218 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user test_user
2025-07-10 09:53:47,222 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:53:47,224 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.20
2025-07-10 09:53:47,226 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user test_user
2025-07-10 09:53:47,228 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:53:47,230 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.20
2025-07-10 09:53:47,231 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user test_user
2025-07-10 09:53:47,232 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:53:47,233 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.20
2025-07-10 09:53:47,234 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user test_user
2025-07-10 09:53:47,238 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:53:47,241 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.20
2025-07-10 09:53:47,244 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:53:47,246 - src.requirements_parser - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:53:47,247 - src.requirements_parser - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:53:47,249 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user integration_test_user
2025-07-10 09:53:47,251 - src.requirements_parser - INFO - requirements_parser.py:228 - Created new user profile for integration_test_user
2025-07-10 09:53:47,256 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:53:47,259 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.83
2025-07-10 09:53:47,260 - src.requirements_parser - INFO - question_generator.py:75 - Generating questions for web_app project
2025-07-10 09:53:47,261 - src.requirements_parser - INFO - question_generator.py:101 - Generated 2 contextual questions
2025-07-10 09:53:47,263 - src.requirements_parser - INFO - explanation_system.py:104 - Generating explanation for topic: web application architecture
2025-07-10 09:53:47,264 - src.requirements_parser - INFO - explanation_system.py:146 - Generated explanation with confidence: 0.70
2025-07-10 09:55:04,797 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:55:04,817 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:55:04,831 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:55:04,837 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:55:04,846 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:55:04,850 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:55:04,859 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:55:04,862 - src.requirements_parser - INFO - requirements_parser.py:228 - Created new user profile for test_user_123
2025-07-10 09:55:04,866 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:55:04,875 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:55:04,880 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:55:04,884 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:55:04,893 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user test_user
2025-07-10 09:55:04,894 - src.requirements_parser - INFO - requirements_parser.py:228 - Created new user profile for test_user
2025-07-10 09:55:04,896 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:55:04,899 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.70
2025-07-10 09:55:04,904 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:55:04,912 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:55:04,917 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user test_user
2025-07-10 09:55:04,918 - src.requirements_parser - INFO - requirements_parser.py:228 - Created new user profile for test_user
2025-07-10 09:55:04,923 - src.requirements_parser - WARNING - requirements_parser.py:319 - LLM analysis failed, using fallback: LLM Error
2025-07-10 09:55:04,926 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:55:04,929 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.20
2025-07-10 09:55:05,062 - src.requirements_parser - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:55:05,096 - src.requirements_parser - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:55:05,100 - src.requirements_parser - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:55:05,110 - src.requirements_parser - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:55:05,115 - src.requirements_parser - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:55:05,124 - src.requirements_parser - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:55:05,129 - src.requirements_parser - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:55:05,135 - src.requirements_parser - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:55:05,146 - src.requirements_parser - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:55:05,149 - src.requirements_parser - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:55:05,155 - src.requirements_parser - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:55:05,161 - src.requirements_parser - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:55:05,164 - src.requirements_parser - INFO - question_generator.py:75 - Generating questions for web_app project
2025-07-10 09:55:05,176 - src.requirements_parser - INFO - question_generator.py:101 - Generated 3 contextual questions
2025-07-10 09:55:05,180 - src.requirements_parser - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:55:05,183 - src.requirements_parser - INFO - question_generator.py:75 - Generating questions for web_app project
2025-07-10 09:55:05,184 - src.requirements_parser - WARNING - question_generator.py:142 - LLM question generation failed: LLM Error
2025-07-10 09:55:05,190 - src.requirements_parser - INFO - question_generator.py:101 - Generated 3 contextual questions
2025-07-10 09:55:05,198 - src.requirements_parser - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:55:05,207 - src.requirements_parser - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:55:05,212 - src.requirements_parser - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:55:05,218 - src.requirements_parser - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:55:05,227 - src.requirements_parser - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:55:05,232 - src.requirements_parser - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:55:05,237 - src.requirements_parser - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:55:05,244 - src.requirements_parser - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:55:05,250 - src.requirements_parser - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:55:05,255 - src.requirements_parser - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:55:05,260 - src.requirements_parser - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:55:05,271 - src.requirements_parser - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:55:05,277 - src.requirements_parser - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:55:05,281 - src.requirements_parser - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:55:05,292 - src.requirements_parser - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:55:05,297 - src.requirements_parser - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:55:05,303 - src.requirements_parser - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:55:05,312 - src.requirements_parser - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:55:05,316 - src.requirements_parser - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:55:05,320 - src.requirements_parser - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:55:05,325 - src.requirements_parser - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:55:05,328 - src.requirements_parser - INFO - explanation_system.py:104 - Generating explanation for topic: API
2025-07-10 09:55:05,331 - src.requirements_parser - INFO - explanation_system.py:146 - Generated explanation with confidence: 0.70
2025-07-10 09:55:05,341 - src.requirements_parser - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:55:05,346 - src.requirements_parser - INFO - explanation_system.py:104 - Generating explanation for topic: database
2025-07-10 09:55:05,348 - src.requirements_parser - WARNING - explanation_system.py:222 - LLM explanation generation failed: LLM Error
2025-07-10 09:55:05,350 - src.requirements_parser - ERROR - explanation_system.py:150 - Failed to generate explanation: LLM Error
2025-07-10 09:55:05,364 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:55:05,366 - src.requirements_parser - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:55:05,368 - src.requirements_parser - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:55:05,372 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user casual_user
2025-07-10 09:55:05,375 - src.requirements_parser - INFO - requirements_parser.py:228 - Created new user profile for casual_user
2025-07-10 09:55:05,376 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:55:05,380 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.70
2025-07-10 09:55:05,386 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user casual_user
2025-07-10 09:55:05,389 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:55:05,392 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.75
2025-07-10 09:55:05,393 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user casual_user
2025-07-10 09:55:05,397 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:55:05,399 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.75
2025-07-10 09:55:05,401 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user casual_user
2025-07-10 09:55:05,404 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:55:05,407 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.70
2025-07-10 09:55:05,409 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user casual_user
2025-07-10 09:55:05,411 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:55:05,416 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.70
2025-07-10 09:55:05,423 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:55:05,426 - src.requirements_parser - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:55:05,428 - src.requirements_parser - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:55:05,432 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user technical_user
2025-07-10 09:55:05,435 - src.requirements_parser - INFO - requirements_parser.py:228 - Created new user profile for technical_user
2025-07-10 09:55:05,442 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:55:05,444 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.74
2025-07-10 09:55:05,446 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user technical_user
2025-07-10 09:55:05,448 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:55:05,450 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.77
2025-07-10 09:55:05,452 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user technical_user
2025-07-10 09:55:05,454 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:55:05,456 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.74
2025-07-10 09:55:05,458 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user technical_user
2025-07-10 09:55:05,460 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:55:05,462 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.74
2025-07-10 09:55:05,463 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user technical_user
2025-07-10 09:55:05,468 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:55:05,471 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.74
2025-07-10 09:55:05,476 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:55:05,477 - src.requirements_parser - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:55:05,479 - src.requirements_parser - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:55:05,481 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user beginner_user
2025-07-10 09:55:05,482 - src.requirements_parser - INFO - requirements_parser.py:228 - Created new user profile for beginner_user
2025-07-10 09:55:05,484 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:55:05,486 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.65
2025-07-10 09:55:05,493 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user beginner_user
2025-07-10 09:55:05,496 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:55:05,498 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.65
2025-07-10 09:55:05,499 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user beginner_user
2025-07-10 09:55:05,500 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:55:05,502 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.65
2025-07-10 09:55:05,506 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user beginner_user
2025-07-10 09:55:05,508 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:55:05,509 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.65
2025-07-10 09:55:05,511 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user beginner_user
2025-07-10 09:55:05,512 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:55:05,514 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.65
2025-07-10 09:55:05,520 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:55:05,524 - src.requirements_parser - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:55:05,527 - src.requirements_parser - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:55:05,529 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user domain_user_0
2025-07-10 09:55:05,531 - src.requirements_parser - INFO - requirements_parser.py:228 - Created new user profile for domain_user_0
2025-07-10 09:55:05,534 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:55:05,538 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.70
2025-07-10 09:55:05,541 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user domain_user_1
2025-07-10 09:55:05,542 - src.requirements_parser - INFO - requirements_parser.py:228 - Created new user profile for domain_user_1
2025-07-10 09:55:05,544 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:55:05,545 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.70
2025-07-10 09:55:05,547 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user domain_user_2
2025-07-10 09:55:05,549 - src.requirements_parser - INFO - requirements_parser.py:228 - Created new user profile for domain_user_2
2025-07-10 09:55:05,551 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:55:05,554 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.70
2025-07-10 09:55:05,556 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user domain_user_3
2025-07-10 09:55:05,558 - src.requirements_parser - INFO - requirements_parser.py:228 - Created new user profile for domain_user_3
2025-07-10 09:55:05,560 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:55:05,561 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.70
2025-07-10 09:55:05,562 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user domain_user_4
2025-07-10 09:55:05,563 - src.requirements_parser - INFO - requirements_parser.py:228 - Created new user profile for domain_user_4
2025-07-10 09:55:05,565 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:55:05,566 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.70
2025-07-10 09:55:05,570 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:55:05,573 - src.requirements_parser - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:55:05,575 - src.requirements_parser - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:55:05,578 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user vague_user
2025-07-10 09:55:05,580 - src.requirements_parser - INFO - requirements_parser.py:228 - Created new user profile for vague_user
2025-07-10 09:55:05,581 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:55:05,584 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.20
2025-07-10 09:55:05,587 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user vague_user
2025-07-10 09:55:05,592 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:55:05,593 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.20
2025-07-10 09:55:05,595 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user vague_user
2025-07-10 09:55:05,596 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:55:05,598 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.20
2025-07-10 09:55:05,600 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user vague_user
2025-07-10 09:55:05,602 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:55:05,604 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.20
2025-07-10 09:55:05,608 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user vague_user
2025-07-10 09:55:05,610 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:55:05,611 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.20
2025-07-10 09:55:05,615 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:55:05,618 - src.requirements_parser - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:55:05,620 - src.requirements_parser - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:55:05,627 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user mixed_user
2025-07-10 09:55:05,629 - src.requirements_parser - INFO - requirements_parser.py:228 - Created new user profile for mixed_user
2025-07-10 09:55:05,631 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:55:05,632 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.57
2025-07-10 09:55:05,634 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user mixed_user
2025-07-10 09:55:05,635 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:55:05,637 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.57
2025-07-10 09:55:05,640 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user mixed_user
2025-07-10 09:55:05,642 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:55:05,643 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.57
2025-07-10 09:55:05,645 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user mixed_user
2025-07-10 09:55:05,646 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:55:05,648 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.57
2025-07-10 09:55:05,649 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user mixed_user
2025-07-10 09:55:05,651 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:55:05,653 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.57
2025-07-10 09:55:05,659 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:55:05,662 - src.requirements_parser - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:55:05,664 - src.requirements_parser - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:55:05,668 - src.requirements_parser - INFO - question_generator.py:75 - Generating questions for web_app project
2025-07-10 09:55:05,672 - src.requirements_parser - INFO - question_generator.py:101 - Generated 3 contextual questions
2025-07-10 09:55:05,675 - src.requirements_parser - INFO - question_generator.py:75 - Generating questions for api project
2025-07-10 09:55:05,678 - src.requirements_parser - INFO - question_generator.py:101 - Generated 3 contextual questions
2025-07-10 09:55:05,680 - src.requirements_parser - INFO - question_generator.py:75 - Generating questions for mobile_app project
2025-07-10 09:55:05,682 - src.requirements_parser - INFO - question_generator.py:101 - Generated 3 contextual questions
2025-07-10 09:55:05,685 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:55:05,689 - src.requirements_parser - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:55:05,692 - src.requirements_parser - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:55:05,695 - src.requirements_parser - INFO - explanation_system.py:104 - Generating explanation for topic: API
2025-07-10 09:55:05,697 - src.requirements_parser - INFO - explanation_system.py:146 - Generated explanation with confidence: 0.70
2025-07-10 09:55:05,699 - src.requirements_parser - INFO - explanation_system.py:104 - Generating explanation for topic: microservices
2025-07-10 09:55:05,705 - src.requirements_parser - INFO - explanation_system.py:146 - Generated explanation with confidence: 0.70
2025-07-10 09:55:05,708 - src.requirements_parser - INFO - explanation_system.py:104 - Generating explanation for topic: database
2025-07-10 09:55:05,709 - src.requirements_parser - INFO - explanation_system.py:146 - Generated explanation with confidence: 0.70
2025-07-10 09:55:05,713 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:55:05,715 - src.requirements_parser - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:55:05,716 - src.requirements_parser - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:55:05,723 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user test_user
2025-07-10 09:55:05,726 - src.requirements_parser - INFO - requirements_parser.py:228 - Created new user profile for test_user
2025-07-10 09:55:05,728 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:55:05,729 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.20
2025-07-10 09:55:05,730 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user test_user
2025-07-10 09:55:05,732 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:55:05,733 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.20
2025-07-10 09:55:05,734 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user test_user
2025-07-10 09:55:05,744 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:55:05,747 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.20
2025-07-10 09:55:05,748 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user test_user
2025-07-10 09:55:05,750 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:55:05,752 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.20
2025-07-10 09:55:05,754 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user test_user
2025-07-10 09:55:05,758 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:55:05,761 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.20
2025-07-10 09:55:05,764 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user test_user
2025-07-10 09:55:05,767 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:55:05,771 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.20
2025-07-10 09:55:05,774 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user test_user
2025-07-10 09:55:05,776 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:55:05,778 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.20
2025-07-10 09:55:05,781 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user test_user
2025-07-10 09:55:05,783 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:55:05,784 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.20
2025-07-10 09:55:05,791 - src.requirements_parser - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 09:55:05,793 - src.requirements_parser - INFO - question_generator.py:56 - QuestionGenerator initialized with dynamic question capabilities
2025-07-10 09:55:05,795 - src.requirements_parser - INFO - explanation_system.py:89 - ExplanationSystem initialized with adaptive communication capabilities
2025-07-10 09:55:05,798 - src.requirements_parser - INFO - requirements_parser.py:133 - Parsing requirements for user integration_test_user
2025-07-10 09:55:05,800 - src.requirements_parser - INFO - requirements_parser.py:228 - Created new user profile for integration_test_user
2025-07-10 09:55:05,801 - src.requirements_parser - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 09:55:05,806 - src.requirements_parser - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.83
2025-07-10 09:55:05,808 - src.requirements_parser - INFO - question_generator.py:75 - Generating questions for web_app project
2025-07-10 09:55:05,809 - src.requirements_parser - INFO - question_generator.py:101 - Generated 2 contextual questions
2025-07-10 09:55:05,810 - src.requirements_parser - INFO - explanation_system.py:104 - Generating explanation for topic: web application architecture
2025-07-10 09:55:05,815 - src.requirements_parser - INFO - explanation_system.py:146 - Generated explanation with confidence: 0.70
2025-07-10 10:09:11,371 - src.dependency_manager - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 10:09:11,375 - src.dependency_manager - INFO - dependency_manager.py:98 - CodeAnalysisEngine initialized with comprehensive language support
2025-07-10 10:09:11,398 - src.dependency_manager - INFO - dependency_manager.py:98 - CodeAnalysisEngine initialized with comprehensive language support
2025-07-10 10:09:11,555 - src.dependency_manager - INFO - dependency_manager.py:98 - CodeAnalysisEngine initialized with comprehensive language support
2025-07-10 10:09:11,575 - src.dependency_manager - INFO - dependency_manager.py:98 - CodeAnalysisEngine initialized with comprehensive language support
2025-07-10 10:09:11,598 - src.dependency_manager - WARNING - monitoring.py:164 - High CPU usage: 100.0%
2025-07-10 10:09:11,603 - src.dependency_manager - INFO - dependency_manager.py:98 - CodeAnalysisEngine initialized with comprehensive language support
2025-07-10 10:09:11,625 - src.dependency_manager - WARNING - monitoring.py:164 - High CPU usage: 100.0%
2025-07-10 10:09:11,665 - src.dependency_manager - INFO - dependency_manager.py:180 - Analyzed 3 files in C:\Users\<USER>\AppData\Local\Temp\tmp5ltcwu86
2025-07-10 10:09:11,673 - src.dependency_manager - INFO - dependency_manager.py:98 - CodeAnalysisEngine initialized with comprehensive language support
2025-07-10 10:09:11,678 - src.dependency_manager - DEBUG - dependency_manager.py:132 - Unsupported file type: .txt
2025-07-10 10:09:11,685 - src.dependency_manager - INFO - dependency_manager.py:98 - CodeAnalysisEngine initialized with comprehensive language support
2025-07-10 10:09:11,691 - src.dependency_manager - INFO - dependency_manager.py:582 - PackageInstaller initialized with multi-manager support
2025-07-10 10:09:11,698 - src.dependency_manager - INFO - dependency_manager.py:605 - Package requests already installed
2025-07-10 10:09:11,726 - src.dependency_manager - INFO - dependency_manager.py:582 - PackageInstaller initialized with multi-manager support
2025-07-10 10:09:11,736 - src.dependency_manager - INFO - dependency_manager.py:605 - Package react already installed
2025-07-10 10:09:11,763 - src.dependency_manager - INFO - dependency_manager.py:582 - PackageInstaller initialized with multi-manager support
2025-07-10 10:09:11,771 - src.dependency_manager - INFO - dependency_manager.py:716 - Installing Python package: nonexistent-package
2025-07-10 10:09:11,774 - src.dependency_manager - ERROR - dependency_manager.py:731 - pip install failed: Package not found
2025-07-10 10:09:11,782 - src.dependency_manager - INFO - dependency_manager.py:582 - PackageInstaller initialized with multi-manager support
2025-07-10 10:09:11,790 - src.dependency_manager - DEBUG - dependency_manager.py:600 - Skipping standard library package: os
2025-07-10 10:09:11,797 - src.dependency_manager - INFO - dependency_manager.py:582 - PackageInstaller initialized with multi-manager support
2025-07-10 10:09:11,800 - src.dependency_manager - INFO - dependency_manager.py:643 - Installing 2 packages with pip
2025-07-10 10:09:11,807 - src.dependency_manager - INFO - dependency_manager.py:605 - Package requests already installed
2025-07-10 10:09:11,811 - src.dependency_manager - INFO - dependency_manager.py:651 - ✓ Successfully installed requests
2025-07-10 10:09:11,817 - src.dependency_manager - INFO - dependency_manager.py:605 - Package flask already installed
2025-07-10 10:09:11,824 - src.dependency_manager - INFO - dependency_manager.py:651 - ✓ Successfully installed flask
2025-07-10 10:09:11,827 - src.dependency_manager - INFO - dependency_manager.py:643 - Installing 1 packages with npm
2025-07-10 10:09:11,830 - src.dependency_manager - INFO - dependency_manager.py:605 - Package react already installed
2025-07-10 10:09:11,834 - src.dependency_manager - INFO - dependency_manager.py:651 - ✓ Successfully installed react
2025-07-10 10:09:11,840 - src.dependency_manager - INFO - dependency_manager.py:857 - ConflictResolver initialized with compatibility rules
2025-07-10 10:09:11,844 - src.dependency_manager - INFO - dependency_manager.py:887 - Detected 1 potential conflicts
2025-07-10 10:09:11,849 - src.dependency_manager - INFO - dependency_manager.py:857 - ConflictResolver initialized with compatibility rules
2025-07-10 10:09:11,858 - src.dependency_manager - INFO - dependency_manager.py:887 - Detected 0 potential conflicts
2025-07-10 10:09:11,865 - src.dependency_manager - INFO - dependency_manager.py:857 - ConflictResolver initialized with compatibility rules
2025-07-10 10:09:11,872 - src.dependency_manager - INFO - dependency_manager.py:887 - Detected 2 potential conflicts
2025-07-10 10:09:11,879 - src.dependency_manager - INFO - dependency_manager.py:857 - ConflictResolver initialized with compatibility rules
2025-07-10 10:09:11,881 - src.dependency_manager - INFO - dependency_manager.py:911 - Proposed resolution for requests: 2.30.0
2025-07-10 10:09:11,885 - src.dependency_manager - INFO - dependency_manager.py:857 - ConflictResolver initialized with compatibility rules
2025-07-10 10:09:11,895 - src.dependency_manager - INFO - dependency_manager.py:1416 - DependencyCache initialized with cache dir: C:\Users\<USER>\AppData\Local\Temp\tmpm7geoxju\cache
2025-07-10 10:09:11,908 - src.dependency_manager - INFO - dependency_manager.py:1416 - DependencyCache initialized with cache dir: C:\Users\<USER>\AppData\Local\Temp\tmp8h4p9tm_\cache
2025-07-10 10:09:13,038 - src.dependency_manager - INFO - dependency_manager.py:1416 - DependencyCache initialized with cache dir: C:\Users\<USER>\AppData\Local\Temp\tmpg_btqie8\cache
2025-07-10 10:09:13,044 - src.dependency_manager - INFO - dependency_manager.py:1416 - DependencyCache initialized with cache dir: C:\Users\<USER>\AppData\Local\Temp\tmpwbfodsyz\cache
2025-07-10 10:09:13,055 - src.dependency_manager - INFO - dependency_manager.py:1416 - DependencyCache initialized with cache dir: C:\Users\<USER>\AppData\Local\Temp\tmppkbsxtup\cache
2025-07-10 10:09:13,060 - src.dependency_manager - INFO - dependency_manager.py:1526 - Cache cleared successfully
2025-07-10 10:09:13,066 - src.dependency_manager - INFO - dependency_manager.py:98 - CodeAnalysisEngine initialized with comprehensive language support
2025-07-10 10:09:13,068 - src.dependency_manager - INFO - dependency_manager.py:582 - PackageInstaller initialized with multi-manager support
2025-07-10 10:09:13,071 - src.dependency_manager - INFO - dependency_manager.py:857 - ConflictResolver initialized with compatibility rules
2025-07-10 10:09:13,075 - src.dependency_manager - INFO - dependency_manager.py:1103 - DependencyManager initialized with all components
2025-07-10 10:09:13,082 - src.dependency_manager - INFO - dependency_manager.py:1124 - Analyzing dependencies in C:\Users\<USER>\AppData\Local\Temp\tmp4d1hgyyl
2025-07-10 10:09:13,117 - src.dependency_manager - INFO - dependency_manager.py:180 - Analyzed 2 files in C:\Users\<USER>\AppData\Local\Temp\tmp4d1hgyyl
2025-07-10 10:09:13,124 - src.dependency_manager - INFO - dependency_manager.py:1135 - Found 3 unique dependencies
2025-07-10 10:09:13,127 - src.dependency_manager - INFO - dependency_manager.py:887 - Detected 0 potential conflicts
2025-07-10 10:09:13,132 - src.dependency_manager - INFO - dependency_manager.py:1161 - Installing 3 packages
2025-07-10 10:09:13,134 - src.dependency_manager - INFO - dependency_manager.py:1334 - Added 1 packages to requirements.txt
2025-07-10 10:09:13,140 - src.dependency_manager - WARNING - monitoring.py:164 - High CPU usage: 100.0%
2025-07-10 10:09:13,145 - src.dependency_manager - INFO - dependency_manager.py:98 - CodeAnalysisEngine initialized with comprehensive language support
2025-07-10 10:09:13,146 - src.dependency_manager - INFO - dependency_manager.py:582 - PackageInstaller initialized with multi-manager support
2025-07-10 10:09:13,148 - src.dependency_manager - INFO - dependency_manager.py:857 - ConflictResolver initialized with compatibility rules
2025-07-10 10:09:13,149 - src.dependency_manager - INFO - dependency_manager.py:1103 - DependencyManager initialized with all components
2025-07-10 10:09:13,180 - src.dependency_manager - INFO - dependency_manager.py:180 - Analyzed 2 files in C:\Users\<USER>\AppData\Local\Temp\tmpqf3yn8z0
2025-07-10 10:09:13,188 - src.dependency_manager - INFO - dependency_manager.py:98 - CodeAnalysisEngine initialized with comprehensive language support
2025-07-10 10:09:13,190 - src.dependency_manager - INFO - dependency_manager.py:582 - PackageInstaller initialized with multi-manager support
2025-07-10 10:09:13,192 - src.dependency_manager - INFO - dependency_manager.py:857 - ConflictResolver initialized with compatibility rules
2025-07-10 10:09:13,193 - src.dependency_manager - INFO - dependency_manager.py:1103 - DependencyManager initialized with all components
2025-07-10 10:09:13,194 - src.dependency_manager - INFO - dependency_manager.py:1416 - DependencyCache initialized with cache dir: C:\Users\<USER>\AppData\Local\Temp\tmp0hu94r6t\cache
2025-07-10 10:09:13,196 - src.dependency_manager - INFO - dependency_manager.py:98 - CodeAnalysisEngine initialized with comprehensive language support
2025-07-10 10:09:13,203 - src.dependency_manager - INFO - dependency_manager.py:1623 - CachedCodeAnalysisEngine initialized
2025-07-10 10:09:13,205 - src.dependency_manager - INFO - dependency_manager.py:1612 - EnhancedDependencyManager initialized with caching support
2025-07-10 10:09:13,208 - src.dependency_manager - DEBUG - dependency_manager.py:1636 - Cache miss for C:\Users\<USER>\AppData\Local\Temp\tmp0hu94r6t\test.py, analyzing...
2025-07-10 10:09:13,225 - src.dependency_manager - DEBUG - dependency_manager.py:1632 - Cache hit for C:\Users\<USER>\AppData\Local\Temp\tmp0hu94r6t\test.py
2025-07-10 10:10:54,168 - src.dependency_manager - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 10:10:54,176 - src.dependency_manager - INFO - dependency_manager.py:98 - CodeAnalysisEngine initialized with comprehensive language support
2025-07-10 10:11:06,171 - src.dependency_manager - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 10:11:06,179 - src.dependency_manager - INFO - dependency_manager.py:98 - CodeAnalysisEngine initialized with comprehensive language support
2025-07-10 10:11:48,152 - src.dependency_manager - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 10:11:48,160 - src.dependency_manager - INFO - dependency_manager.py:98 - CodeAnalysisEngine initialized with comprehensive language support
2025-07-10 10:11:48,176 - src.dependency_manager - WARNING - monitoring.py:164 - High CPU usage: 100.0%
2025-07-10 10:11:57,451 - src.dependency_manager - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 10:11:57,459 - src.dependency_manager - INFO - dependency_manager.py:98 - CodeAnalysisEngine initialized with comprehensive language support
2025-07-10 10:12:06,585 - src.dependency_manager - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 10:12:06,593 - src.dependency_manager - INFO - dependency_manager.py:98 - CodeAnalysisEngine initialized with comprehensive language support
2025-07-10 10:12:06,612 - src.dependency_manager - INFO - dependency_manager.py:98 - CodeAnalysisEngine initialized with comprehensive language support
2025-07-10 10:12:06,625 - src.dependency_manager - WARNING - monitoring.py:164 - High CPU usage: 100.0%
2025-07-10 10:12:06,632 - src.dependency_manager - INFO - dependency_manager.py:98 - CodeAnalysisEngine initialized with comprehensive language support
2025-07-10 10:12:06,645 - src.dependency_manager - INFO - dependency_manager.py:98 - CodeAnalysisEngine initialized with comprehensive language support
2025-07-10 10:12:06,657 - src.dependency_manager - INFO - dependency_manager.py:98 - CodeAnalysisEngine initialized with comprehensive language support
2025-07-10 10:12:06,694 - src.dependency_manager - INFO - dependency_manager.py:180 - Analyzed 3 files in C:\Users\<USER>\AppData\Local\Temp\tmpem_h0da5
2025-07-10 10:12:06,701 - src.dependency_manager - INFO - dependency_manager.py:98 - CodeAnalysisEngine initialized with comprehensive language support
2025-07-10 10:12:06,708 - src.dependency_manager - DEBUG - dependency_manager.py:132 - Unsupported file type: .txt
2025-07-10 10:12:06,716 - src.dependency_manager - INFO - dependency_manager.py:98 - CodeAnalysisEngine initialized with comprehensive language support
2025-07-10 10:12:06,722 - src.dependency_manager - INFO - dependency_manager.py:585 - PackageInstaller initialized with multi-manager support
2025-07-10 10:12:06,727 - src.dependency_manager - INFO - dependency_manager.py:719 - Installing Python package: requests>=2.28.0
2025-07-10 10:12:06,729 - src.dependency_manager - INFO - dependency_manager.py:727 - Successfully installed requests
2025-07-10 10:12:06,740 - src.dependency_manager - INFO - dependency_manager.py:585 - PackageInstaller initialized with multi-manager support
2025-07-10 10:12:06,745 - src.dependency_manager - INFO - dependency_manager.py:763 - Installing npm package: react@^18.0.0
2025-07-10 10:12:06,747 - src.dependency_manager - INFO - dependency_manager.py:771 - Successfully installed react
2025-07-10 10:12:06,752 - src.dependency_manager - INFO - dependency_manager.py:585 - PackageInstaller initialized with multi-manager support
2025-07-10 10:12:06,761 - src.dependency_manager - INFO - dependency_manager.py:719 - Installing Python package: nonexistent-package
2025-07-10 10:12:06,762 - src.dependency_manager - ERROR - dependency_manager.py:734 - pip install failed: Package not found
2025-07-10 10:12:06,769 - src.dependency_manager - WARNING - monitoring.py:164 - High CPU usage: 100.0%
2025-07-10 10:12:06,776 - src.dependency_manager - INFO - dependency_manager.py:585 - PackageInstaller initialized with multi-manager support
2025-07-10 10:12:06,782 - src.dependency_manager - DEBUG - dependency_manager.py:603 - Skipping standard library package: os
2025-07-10 10:12:06,791 - src.dependency_manager - INFO - dependency_manager.py:585 - PackageInstaller initialized with multi-manager support
2025-07-10 10:12:06,795 - src.dependency_manager - INFO - dependency_manager.py:646 - Installing 2 packages with pip
2025-07-10 10:12:06,799 - src.dependency_manager - INFO - dependency_manager.py:719 - Installing Python package: requests
2025-07-10 10:12:06,801 - src.dependency_manager - INFO - dependency_manager.py:727 - Successfully installed requests
2025-07-10 10:12:06,807 - src.dependency_manager - INFO - dependency_manager.py:654 - ✓ Successfully installed requests
2025-07-10 10:12:06,812 - src.dependency_manager - INFO - dependency_manager.py:719 - Installing Python package: flask
2025-07-10 10:12:06,814 - src.dependency_manager - INFO - dependency_manager.py:727 - Successfully installed flask
2025-07-10 10:12:06,820 - src.dependency_manager - INFO - dependency_manager.py:654 - ✓ Successfully installed flask
2025-07-10 10:12:06,824 - src.dependency_manager - INFO - dependency_manager.py:646 - Installing 1 packages with npm
2025-07-10 10:12:06,827 - src.dependency_manager - INFO - dependency_manager.py:763 - Installing npm package: react
2025-07-10 10:12:06,828 - src.dependency_manager - INFO - dependency_manager.py:771 - Successfully installed react
2025-07-10 10:12:06,833 - src.dependency_manager - INFO - dependency_manager.py:654 - ✓ Successfully installed react
2025-07-10 10:12:06,839 - src.dependency_manager - INFO - dependency_manager.py:860 - ConflictResolver initialized with compatibility rules
2025-07-10 10:12:06,844 - src.dependency_manager - INFO - dependency_manager.py:890 - Detected 1 potential conflicts
2025-07-10 10:12:06,849 - src.dependency_manager - INFO - dependency_manager.py:860 - ConflictResolver initialized with compatibility rules
2025-07-10 10:12:06,856 - src.dependency_manager - INFO - dependency_manager.py:890 - Detected 0 potential conflicts
2025-07-10 10:12:06,862 - src.dependency_manager - INFO - dependency_manager.py:860 - ConflictResolver initialized with compatibility rules
2025-07-10 10:12:06,867 - src.dependency_manager - INFO - dependency_manager.py:890 - Detected 2 potential conflicts
2025-07-10 10:12:06,876 - src.dependency_manager - INFO - dependency_manager.py:860 - ConflictResolver initialized with compatibility rules
2025-07-10 10:12:06,878 - src.dependency_manager - INFO - dependency_manager.py:914 - Proposed resolution for requests: 2.30.0
2025-07-10 10:12:06,882 - src.dependency_manager - INFO - dependency_manager.py:860 - ConflictResolver initialized with compatibility rules
2025-07-10 10:12:06,890 - src.dependency_manager - INFO - dependency_manager.py:1419 - DependencyCache initialized with cache dir: C:\Users\<USER>\AppData\Local\Temp\tmpo210zd00\cache
2025-07-10 10:12:06,900 - src.dependency_manager - INFO - dependency_manager.py:1419 - DependencyCache initialized with cache dir: C:\Users\<USER>\AppData\Local\Temp\tmpkhbdqcgn\cache
2025-07-10 10:12:07,057 - src.dependency_manager - INFO - dependency_manager.py:1419 - DependencyCache initialized with cache dir: C:\Users\<USER>\AppData\Local\Temp\tmpijg4cv68\cache
2025-07-10 10:12:07,062 - src.dependency_manager - INFO - dependency_manager.py:1419 - DependencyCache initialized with cache dir: C:\Users\<USER>\AppData\Local\Temp\tmp6stad_6a\cache
2025-07-10 10:12:07,073 - src.dependency_manager - INFO - dependency_manager.py:1419 - DependencyCache initialized with cache dir: C:\Users\<USER>\AppData\Local\Temp\tmpxukbn5od\cache
2025-07-10 10:12:07,079 - src.dependency_manager - INFO - dependency_manager.py:1529 - Cache cleared successfully
2025-07-10 10:12:07,086 - src.dependency_manager - INFO - dependency_manager.py:98 - CodeAnalysisEngine initialized with comprehensive language support
2025-07-10 10:12:07,093 - src.dependency_manager - INFO - dependency_manager.py:585 - PackageInstaller initialized with multi-manager support
2025-07-10 10:12:07,095 - src.dependency_manager - INFO - dependency_manager.py:860 - ConflictResolver initialized with compatibility rules
2025-07-10 10:12:07,097 - src.dependency_manager - INFO - dependency_manager.py:1106 - DependencyManager initialized with all components
2025-07-10 10:12:07,102 - src.dependency_manager - INFO - dependency_manager.py:1127 - Analyzing dependencies in C:\Users\<USER>\AppData\Local\Temp\tmpc1twigct
2025-07-10 10:12:07,117 - src.dependency_manager - WARNING - monitoring.py:164 - High CPU usage: 100.0%
2025-07-10 10:12:07,130 - src.dependency_manager - INFO - dependency_manager.py:180 - Analyzed 2 files in C:\Users\<USER>\AppData\Local\Temp\tmpc1twigct
2025-07-10 10:12:07,138 - src.dependency_manager - WARNING - monitoring.py:164 - High CPU usage: 100.0%
2025-07-10 10:12:07,140 - src.dependency_manager - INFO - dependency_manager.py:1138 - Found 3 unique dependencies
2025-07-10 10:12:07,143 - src.dependency_manager - INFO - dependency_manager.py:890 - Detected 0 potential conflicts
2025-07-10 10:12:07,146 - src.dependency_manager - INFO - dependency_manager.py:1164 - Installing 3 packages
2025-07-10 10:12:07,148 - src.dependency_manager - INFO - dependency_manager.py:1337 - Added 1 packages to requirements.txt
2025-07-10 10:12:07,157 - src.dependency_manager - INFO - dependency_manager.py:98 - CodeAnalysisEngine initialized with comprehensive language support
2025-07-10 10:12:07,159 - src.dependency_manager - INFO - dependency_manager.py:585 - PackageInstaller initialized with multi-manager support
2025-07-10 10:12:07,160 - src.dependency_manager - INFO - dependency_manager.py:860 - ConflictResolver initialized with compatibility rules
2025-07-10 10:12:07,162 - src.dependency_manager - INFO - dependency_manager.py:1106 - DependencyManager initialized with all components
2025-07-10 10:12:07,181 - src.dependency_manager - INFO - dependency_manager.py:180 - Analyzed 2 files in C:\Users\<USER>\AppData\Local\Temp\tmpbg86fdy7
2025-07-10 10:12:07,189 - src.dependency_manager - INFO - dependency_manager.py:98 - CodeAnalysisEngine initialized with comprehensive language support
2025-07-10 10:12:07,191 - src.dependency_manager - INFO - dependency_manager.py:585 - PackageInstaller initialized with multi-manager support
2025-07-10 10:12:07,192 - src.dependency_manager - INFO - dependency_manager.py:860 - ConflictResolver initialized with compatibility rules
2025-07-10 10:12:07,194 - src.dependency_manager - INFO - dependency_manager.py:1106 - DependencyManager initialized with all components
2025-07-10 10:12:07,195 - src.dependency_manager - INFO - dependency_manager.py:1419 - DependencyCache initialized with cache dir: C:\Users\<USER>\AppData\Local\Temp\tmp3e9cjelt\cache
2025-07-10 10:12:07,197 - src.dependency_manager - INFO - dependency_manager.py:98 - CodeAnalysisEngine initialized with comprehensive language support
2025-07-10 10:12:07,202 - src.dependency_manager - INFO - dependency_manager.py:1626 - CachedCodeAnalysisEngine initialized
2025-07-10 10:12:07,206 - src.dependency_manager - INFO - dependency_manager.py:1615 - EnhancedDependencyManager initialized with caching support
2025-07-10 10:12:07,211 - src.dependency_manager - DEBUG - dependency_manager.py:1639 - Cache miss for C:\Users\<USER>\AppData\Local\Temp\tmp3e9cjelt\test.py, analyzing...
2025-07-10 10:12:07,224 - src.dependency_manager - DEBUG - dependency_manager.py:1635 - Cache hit for C:\Users\<USER>\AppData\Local\Temp\tmp3e9cjelt\test.py
2025-07-10 10:12:32,871 - src.dependency_manager - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 10:12:32,876 - src.dependency_manager - INFO - dependency_manager.py:98 - CodeAnalysisEngine initialized with comprehensive language support
2025-07-10 10:12:32,889 - src.dependency_manager - INFO - dependency_manager.py:98 - CodeAnalysisEngine initialized with comprehensive language support
2025-07-10 10:12:32,906 - src.dependency_manager - INFO - dependency_manager.py:98 - CodeAnalysisEngine initialized with comprehensive language support
2025-07-10 10:12:32,917 - src.dependency_manager - INFO - dependency_manager.py:98 - CodeAnalysisEngine initialized with comprehensive language support
2025-07-10 10:12:32,930 - src.dependency_manager - INFO - dependency_manager.py:98 - CodeAnalysisEngine initialized with comprehensive language support
2025-07-10 10:12:32,967 - src.dependency_manager - INFO - dependency_manager.py:180 - Analyzed 3 files in C:\Users\<USER>\AppData\Local\Temp\tmpq0xay1y0
2025-07-10 10:12:32,973 - src.dependency_manager - WARNING - monitoring.py:164 - High CPU usage: 100.0%
2025-07-10 10:12:32,980 - src.dependency_manager - INFO - dependency_manager.py:98 - CodeAnalysisEngine initialized with comprehensive language support
2025-07-10 10:12:32,986 - src.dependency_manager - DEBUG - dependency_manager.py:132 - Unsupported file type: .txt
2025-07-10 10:12:32,992 - src.dependency_manager - WARNING - monitoring.py:164 - High CPU usage: 100.0%
2025-07-10 10:12:32,996 - src.dependency_manager - INFO - dependency_manager.py:98 - CodeAnalysisEngine initialized with comprehensive language support
2025-07-10 10:12:33,001 - src.dependency_manager - INFO - dependency_manager.py:585 - PackageInstaller initialized with multi-manager support
2025-07-10 10:12:33,009 - src.dependency_manager - INFO - dependency_manager.py:719 - Installing Python package: requests>=2.28.0
2025-07-10 10:12:33,010 - src.dependency_manager - INFO - dependency_manager.py:727 - Successfully installed requests
2025-07-10 10:12:33,131 - src.dependency_manager - INFO - dependency_manager.py:585 - PackageInstaller initialized with multi-manager support
2025-07-10 10:12:33,150 - src.dependency_manager - INFO - dependency_manager.py:763 - Installing npm package: react@^18.0.0
2025-07-10 10:12:33,152 - src.dependency_manager - INFO - dependency_manager.py:771 - Successfully installed react
2025-07-10 10:12:33,165 - src.dependency_manager - INFO - dependency_manager.py:585 - PackageInstaller initialized with multi-manager support
2025-07-10 10:12:33,182 - src.dependency_manager - INFO - dependency_manager.py:719 - Installing Python package: nonexistent-package
2025-07-10 10:12:33,184 - src.dependency_manager - ERROR - dependency_manager.py:734 - pip install failed: Package not found
2025-07-10 10:12:33,191 - src.dependency_manager - INFO - dependency_manager.py:585 - PackageInstaller initialized with multi-manager support
2025-07-10 10:12:33,196 - src.dependency_manager - DEBUG - dependency_manager.py:603 - Skipping standard library package: os
2025-07-10 10:12:33,207 - src.dependency_manager - INFO - dependency_manager.py:585 - PackageInstaller initialized with multi-manager support
2025-07-10 10:12:33,214 - src.dependency_manager - INFO - dependency_manager.py:646 - Installing 2 packages with pip
2025-07-10 10:12:33,218 - src.dependency_manager - INFO - dependency_manager.py:719 - Installing Python package: requests
2025-07-10 10:12:33,220 - src.dependency_manager - INFO - dependency_manager.py:727 - Successfully installed requests
2025-07-10 10:12:33,224 - src.dependency_manager - INFO - dependency_manager.py:654 - ✓ Successfully installed requests
2025-07-10 10:12:33,228 - src.dependency_manager - INFO - dependency_manager.py:719 - Installing Python package: flask
2025-07-10 10:12:33,231 - src.dependency_manager - INFO - dependency_manager.py:727 - Successfully installed flask
2025-07-10 10:12:33,239 - src.dependency_manager - INFO - dependency_manager.py:654 - ✓ Successfully installed flask
2025-07-10 10:12:33,241 - src.dependency_manager - INFO - dependency_manager.py:646 - Installing 1 packages with npm
2025-07-10 10:12:33,244 - src.dependency_manager - INFO - dependency_manager.py:763 - Installing npm package: react
2025-07-10 10:12:33,247 - src.dependency_manager - INFO - dependency_manager.py:771 - Successfully installed react
2025-07-10 10:12:33,251 - src.dependency_manager - INFO - dependency_manager.py:654 - ✓ Successfully installed react
2025-07-10 10:12:33,257 - src.dependency_manager - INFO - dependency_manager.py:860 - ConflictResolver initialized with compatibility rules
2025-07-10 10:12:33,261 - src.dependency_manager - INFO - dependency_manager.py:890 - Detected 1 potential conflicts
2025-07-10 10:12:33,268 - src.dependency_manager - INFO - dependency_manager.py:860 - ConflictResolver initialized with compatibility rules
2025-07-10 10:12:33,275 - src.dependency_manager - INFO - dependency_manager.py:890 - Detected 0 potential conflicts
2025-07-10 10:12:33,288 - src.dependency_manager - INFO - dependency_manager.py:860 - ConflictResolver initialized with compatibility rules
2025-07-10 10:12:33,293 - src.dependency_manager - INFO - dependency_manager.py:890 - Detected 2 potential conflicts
2025-07-10 10:12:33,302 - src.dependency_manager - WARNING - monitoring.py:164 - High CPU usage: 100.0%
2025-07-10 10:12:33,307 - src.dependency_manager - INFO - dependency_manager.py:860 - ConflictResolver initialized with compatibility rules
2025-07-10 10:12:33,309 - src.dependency_manager - INFO - dependency_manager.py:914 - Proposed resolution for requests: 2.30.0
2025-07-10 10:12:33,316 - src.dependency_manager - INFO - dependency_manager.py:860 - ConflictResolver initialized with compatibility rules
2025-07-10 10:12:33,323 - src.dependency_manager - INFO - dependency_manager.py:1419 - DependencyCache initialized with cache dir: C:\Users\<USER>\AppData\Local\Temp\tmpj62zs7z9\cache
2025-07-10 10:12:33,333 - src.dependency_manager - INFO - dependency_manager.py:1419 - DependencyCache initialized with cache dir: C:\Users\<USER>\AppData\Local\Temp\tmpff0tsb9r\cache
2025-07-10 10:12:33,350 - src.dependency_manager - INFO - dependency_manager.py:1529 - Cache cleared successfully
2025-07-10 10:12:33,357 - src.dependency_manager - INFO - dependency_manager.py:1419 - DependencyCache initialized with cache dir: C:\Users\<USER>\AppData\Local\Temp\tmpd44bnhkj\cache
2025-07-10 10:12:33,364 - src.dependency_manager - INFO - dependency_manager.py:1419 - DependencyCache initialized with cache dir: C:\Users\<USER>\AppData\Local\Temp\tmpu9szwf0g\cache
2025-07-10 10:12:33,373 - src.dependency_manager - INFO - dependency_manager.py:1419 - DependencyCache initialized with cache dir: C:\Users\<USER>\AppData\Local\Temp\tmp95i15_54\cache
2025-07-10 10:12:33,382 - src.dependency_manager - INFO - dependency_manager.py:1529 - Cache cleared successfully
2025-07-10 10:12:33,389 - src.dependency_manager - INFO - dependency_manager.py:98 - CodeAnalysisEngine initialized with comprehensive language support
2025-07-10 10:12:33,390 - src.dependency_manager - INFO - dependency_manager.py:585 - PackageInstaller initialized with multi-manager support
2025-07-10 10:12:33,392 - src.dependency_manager - INFO - dependency_manager.py:860 - ConflictResolver initialized with compatibility rules
2025-07-10 10:12:33,395 - src.dependency_manager - INFO - dependency_manager.py:1106 - DependencyManager initialized with all components
2025-07-10 10:12:33,401 - src.dependency_manager - INFO - dependency_manager.py:1127 - Analyzing dependencies in C:\Users\<USER>\AppData\Local\Temp\tmpk_symvwz
2025-07-10 10:12:33,421 - src.dependency_manager - INFO - dependency_manager.py:180 - Analyzed 2 files in C:\Users\<USER>\AppData\Local\Temp\tmpk_symvwz
2025-07-10 10:12:33,425 - src.dependency_manager - INFO - dependency_manager.py:1138 - Found 3 unique dependencies
2025-07-10 10:12:33,430 - src.dependency_manager - INFO - dependency_manager.py:890 - Detected 0 potential conflicts
2025-07-10 10:12:33,434 - src.dependency_manager - INFO - dependency_manager.py:1164 - Installing 3 packages
2025-07-10 10:12:33,437 - src.dependency_manager - INFO - dependency_manager.py:1337 - Added 1 packages to requirements.txt
2025-07-10 10:12:33,441 - src.dependency_manager - WARNING - monitoring.py:164 - High CPU usage: 100.0%
2025-07-10 10:12:33,445 - src.dependency_manager - INFO - dependency_manager.py:98 - CodeAnalysisEngine initialized with comprehensive language support
2025-07-10 10:12:33,446 - src.dependency_manager - INFO - dependency_manager.py:585 - PackageInstaller initialized with multi-manager support
2025-07-10 10:12:33,448 - src.dependency_manager - INFO - dependency_manager.py:860 - ConflictResolver initialized with compatibility rules
2025-07-10 10:12:33,449 - src.dependency_manager - INFO - dependency_manager.py:1106 - DependencyManager initialized with all components
2025-07-10 10:12:33,471 - src.dependency_manager - INFO - dependency_manager.py:180 - Analyzed 2 files in C:\Users\<USER>\AppData\Local\Temp\tmpy1jx18lj
2025-07-10 10:12:33,475 - src.dependency_manager - WARNING - monitoring.py:164 - High CPU usage: 100.0%
2025-07-10 10:12:33,482 - src.dependency_manager - INFO - dependency_manager.py:98 - CodeAnalysisEngine initialized with comprehensive language support
2025-07-10 10:12:33,483 - src.dependency_manager - INFO - dependency_manager.py:585 - PackageInstaller initialized with multi-manager support
2025-07-10 10:12:33,485 - src.dependency_manager - INFO - dependency_manager.py:860 - ConflictResolver initialized with compatibility rules
2025-07-10 10:12:33,488 - src.dependency_manager - INFO - dependency_manager.py:1106 - DependencyManager initialized with all components
2025-07-10 10:12:33,490 - src.dependency_manager - INFO - dependency_manager.py:1419 - DependencyCache initialized with cache dir: C:\Users\<USER>\AppData\Local\Temp\tmpk_y2x7qt\cache
2025-07-10 10:12:33,492 - src.dependency_manager - INFO - dependency_manager.py:98 - CodeAnalysisEngine initialized with comprehensive language support
2025-07-10 10:12:33,493 - src.dependency_manager - INFO - dependency_manager.py:1626 - CachedCodeAnalysisEngine initialized
2025-07-10 10:12:33,494 - src.dependency_manager - INFO - dependency_manager.py:1615 - EnhancedDependencyManager initialized with caching support
2025-07-10 10:12:33,497 - src.dependency_manager - DEBUG - dependency_manager.py:1639 - Cache miss for C:\Users\<USER>\AppData\Local\Temp\tmpk_y2x7qt\test.py, analyzing...
2025-07-10 10:12:33,507 - src.dependency_manager - DEBUG - dependency_manager.py:1635 - Cache hit for C:\Users\<USER>\AppData\Local\Temp\tmpk_y2x7qt\test.py
2025-07-10 10:40:47,248 - src.database_setup - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 10:40:48,656 - src.database_setup - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 10:40:48,657 - src.database_setup - INFO - database_setup.py:137 - ProjectDatabaseAnalyzer initialized with intelligent pattern recognition
2025-07-10 10:40:49,211 - src.database_setup - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 10:40:49,212 - src.database_setup - INFO - database_setup.py:137 - ProjectDatabaseAnalyzer initialized with intelligent pattern recognition
2025-07-10 10:40:49,634 - src.database_setup - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 10:40:49,636 - src.database_setup - INFO - database_setup.py:137 - ProjectDatabaseAnalyzer initialized with intelligent pattern recognition
2025-07-10 10:40:49,643 - src.database_setup - INFO - database_setup.py:172 - Analyzed database requirements: 2 entities, 0 relationships
2025-07-10 10:40:50,068 - src.database_setup - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 10:40:50,071 - src.database_setup - INFO - database_setup.py:137 - ProjectDatabaseAnalyzer initialized with intelligent pattern recognition
2025-07-10 10:40:50,076 - src.database_setup - INFO - database_setup.py:686 - DatabaseSelector initialized with intelligent selection criteria
2025-07-10 10:40:50,080 - src.database_setup - DEBUG - database_setup.py:710 - sqlite score: 10.0
2025-07-10 10:40:50,081 - src.database_setup - DEBUG - database_setup.py:710 - postgresql score: 8.0
2025-07-10 10:40:50,083 - src.database_setup - DEBUG - database_setup.py:710 - mysql score: 7.0
2025-07-10 10:40:50,085 - src.database_setup - DEBUG - database_setup.py:710 - mongodb score: 6.5
2025-07-10 10:40:50,086 - src.database_setup - INFO - database_setup.py:715 - Selected database type: sqlite (score: 10.0)
2025-07-10 10:40:50,096 - src.database_setup - INFO - database_setup.py:686 - DatabaseSelector initialized with intelligent selection criteria
2025-07-10 10:40:50,101 - src.database_setup - DEBUG - database_setup.py:710 - sqlite score: 5.0
2025-07-10 10:40:50,103 - src.database_setup - DEBUG - database_setup.py:710 - postgresql score: 11.0
2025-07-10 10:40:50,106 - src.database_setup - DEBUG - database_setup.py:710 - mysql score: 9.0
2025-07-10 10:40:50,108 - src.database_setup - DEBUG - database_setup.py:710 - mongodb score: 9.0
2025-07-10 10:40:50,109 - src.database_setup - INFO - database_setup.py:715 - Selected database type: postgresql (score: 11.0)
2025-07-10 10:40:50,115 - src.database_setup - INFO - database_setup.py:686 - DatabaseSelector initialized with intelligent selection criteria
2025-07-10 10:40:50,547 - src.database_setup - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 10:40:50,548 - src.database_setup - INFO - database_setup.py:905 - SchemaGenerator initialized with SQL generation capabilities
2025-07-10 10:40:50,553 - src.database_setup - INFO - database_setup.py:953 - Generated schema with 2 tables, 1 relationships, 4 indexes
2025-07-10 10:40:50,999 - src.database_setup - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 10:40:51,001 - src.database_setup - INFO - database_setup.py:905 - SchemaGenerator initialized with SQL generation capabilities
2025-07-10 10:40:51,428 - src.database_setup - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 10:40:51,430 - src.database_setup - INFO - database_setup.py:905 - SchemaGenerator initialized with SQL generation capabilities
2025-07-10 10:40:51,434 - src.database_setup - INFO - database_setup.py:1350 - MigrationManager initialized for project: C:\Users\<USER>\AppData\Local\Temp\tmp_h18yx5w
2025-07-10 10:40:51,440 - src.database_setup - INFO - database_setup.py:1382 - Created migration: 20250710_104051_create_users_table.sql
2025-07-10 10:40:51,458 - src.database_setup - INFO - database_setup.py:1350 - MigrationManager initialized for project: C:\Users\<USER>\AppData\Local\Temp\tmp5g0rqory
2025-07-10 10:40:51,470 - src.database_setup - INFO - database_setup.py:1732 - DatabaseConnectionManager initialized for project: C:\Users\<USER>\AppData\Local\Temp\tmpka2deh8c
2025-07-10 10:40:51,478 - src.database_setup - INFO - database_setup.py:1985 - Generated config file: C:\Users\<USER>\AppData\Local\Temp\tmpka2deh8c\config\database_development.py
2025-07-10 10:40:51,481 - src.database_setup - INFO - database_setup.py:1985 - Generated config file: C:\Users\<USER>\AppData\Local\Temp\tmpka2deh8c\config\database_testing.py
2025-07-10 10:40:51,487 - src.database_setup - INFO - database_setup.py:1985 - Generated config file: C:\Users\<USER>\AppData\Local\Temp\tmpka2deh8c\config\database_production.py
2025-07-10 10:40:51,495 - src.database_setup - INFO - database_setup.py:1776 - Generated 7 configuration files
2025-07-10 10:40:51,506 - src.database_setup - INFO - database_setup.py:1732 - DatabaseConnectionManager initialized for project: C:\Users\<USER>\AppData\Local\Temp\tmpmu871nil
2025-07-10 10:40:51,510 - src.database_setup - INFO - database_setup.py:1810 - Created SQLite database: C:\Users\<USER>\AppData\Local\Temp\tmpmu871nil\test.db
2025-07-10 10:40:51,514 - src.database_setup - INFO - database_setup.py:1732 - DatabaseConnectionManager initialized for project: C:\Users\<USER>\AppData\Local\Temp\tmpfid4czyp
2025-07-10 10:40:51,524 - src.database_setup - INFO - database_setup.py:1810 - Created SQLite database: C:\Users\<USER>\AppData\Local\Temp\tmpfid4czyp\test.db
2025-07-10 10:40:51,526 - src.database_setup - INFO - database_setup.py:1850 - SQLite connection test successful
2025-07-10 10:42:18,733 - src.database_setup - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 10:42:19,924 - src.database_setup - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 10:42:19,926 - src.database_setup - INFO - database_setup.py:137 - ProjectDatabaseAnalyzer initialized with intelligent pattern recognition
2025-07-10 10:42:29,629 - src.database_setup - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 10:42:29,636 - src.database_setup - INFO - database_setup.py:686 - DatabaseSelector initialized with intelligent selection criteria
2025-07-10 10:42:29,647 - src.database_setup - DEBUG - database_setup.py:710 - sqlite score: 10.0
2025-07-10 10:42:29,648 - src.database_setup - DEBUG - database_setup.py:710 - postgresql score: 8.0
2025-07-10 10:42:29,649 - src.database_setup - DEBUG - database_setup.py:710 - mysql score: 7.0
2025-07-10 10:42:29,650 - src.database_setup - DEBUG - database_setup.py:710 - mongodb score: 6.5
2025-07-10 10:42:29,652 - src.database_setup - INFO - database_setup.py:715 - Selected database type: sqlite (score: 10.0)
2025-07-10 10:42:39,474 - src.database_setup - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 10:42:40,668 - src.database_setup - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 10:42:40,670 - src.database_setup - INFO - database_setup.py:137 - ProjectDatabaseAnalyzer initialized with intelligent pattern recognition
2025-07-10 10:42:41,204 - src.database_setup - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 10:42:41,205 - src.database_setup - INFO - database_setup.py:137 - ProjectDatabaseAnalyzer initialized with intelligent pattern recognition
2025-07-10 10:42:41,601 - src.database_setup - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 10:42:41,603 - src.database_setup - INFO - database_setup.py:137 - ProjectDatabaseAnalyzer initialized with intelligent pattern recognition
2025-07-10 10:42:41,610 - src.database_setup - INFO - database_setup.py:172 - Analyzed database requirements: 2 entities, 0 relationships
2025-07-10 10:42:42,010 - src.database_setup - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 10:42:42,012 - src.database_setup - INFO - database_setup.py:137 - ProjectDatabaseAnalyzer initialized with intelligent pattern recognition
2025-07-10 10:42:42,016 - src.database_setup - INFO - database_setup.py:686 - DatabaseSelector initialized with intelligent selection criteria
2025-07-10 10:42:42,021 - src.database_setup - DEBUG - database_setup.py:710 - sqlite score: 10.0
2025-07-10 10:42:42,022 - src.database_setup - DEBUG - database_setup.py:710 - postgresql score: 8.0
2025-07-10 10:42:42,024 - src.database_setup - DEBUG - database_setup.py:710 - mysql score: 7.0
2025-07-10 10:42:42,025 - src.database_setup - DEBUG - database_setup.py:710 - mongodb score: 6.5
2025-07-10 10:42:42,026 - src.database_setup - INFO - database_setup.py:715 - Selected database type: sqlite (score: 10.0)
2025-07-10 10:42:42,032 - src.database_setup - INFO - database_setup.py:686 - DatabaseSelector initialized with intelligent selection criteria
2025-07-10 10:42:42,037 - src.database_setup - DEBUG - database_setup.py:710 - sqlite score: 5.0
2025-07-10 10:42:42,039 - src.database_setup - DEBUG - database_setup.py:710 - postgresql score: 11.0
2025-07-10 10:42:42,041 - src.database_setup - DEBUG - database_setup.py:710 - mysql score: 9.0
2025-07-10 10:42:42,050 - src.database_setup - DEBUG - database_setup.py:710 - mongodb score: 9.0
2025-07-10 10:42:42,051 - src.database_setup - INFO - database_setup.py:715 - Selected database type: postgresql (score: 11.0)
2025-07-10 10:42:42,059 - src.database_setup - INFO - database_setup.py:686 - DatabaseSelector initialized with intelligent selection criteria
2025-07-10 10:42:42,473 - src.database_setup - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 10:42:42,474 - src.database_setup - INFO - database_setup.py:905 - SchemaGenerator initialized with SQL generation capabilities
2025-07-10 10:42:42,478 - src.database_setup - INFO - database_setup.py:953 - Generated schema with 2 tables, 1 relationships, 4 indexes
2025-07-10 10:42:42,871 - src.database_setup - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 10:42:42,873 - src.database_setup - INFO - database_setup.py:905 - SchemaGenerator initialized with SQL generation capabilities
2025-07-10 10:42:43,271 - src.database_setup - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 10:42:43,273 - src.database_setup - INFO - database_setup.py:905 - SchemaGenerator initialized with SQL generation capabilities
2025-07-10 10:42:43,277 - src.database_setup - INFO - database_setup.py:1350 - MigrationManager initialized for project: C:\Users\<USER>\AppData\Local\Temp\tmp9kzyoxf7
2025-07-10 10:42:43,282 - src.database_setup - INFO - database_setup.py:1382 - Created migration: 20250710_104243_create_users_table.sql
2025-07-10 10:42:43,298 - src.database_setup - INFO - database_setup.py:1350 - MigrationManager initialized for project: C:\Users\<USER>\AppData\Local\Temp\tmpey_6cyj2
2025-07-10 10:42:43,307 - src.database_setup - INFO - database_setup.py:1732 - DatabaseConnectionManager initialized for project: C:\Users\<USER>\AppData\Local\Temp\tmpv62viixe
2025-07-10 10:42:43,314 - src.database_setup - INFO - database_setup.py:1985 - Generated config file: C:\Users\<USER>\AppData\Local\Temp\tmpv62viixe\config\database_development.py
2025-07-10 10:42:43,317 - src.database_setup - INFO - database_setup.py:1985 - Generated config file: C:\Users\<USER>\AppData\Local\Temp\tmpv62viixe\config\database_testing.py
2025-07-10 10:42:43,322 - src.database_setup - INFO - database_setup.py:1985 - Generated config file: C:\Users\<USER>\AppData\Local\Temp\tmpv62viixe\config\database_production.py
2025-07-10 10:42:43,327 - src.database_setup - INFO - database_setup.py:1776 - Generated 7 configuration files
2025-07-10 10:42:43,336 - src.database_setup - INFO - database_setup.py:1732 - DatabaseConnectionManager initialized for project: C:\Users\<USER>\AppData\Local\Temp\tmpwyj7pc4o
2025-07-10 10:42:43,341 - src.database_setup - INFO - database_setup.py:1810 - Created SQLite database: C:\Users\<USER>\AppData\Local\Temp\tmpwyj7pc4o\test.db
2025-07-10 10:42:43,346 - src.database_setup - INFO - database_setup.py:1732 - DatabaseConnectionManager initialized for project: C:\Users\<USER>\AppData\Local\Temp\tmp2zh8kqmt
2025-07-10 10:42:43,349 - src.database_setup - INFO - database_setup.py:1810 - Created SQLite database: C:\Users\<USER>\AppData\Local\Temp\tmp2zh8kqmt\test.db
2025-07-10 10:42:43,352 - src.database_setup - INFO - database_setup.py:1850 - SQLite connection test successful
2025-07-10 10:43:18,541 - src.database_setup - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 10:43:19,812 - src.database_setup - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 10:43:19,814 - src.database_setup - INFO - database_setup.py:137 - ProjectDatabaseAnalyzer initialized with intelligent pattern recognition
2025-07-10 10:44:12,139 - src.database_setup - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 10:44:13,534 - src.database_setup - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 10:44:13,537 - src.database_setup - INFO - database_setup.py:137 - ProjectDatabaseAnalyzer initialized with intelligent pattern recognition
2025-07-10 10:44:23,404 - src.database_setup - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 10:44:24,587 - src.database_setup - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 10:44:24,589 - src.database_setup - INFO - database_setup.py:137 - ProjectDatabaseAnalyzer initialized with intelligent pattern recognition
2025-07-10 10:44:34,623 - src.database_setup - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 10:44:35,820 - src.database_setup - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 10:44:35,822 - src.database_setup - INFO - database_setup.py:137 - ProjectDatabaseAnalyzer initialized with intelligent pattern recognition
2025-07-10 10:44:36,229 - src.database_setup - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 10:44:36,230 - src.database_setup - INFO - database_setup.py:137 - ProjectDatabaseAnalyzer initialized with intelligent pattern recognition
2025-07-10 10:44:36,634 - src.database_setup - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 10:44:36,635 - src.database_setup - INFO - database_setup.py:137 - ProjectDatabaseAnalyzer initialized with intelligent pattern recognition
2025-07-10 10:44:36,642 - src.database_setup - INFO - database_setup.py:172 - Analyzed database requirements: 2 entities, 0 relationships
2025-07-10 10:44:37,043 - src.database_setup - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 10:44:37,045 - src.database_setup - INFO - database_setup.py:137 - ProjectDatabaseAnalyzer initialized with intelligent pattern recognition
2025-07-10 10:44:37,049 - src.database_setup - INFO - database_setup.py:686 - DatabaseSelector initialized with intelligent selection criteria
2025-07-10 10:44:37,054 - src.database_setup - DEBUG - database_setup.py:710 - sqlite score: 10.0
2025-07-10 10:44:37,056 - src.database_setup - DEBUG - database_setup.py:710 - postgresql score: 8.0
2025-07-10 10:44:37,057 - src.database_setup - DEBUG - database_setup.py:710 - mysql score: 7.0
2025-07-10 10:44:37,058 - src.database_setup - DEBUG - database_setup.py:710 - mongodb score: 6.5
2025-07-10 10:44:37,060 - src.database_setup - INFO - database_setup.py:715 - Selected database type: sqlite (score: 10.0)
2025-07-10 10:44:37,065 - src.database_setup - INFO - database_setup.py:686 - DatabaseSelector initialized with intelligent selection criteria
2025-07-10 10:44:37,070 - src.database_setup - DEBUG - database_setup.py:710 - sqlite score: 5.0
2025-07-10 10:44:37,072 - src.database_setup - DEBUG - database_setup.py:710 - postgresql score: 11.0
2025-07-10 10:44:37,073 - src.database_setup - DEBUG - database_setup.py:710 - mysql score: 9.0
2025-07-10 10:44:37,075 - src.database_setup - DEBUG - database_setup.py:710 - mongodb score: 9.0
2025-07-10 10:44:37,078 - src.database_setup - INFO - database_setup.py:715 - Selected database type: postgresql (score: 11.0)
2025-07-10 10:44:37,083 - src.database_setup - INFO - database_setup.py:686 - DatabaseSelector initialized with intelligent selection criteria
2025-07-10 10:44:37,502 - src.database_setup - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 10:44:37,504 - src.database_setup - INFO - database_setup.py:905 - SchemaGenerator initialized with SQL generation capabilities
2025-07-10 10:44:37,508 - src.database_setup - INFO - database_setup.py:953 - Generated schema with 2 tables, 1 relationships, 4 indexes
2025-07-10 10:44:37,898 - src.database_setup - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 10:44:37,900 - src.database_setup - INFO - database_setup.py:905 - SchemaGenerator initialized with SQL generation capabilities
2025-07-10 10:44:38,307 - src.database_setup - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 10:44:38,309 - src.database_setup - INFO - database_setup.py:905 - SchemaGenerator initialized with SQL generation capabilities
2025-07-10 10:44:38,313 - src.database_setup - INFO - database_setup.py:1350 - MigrationManager initialized for project: C:\Users\<USER>\AppData\Local\Temp\tmp_vw48piv
2025-07-10 10:44:38,318 - src.database_setup - INFO - database_setup.py:1382 - Created migration: 20250710_104438_create_users_table.sql
2025-07-10 10:44:38,334 - src.database_setup - INFO - database_setup.py:1350 - MigrationManager initialized for project: C:\Users\<USER>\AppData\Local\Temp\tmpwwps2uh_
2025-07-10 10:44:38,341 - src.database_setup - INFO - database_setup.py:1732 - DatabaseConnectionManager initialized for project: C:\Users\<USER>\AppData\Local\Temp\tmp57ts463o
2025-07-10 10:44:38,348 - src.database_setup - INFO - database_setup.py:1985 - Generated config file: C:\Users\<USER>\AppData\Local\Temp\tmp57ts463o\config\database_development.py
2025-07-10 10:44:38,353 - src.database_setup - INFO - database_setup.py:1985 - Generated config file: C:\Users\<USER>\AppData\Local\Temp\tmp57ts463o\config\database_testing.py
2025-07-10 10:44:38,357 - src.database_setup - INFO - database_setup.py:1985 - Generated config file: C:\Users\<USER>\AppData\Local\Temp\tmp57ts463o\config\database_production.py
2025-07-10 10:44:38,363 - src.database_setup - INFO - database_setup.py:1776 - Generated 7 configuration files
2025-07-10 10:44:38,373 - src.database_setup - INFO - database_setup.py:1732 - DatabaseConnectionManager initialized for project: C:\Users\<USER>\AppData\Local\Temp\tmpciv86xcq
2025-07-10 10:44:38,376 - src.database_setup - INFO - database_setup.py:1810 - Created SQLite database: C:\Users\<USER>\AppData\Local\Temp\tmpciv86xcq\test.db
2025-07-10 10:44:38,381 - src.database_setup - INFO - database_setup.py:1732 - DatabaseConnectionManager initialized for project: C:\Users\<USER>\AppData\Local\Temp\tmpoqwjfsix
2025-07-10 10:44:38,383 - src.database_setup - INFO - database_setup.py:1810 - Created SQLite database: C:\Users\<USER>\AppData\Local\Temp\tmpoqwjfsix\test.db
2025-07-10 10:44:38,386 - src.database_setup - INFO - database_setup.py:1850 - SQLite connection test successful
2025-07-10 14:08:15,531 - src.agent - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 14:08:15,552 - src.agent - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 14:08:15,554 - src.agent - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 14:08:15,559 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 14:08:15,560 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: c2f6e460)
2025-07-10 14:08:15,965 - src.agent - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 14:08:15,968 - src.agent - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 14:08:15,969 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 14:08:15,971 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: fe2be9b7)
2025-07-10 14:08:15,972 - src.agent - INFO - security.py:328 - SECURITY: code_generation_request - {'task_length': 29, 'language': 'python', 'session_id': 'fe2be9b7-3c25-489b-8a3f-8fb5ce7251a0'}
2025-07-10 14:08:15,973 - src.agent - INFO - agent.py:88 - Generating python code for: create a hello world function
2025-07-10 14:08:15,975 - src.agent - INFO - conversation.py:176 - Created new conversation: f5f1366b-df0e-4313-87c0-fe7cd1189a3d - New Conversation
2025-07-10 14:08:15,976 - src.agent - INFO - conversation.py:205 - Added user message to conversation f5f1366b-df0e-4313-87c0-fe7cd1189a3d
2025-07-10 14:08:15,980 - src.agent - INFO - models.py:203 - Generating response with phi3:mini for role: backend
2025-07-10 14:08:56,566 - src.agent - INFO - models.py:223 - Response generated in 40.59s, length: 408 chars
2025-07-10 14:08:56,622 - src.agent - WARNING - monitoring.py:161 - High memory usage: 86.2%
2025-07-10 14:08:56,626 - src.agent - WARNING - validators.py:61 - Syntax validation failed: Syntax Error: invalid syntax (<unknown>, line 1)
2025-07-10 14:08:56,628 - src.agent - WARNING - agent.py:216 - Code validation failed for python: ['Syntax Error: invalid syntax (<unknown>, line 1)']
2025-07-10 14:08:56,682 - src.agent - WARNING - monitoring.py:161 - High memory usage: 86.2%
2025-07-10 14:08:56,684 - src.agent - INFO - conversation.py:205 - Added assistant message to conversation f5f1366b-df0e-4313-87c0-fe7cd1189a3d
2025-07-10 14:08:56,685 - src.agent - INFO - security.py:328 - SECURITY: code_generation_request - {'task_length': 29, 'language': 'javascript', 'session_id': 'fe2be9b7-3c25-489b-8a3f-8fb5ce7251a0'}
2025-07-10 14:08:56,687 - src.agent - INFO - agent.py:88 - Generating javascript code for: create a hello world function
2025-07-10 14:08:56,689 - src.agent - INFO - conversation.py:205 - Added user message to conversation f5f1366b-df0e-4313-87c0-fe7cd1189a3d
2025-07-10 14:08:56,693 - src.agent - INFO - models.py:203 - Generating response with phi3:mini for role: frontend
2025-07-10 14:09:43,846 - src.agent - INFO - models.py:223 - Response generated in 47.15s, length: 480 chars
2025-07-10 14:09:43,895 - src.agent - WARNING - monitoring.py:161 - High memory usage: 85.4%
2025-07-10 14:09:43,899 - src.agent - INFO - validators.py:163 - JavaScript validation completed: passed
2025-07-10 14:09:43,900 - src.agent - INFO - agent.py:210 - Code validation passed for javascript
2025-07-10 14:09:43,948 - src.agent - WARNING - monitoring.py:161 - High memory usage: 85.4%
2025-07-10 14:09:43,949 - src.agent - INFO - conversation.py:205 - Added assistant message to conversation f5f1366b-df0e-4313-87c0-fe7cd1189a3d
2025-07-10 14:09:44,385 - src.agent - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 14:09:44,388 - src.agent - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 14:09:44,389 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 14:09:44,390 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: 1653a4a0)
2025-07-10 14:09:44,794 - src.agent - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 14:09:44,797 - src.agent - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 14:09:44,798 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 14:09:44,800 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: 7e6eda4d)
2025-07-10 14:09:45,214 - src.agent - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 14:09:45,215 - src.agent - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 14:09:45,217 - src.agent - INFO - requirements_parser.py:133 - Parsing requirements for user default
2025-07-10 14:09:45,218 - src.agent - INFO - requirements_parser.py:228 - Created new user profile for default
2025-07-10 14:09:45,222 - src.agent - INFO - models.py:203 - Generating response with phi3:mini for role: assistant
2025-07-10 14:12:51,600 - src.agent - INFO - models.py:223 - Response generated in 186.38s, length: 2230 chars
2025-07-10 14:12:51,602 - src.agent - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 14:12:51,604 - src.agent - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.93
2025-07-10 14:12:51,605 - src.agent - INFO - requirements_parser.py:133 - Parsing requirements for user default
2025-07-10 14:12:51,607 - src.agent - INFO - models.py:203 - Generating response with phi3:mini for role: assistant
2025-07-10 14:21:38,889 - src.agent - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 14:21:38,899 - src.agent - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 14:21:38,903 - src.agent - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 14:21:38,907 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 14:21:38,909 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: f61f288e)
2025-07-10 14:21:39,305 - src.agent - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 14:21:39,308 - src.agent - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 14:21:39,310 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 14:21:39,311 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: 21d763d4)
2025-07-10 14:21:39,313 - src.agent - INFO - security.py:328 - SECURITY: code_generation_request - {'task_length': 29, 'language': 'python', 'session_id': '21d763d4-bff6-43ff-ad5e-64bc4418d0b1'}
2025-07-10 14:21:39,315 - src.agent - INFO - agent.py:88 - Generating python code for: create a hello world function
2025-07-10 14:21:39,316 - src.agent - INFO - conversation.py:176 - Created new conversation: b5fe25e6-3742-4cbc-9ae0-41496808c755 - New Conversation
2025-07-10 14:21:39,318 - src.agent - INFO - conversation.py:205 - Added user message to conversation b5fe25e6-3742-4cbc-9ae0-41496808c755
2025-07-10 14:21:39,323 - src.agent - INFO - models.py:203 - Generating response with phi3:mini for role: backend
2025-07-10 14:21:57,890 - src.agent - INFO - models.py:223 - Response generated in 18.57s, length: 252 chars
2025-07-10 14:21:58,036 - src.agent - WARNING - validators.py:61 - Syntax validation failed: Syntax Error: invalid syntax (<unknown>, line 1)
2025-07-10 14:21:58,039 - src.agent - WARNING - agent.py:216 - Code validation failed for python: ['Syntax Error: invalid syntax (<unknown>, line 1)']
2025-07-10 14:21:58,410 - src.agent - INFO - conversation.py:205 - Added assistant message to conversation b5fe25e6-3742-4cbc-9ae0-41496808c755
2025-07-10 14:21:58,412 - src.agent - INFO - security.py:328 - SECURITY: code_generation_request - {'task_length': 29, 'language': 'javascript', 'session_id': '21d763d4-bff6-43ff-ad5e-64bc4418d0b1'}
2025-07-10 14:21:58,414 - src.agent - INFO - agent.py:88 - Generating javascript code for: create a hello world function
2025-07-10 14:21:58,416 - src.agent - INFO - conversation.py:205 - Added user message to conversation b5fe25e6-3742-4cbc-9ae0-41496808c755
2025-07-10 14:21:58,420 - src.agent - INFO - models.py:203 - Generating response with phi3:mini for role: frontend
2025-07-10 14:22:46,003 - src.agent - INFO - models.py:223 - Response generated in 47.58s, length: 640 chars
2025-07-10 14:22:46,140 - src.agent - INFO - validators.py:163 - JavaScript validation completed: passed
2025-07-10 14:22:46,142 - src.agent - INFO - agent.py:210 - Code validation passed for javascript
2025-07-10 14:22:46,261 - src.agent - INFO - conversation.py:205 - Added assistant message to conversation b5fe25e6-3742-4cbc-9ae0-41496808c755
2025-07-10 14:22:46,643 - src.agent - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 14:22:46,646 - src.agent - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 14:22:46,648 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 14:22:46,649 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: d9e5a516)
2025-07-10 14:22:47,029 - src.agent - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 14:22:47,033 - src.agent - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 14:22:47,035 - src.agent - INFO - agent.py:44 - All core components initialized successfully
2025-07-10 14:22:47,038 - src.agent - INFO - agent.py:55 - 🤖 Coding Agent initialized (Session: 7de919a1)
2025-07-10 14:23:04,235 - prototype_orchestrator - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 14:23:04,242 - prototype_orchestrator - INFO - prototype_orchestrator.py:69 - Initializing core capabilities...
2025-07-10 14:23:05,484 - prototype_orchestrator - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 14:23:05,486 - prototype_orchestrator - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 14:23:05,487 - prototype_orchestrator - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 14:23:05,489 - prototype_orchestrator - INFO - dependency_manager.py:98 - CodeAnalysisEngine initialized with comprehensive language support
2025-07-10 14:23:05,491 - prototype_orchestrator - INFO - dependency_manager.py:585 - PackageInstaller initialized with multi-manager support
2025-07-10 14:23:05,493 - prototype_orchestrator - INFO - dependency_manager.py:860 - ConflictResolver initialized with compatibility rules
2025-07-10 14:23:05,494 - prototype_orchestrator - INFO - dependency_manager.py:1106 - DependencyManager initialized with all components
2025-07-10 14:23:05,973 - prototype_orchestrator - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 14:23:05,975 - prototype_orchestrator - INFO - database_setup.py:137 - ProjectDatabaseAnalyzer initialized with intelligent pattern recognition
2025-07-10 14:23:05,978 - prototype_orchestrator - INFO - database_setup.py:686 - DatabaseSelector initialized with intelligent selection criteria
2025-07-10 14:23:06,445 - prototype_orchestrator - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 14:23:06,447 - prototype_orchestrator - INFO - database_setup.py:905 - SchemaGenerator initialized with SQL generation capabilities
2025-07-10 14:23:38,051 - prototype_orchestrator - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 14:23:38,056 - prototype_orchestrator - INFO - prototype_orchestrator.py:69 - Initializing core capabilities...
2025-07-10 14:23:39,207 - prototype_orchestrator - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 14:23:39,209 - prototype_orchestrator - INFO - requirements_parser.py:114 - RequirementParser initialized with conversational AI capabilities
2025-07-10 14:23:39,211 - prototype_orchestrator - INFO - monitoring.py:71 - Performance monitor initialized
2025-07-10 14:23:39,212 - prototype_orchestrator - INFO - dependency_manager.py:98 - CodeAnalysisEngine initialized with comprehensive language support
2025-07-10 14:23:39,213 - prototype_orchestrator - INFO - dependency_manager.py:585 - PackageInstaller initialized with multi-manager support
2025-07-10 14:23:39,215 - prototype_orchestrator - INFO - dependency_manager.py:860 - ConflictResolver initialized with compatibility rules
2025-07-10 14:23:39,216 - prototype_orchestrator - INFO - dependency_manager.py:1106 - DependencyManager initialized with all components
2025-07-10 14:23:39,597 - prototype_orchestrator - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 14:23:39,598 - prototype_orchestrator - INFO - database_setup.py:137 - ProjectDatabaseAnalyzer initialized with intelligent pattern recognition
2025-07-10 14:23:39,600 - prototype_orchestrator - INFO - database_setup.py:686 - DatabaseSelector initialized with intelligent selection criteria
2025-07-10 14:23:39,978 - prototype_orchestrator - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 14:23:39,979 - prototype_orchestrator - INFO - database_setup.py:905 - SchemaGenerator initialized with SQL generation capabilities
2025-07-10 14:23:39,981 - prototype_orchestrator - INFO - database_setup.py:1350 - MigrationManager initialized for project: C:\Users\<USER>\AppData\Local\Temp\tmp2hz2v9oj
2025-07-10 14:23:39,983 - prototype_orchestrator - INFO - database_setup.py:1732 - DatabaseConnectionManager initialized for project: C:\Users\<USER>\AppData\Local\Temp\tmp2hz2v9oj
2025-07-10 14:23:39,985 - prototype_orchestrator - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 14:23:39,986 - prototype_orchestrator - INFO - error_monitor.py:125 - ErrorMonitor initialized
2025-07-10 14:23:39,988 - prototype_orchestrator - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 14:23:39,989 - prototype_orchestrator - INFO - root_cause_analyzer.py:142 - RootCauseAnalyzer initialized
2025-07-10 14:23:40,369 - prototype_orchestrator - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 14:23:40,370 - prototype_orchestrator - INFO - auto_fix_generator.py:123 - AutoFixGenerator initialized
2025-07-10 14:23:40,744 - prototype_orchestrator - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 14:23:40,745 - prototype_orchestrator - INFO - error_explainer.py:186 - ErrorExplainer initialized
2025-07-10 14:23:40,748 - prototype_orchestrator - INFO - database.py:174 - Database schema initialized successfully
2025-07-10 14:23:40,749 - prototype_orchestrator - INFO - error_pattern_learner.py:74 - ErrorPatternLearner initialized
2025-07-10 14:23:41,122 - prototype_orchestrator - INFO - models.py:87 - Ollama client initialized with host: http://localhost:11434
2025-07-10 14:23:41,126 - prototype_orchestrator - INFO - error_monitor.py:274 - File monitoring started for C:\Users\<USER>\AppData\Local\Temp\tmp2hz2v9oj
2025-07-10 14:23:41,127 - prototype_orchestrator - INFO - error_monitor.py:142 - Error monitoring started
2025-07-10 14:23:41,129 - prototype_orchestrator - INFO - prototype_orchestrator.py:98 - PrototypeOrchestrator initialized successfully
2025-07-10 14:23:41,130 - prototype_orchestrator - INFO - prototype_orchestrator.py:115 - Starting project creation from natural language: 
        I want to create a simple todo list web application.
        Users should be able to:
     ...
2025-07-10 14:23:41,132 - prototype_orchestrator - INFO - prototype_orchestrator.py:119 - Phase 1: Parsing requirements from natural language
2025-07-10 14:23:41,134 - prototype_orchestrator - INFO - requirements_parser.py:133 - Parsing requirements for user default
2025-07-10 14:23:41,135 - prototype_orchestrator - INFO - requirements_parser.py:228 - Created new user profile for default
2025-07-10 14:23:41,142 - prototype_orchestrator - INFO - models.py:203 - Generating response with phi3:mini for role: assistant
2025-07-10 14:25:36,372 - prototype_orchestrator - WARNING - error_monitor.py:511 - Error detected: HighCPUUsage - High CPU usage: 93.1%
2025-07-10 14:26:41,967 - prototype_orchestrator - INFO - models.py:223 - Response generated in 180.82s, length: 2426 chars
2025-07-10 14:26:41,969 - prototype_orchestrator - WARNING - requirements_parser.py:375 - Failed to parse JSON from LLM response: Expecting value: line 16 column 1 (char 555)
2025-07-10 14:26:41,970 - prototype_orchestrator - DEBUG - requirements_parser.py:522 - Updated user profile for interaction on 2025-07-10
2025-07-10 14:26:41,972 - prototype_orchestrator - INFO - requirements_parser.py:159 - Successfully parsed requirements with confidence: 0.20
2025-07-10 14:26:41,973 - prototype_orchestrator - INFO - prototype_orchestrator.py:123 - Phase 2: Generating project structure and code
2025-07-10 14:26:41,974 - prototype_orchestrator - ERROR - prototype_orchestrator.py:222 - Error generating project code: 'ProjectRequirement' object has no attribute 'get'
2025-07-10 14:26:41,976 - prototype_orchestrator - ERROR - prototype_orchestrator.py:154 - Error in project creation: Failed to generate project code: 'ProjectRequirement' object has no attribute 'get'
2025-07-10 14:26:46,978 - prototype_orchestrator - INFO - error_monitor.py:155 - Error monitoring stopped
2025-07-10 14:30:09,365 - enhanced_project_generators - INFO - enhanced_project_generators.py:62 - Generating enhanced react_app project: test-react-app
2025-07-10 14:30:09,379 - enhanced_project_generators - INFO - enhanced_project_generators.py:112 - Successfully generated 11 files for react_app project
2025-07-10 14:30:09,387 - enhanced_project_generators - INFO - enhanced_project_generators.py:62 - Generating enhanced nodejs_api project: test-nodejs-api
2025-07-10 14:30:09,407 - enhanced_project_generators - INFO - enhanced_project_generators.py:112 - Successfully generated 9 files for nodejs_api project
2025-07-10 14:30:09,417 - enhanced_project_generators - INFO - enhanced_project_generators.py:62 - Generating enhanced fullstack_react_node project: test-fullstack-app
2025-07-10 14:30:09,437 - enhanced_project_generators - INFO - enhanced_project_generators.py:112 - Successfully generated 13 files for fullstack_react_node project
