# AI Coding Agent - End-to-End Todo List Web App Test
"""
Comprehensive test that validates the complete workflow from natural language input
to working web application for a todo list app
"""

import unittest
import tempfile
import shutil
import time
from pathlib import Path
from typing import Dict, Any

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from prototype_orchestrator import PrototypeOrchestrator, ProjectResult

class TestEndToEndTodoApp(unittest.TestCase):
    """
    End-to-end test for creating a complete todo list web application
    from natural language input
    """

    def setUp(self):
        """Set up test environment"""
        # Create temporary directory for test projects
        self.test_dir = Path(tempfile.mkdtemp())
        self.orchestrator = PrototypeOrchestrator(str(self.test_dir))

        # Test input for todo list app
        self.todo_app_request = """
        I want to create a simple todo list web application.
        Users should be able to:
        - Add new tasks
        - Mark tasks as completed
        - Delete tasks
        - View all tasks

        The app should have a clean, modern interface and use a database
        to store the tasks. I want it to be a web app that I can run locally.
        """

        self.user_context = {
            'skill_level': 'beginner',
            'preferred_technologies': ['python', 'web'],
            'deployment_target': 'local'
        }

    def tearDown(self):
        """Clean up test environment"""
        # Stop error monitoring
        if hasattr(self.orchestrator, 'error_monitor'):
            self.orchestrator.error_monitor.stop_monitoring()

        # Clean up temporary directory
        if self.test_dir.exists():
            shutil.rmtree(self.test_dir)

    def test_complete_todo_app_workflow(self):
        """Test the complete workflow from natural language to working todo app"""
        print("\n" + "="*80)
        print("TESTING: Complete Todo List Web App Generation Workflow")
        print("="*80)

        # Phase 1: Create project from natural language
        print("\n1. Creating project from natural language input...")
        start_time = time.time()

        result = self.orchestrator.create_project_from_natural_language(
            self.todo_app_request,
            self.user_context
        )

        creation_time = time.time() - start_time
        print(f"   Project creation completed in {creation_time:.2f} seconds")

        # Validate result structure
        self.assertIsInstance(result, ProjectResult)
        print(f"   Success: {result.success}")
        print(f"   Project path: {result.project_path}")
        print(f"   Generated files: {len(result.generated_files)}")
        print(f"   Dependencies installed: {len(result.dependencies_installed)}")

        # Phase 2: Validate project structure
        print("\n2. Validating project structure...")
        self._validate_project_structure(result)

        # Phase 3: Validate generated code quality
        print("\n3. Validating code quality...")
        self._validate_code_quality(result)

        # Phase 4: Test error detection and resolution
        print("\n4. Testing error detection and resolution...")
        self._test_error_resolution(result)

        # Phase 5: Test dependency management
        print("\n5. Testing dependency management...")
        self._test_dependency_management(result)

        # Phase 6: Test database setup
        print("\n6. Testing database setup...")
        self._test_database_setup(result)

        # Phase 7: Validate user experience
        print("\n7. Validating user experience...")
        self._validate_user_experience(result)

        # Final validation
        print("\n8. Final validation...")
        self.assertTrue(result.success, "Project creation should succeed")
        self.assertGreater(len(result.generated_files), 0, "Should generate files")
        self.assertIsNotNone(result.explanation, "Should provide explanation")
        self.assertGreater(len(result.next_steps), 0, "Should provide next steps")

        print("\n" + "="*80)
        print("✅ END-TO-END TEST PASSED: Todo app successfully generated!")
        print("="*80)

        # Print summary
        self._print_test_summary(result, creation_time)

    def _validate_project_structure(self, result: ProjectResult):
        """Validate that the project has the expected structure"""
        project_path = Path(result.project_path)

        # Check project directory exists
        self.assertTrue(project_path.exists(), "Project directory should exist")

        # Check for essential files
        essential_files = ['app.py', 'requirements.txt']
        for file_name in essential_files:
            file_path = project_path / file_name
            self.assertTrue(file_path.exists(), f"Essential file {file_name} should exist")
            print(f"   ✓ Found essential file: {file_name}")

        # Check for templates directory (for web apps)
        templates_dir = project_path / 'templates'
        if templates_dir.exists():
            print(f"   ✓ Found templates directory")

            # Check for HTML files
            html_files = list(templates_dir.glob('*.html'))
            self.assertGreater(len(html_files), 0, "Should have HTML template files")
            print(f"   ✓ Found {len(html_files)} HTML template(s)")

        print(f"   ✓ Project structure validation passed")

    def _validate_code_quality(self, result: ProjectResult):
        """Validate the quality of generated code"""
        project_path = Path(result.project_path)

        # Check Python files for basic quality
        python_files = list(project_path.glob('**/*.py'))
        self.assertGreater(len(python_files), 0, "Should have Python files")

        for py_file in python_files:
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # Basic quality checks
            self.assertGreater(len(content), 50, f"File {py_file.name} should have substantial content")
            self.assertIn('def ', content, f"File {py_file.name} should have function definitions")

            # Check for common patterns
            if py_file.name == 'app.py':
                self.assertIn('Flask', content, "Web app should use Flask")
                self.assertIn('@app.route', content, "Should have route definitions")
                print(f"   ✓ {py_file.name} has proper Flask structure")

        # Check HTML files for basic structure
        html_files = list(project_path.glob('**/*.html'))
        for html_file in html_files:
            with open(html_file, 'r', encoding='utf-8') as f:
                content = f.read()

            self.assertIn('<!DOCTYPE html>', content, f"HTML file {html_file.name} should have DOCTYPE")
            self.assertIn('<html', content, f"HTML file {html_file.name} should have html tag")
            print(f"   ✓ {html_file.name} has proper HTML structure")

        print(f"   ✓ Code quality validation passed")

    def _test_error_resolution(self, result: ProjectResult):
        """Test that error detection and resolution worked properly"""
        # Check if any errors were detected and resolved
        errors_detected = len(result.errors_detected)
        errors_fixed = len(result.errors_fixed)

        print(f"   Errors detected: {errors_detected}")
        print(f"   Errors fixed: {errors_fixed}")

        # If errors were detected, some should have been fixed
        if errors_detected > 0:
            self.assertGreaterEqual(errors_fixed, 0, "Should attempt to fix detected errors")
            print(f"   ✓ Error resolution system is working")
        else:
            print(f"   ✓ No errors detected - clean code generation")

        print(f"   ✓ Error resolution validation passed")

    def _test_dependency_management(self, result: ProjectResult):
        """Test that dependency management worked correctly"""
        dependencies = result.dependencies_installed

        print(f"   Dependencies installed: {len(dependencies)}")
        for dep in dependencies:
            print(f"     - {dep}")

        # Check that requirements.txt exists and has content
        project_path = Path(result.project_path)
        requirements_file = project_path / 'requirements.txt'

        if requirements_file.exists():
            with open(requirements_file, 'r') as f:
                content = f.read().strip()

            self.assertGreater(len(content), 0, "Requirements file should have content")
            print(f"   ✓ Requirements file created with content")

        print(f"   ✓ Dependency management validation passed")

    def _test_database_setup(self, result: ProjectResult):
        """Test that database setup worked correctly"""
        db_setup = result.database_setup

        print(f"   Database needed: {db_setup.get('database_needed', False)}")

        if db_setup.get('database_needed', False):
            print(f"   Database type: {db_setup.get('database_type', 'unknown')}")
            print(f"   Schema created: {db_setup.get('schema_created', False)}")
            print(f"   Connection configured: {db_setup.get('connection_configured', False)}")

            self.assertIsNotNone(db_setup.get('database_type'), "Should specify database type")
            print(f"   ✓ Database setup completed")
        else:
            print(f"   ✓ No database needed for this project")

        print(f"   ✓ Database setup validation passed")

    def _validate_user_experience(self, result: ProjectResult):
        """Validate that the user experience meets requirements"""
        # Check explanation quality
        explanation = result.explanation
        self.assertIsNotNone(explanation, "Should provide explanation")
        self.assertGreater(len(explanation), 50, "Explanation should be substantial")
        print(f"   ✓ Explanation provided: {len(explanation)} characters")

        # Check next steps
        next_steps = result.next_steps
        self.assertGreater(len(next_steps), 0, "Should provide next steps")
        print(f"   ✓ Next steps provided: {len(next_steps)} steps")

        # Check that files are actually generated
        project_path = Path(result.project_path)
        actual_files = list(project_path.glob('**/*'))
        actual_files = [f for f in actual_files if f.is_file()]

        self.assertGreater(len(actual_files), 0, "Should generate actual files")
        print(f"   ✓ Generated {len(actual_files)} actual files")

        # Validate zero coding knowledge requirement
        # The user should be able to understand what was created
        beginner_friendly_terms = ['run', 'start', 'open', 'browser', 'install']
        explanation_lower = explanation.lower()

        has_beginner_terms = any(term in explanation_lower for term in beginner_friendly_terms)
        self.assertTrue(has_beginner_terms, "Explanation should be beginner-friendly")
        print(f"   ✓ Explanation is beginner-friendly")

        print(f"   ✓ User experience validation passed")

    def _print_test_summary(self, result: ProjectResult, creation_time: float):
        """Print comprehensive test summary"""
        print(f"\n📊 TEST SUMMARY:")
        print(f"   ⏱️  Total creation time: {creation_time:.2f} seconds")
        print(f"   📁 Project path: {result.project_path}")
        print(f"   📄 Files generated: {len(result.generated_files)}")
        print(f"   📦 Dependencies: {len(result.dependencies_installed)}")
        print(f"   🐛 Errors detected: {len(result.errors_detected)}")
        print(f"   🔧 Errors fixed: {len(result.errors_fixed)}")
        print(f"   ✅ Success: {result.success}")

        print(f"\n📝 EXPLANATION:")
        print(f"   {result.explanation}")

        print(f"\n🚀 NEXT STEPS:")
        for i, step in enumerate(result.next_steps[:5], 1):
            print(f"   {i}. {step}")

        if len(result.next_steps) > 5:
            print(f"   ... and {len(result.next_steps) - 5} more steps")

        print(f"\n🎯 ZERO CODING KNOWLEDGE VALIDATION:")
        print(f"   ✅ User can create applications without coding knowledge")
        print(f"   ✅ Natural conversation interface works")
        print(f"   ✅ Automatic error detection and fixing")
        print(f"   ✅ Dependency management is transparent")
        print(f"   ✅ Clear explanations and next steps provided")

if __name__ == '__main__':
    # Run the test
    unittest.main(verbosity=2)