# AI Coding Agent - Dependency Manager Tests
"""
Comprehensive test suite for the dependency management system
covering code analysis, package installation, conflict resolution, and caching.
"""

import json
import tempfile
import pytest
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

from src.dependency_manager import (
    CodeAnalysisEngine, PackageInstaller, ConflictResolver, DependencyManager,
    DependencyCache, EnhancedDependencyManager, CachedCodeAnalysisEngine,
    PackageInfo, ConflictInfo, PackageManager, DependencyType,
    DependencyAnalysisError, PackageInstallationError
)


class TestCodeAnalysisEngine:
    """Test cases for CodeAnalysisEngine"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.analyzer = CodeAnalysisEngine()
        self.temp_dir = Path(tempfile.mkdtemp())
    
    def teardown_method(self):
        """Clean up test fixtures"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_analyze_python_file(self):
        """Test Python file analysis"""
        # Create test Python file
        python_file = self.temp_dir / "test.py"
        python_content = """
import os
import sys
import requests
from flask import Flask
from sklearn import datasets
import cv2
"""
        python_file.write_text(python_content)
        
        # Analyze file
        packages = self.analyzer.analyze_file(python_file)
        
        # Verify results
        package_names = [pkg.name for pkg in packages]
        assert 'os' in package_names  # Standard library
        assert 'sys' in package_names  # Standard library
        assert 'requests' in package_names  # Third-party
        assert 'Flask' in package_names  # Mapped from flask
        assert 'scikit-learn' in package_names  # Mapped from sklearn
        assert 'opencv-python' in package_names  # Mapped from cv2
        
        # Check standard library detection
        os_package = next(pkg for pkg in packages if pkg.name == 'os')
        assert os_package.is_standard_library
        
        requests_package = next(pkg for pkg in packages if pkg.name == 'requests')
        assert not requests_package.is_standard_library
    
    def test_analyze_javascript_file(self):
        """Test JavaScript file analysis"""
        # Create test JavaScript file
        js_file = self.temp_dir / "test.js"
        js_content = """
import React from 'react';
import { useState } from 'react';
const express = require('express');
const fs = require('fs');
import axios from 'axios';
import '@babel/core';
"""
        js_file.write_text(js_content)
        
        # Analyze file
        packages = self.analyzer.analyze_file(js_file)
        
        # Verify results
        package_names = [pkg.name for pkg in packages]
        assert 'react' in package_names
        assert 'express' in package_names
        assert 'fs' in package_names  # Built-in Node.js module
        assert 'axios' in package_names
        assert '@babel/core' in package_names  # Scoped package
        
        # Check built-in module detection
        fs_package = next(pkg for pkg in packages if pkg.name == 'fs')
        assert fs_package.is_standard_library
        
        react_package = next(pkg for pkg in packages if pkg.name == 'react')
        assert not react_package.is_standard_library
        assert react_package.package_manager == PackageManager.NPM
    
    def test_analyze_requirements_file(self):
        """Test requirements.txt analysis"""
        # Create test requirements file
        req_file = self.temp_dir / "requirements.txt"
        req_content = """
# Web framework
flask>=2.0.0
django==4.2.0
requests~=2.28.0

# Data science
numpy>=1.21.0
pandas
matplotlib==3.5.0
"""
        req_file.write_text(req_content)
        
        # Analyze file
        packages = self.analyzer.analyze_file(req_file)
        
        # Verify results
        assert len(packages) == 6
        
        flask_package = next(pkg for pkg in packages if pkg.name == 'flask')
        assert flask_package.version == '>=2.0.0'
        
        django_package = next(pkg for pkg in packages if pkg.name == 'django')
        assert django_package.version == '==4.2.0'
        
        pandas_package = next(pkg for pkg in packages if pkg.name == 'pandas')
        assert pandas_package.version is None
    
    def test_analyze_package_json(self):
        """Test package.json analysis"""
        # Create test package.json file
        package_json = self.temp_dir / "package.json"
        package_data = {
            "name": "test-project",
            "version": "1.0.0",
            "dependencies": {
                "react": "^18.0.0",
                "axios": "^1.0.0"
            },
            "devDependencies": {
                "jest": "^29.0.0",
                "eslint": "^8.0.0"
            }
        }
        package_json.write_text(json.dumps(package_data, indent=2))
        
        # Analyze file
        packages = self.analyzer.analyze_file(package_json)
        
        # Verify results
        assert len(packages) == 4
        
        react_package = next(pkg for pkg in packages if pkg.name == 'react')
        assert react_package.version == '^18.0.0'
        assert react_package.dependency_type == DependencyType.RUNTIME
        
        jest_package = next(pkg for pkg in packages if pkg.name == 'jest')
        assert jest_package.dependency_type == DependencyType.DEVELOPMENT
    
    def test_analyze_directory(self):
        """Test directory analysis"""
        # Create multiple test files
        (self.temp_dir / "app.py").write_text("import flask\nimport requests")
        (self.temp_dir / "script.js").write_text("const express = require('express');")
        (self.temp_dir / "requirements.txt").write_text("flask>=2.0.0\nrequests")
        
        # Analyze directory
        results = self.analyzer.analyze_directory(self.temp_dir)
        
        # Verify results
        assert len(results) == 3
        assert any('app.py' in path for path in results.keys())
        assert any('script.js' in path for path in results.keys())
        assert any('requirements.txt' in path for path in results.keys())
    
    def test_unsupported_file_type(self):
        """Test handling of unsupported file types"""
        # Create unsupported file
        unsupported_file = self.temp_dir / "test.txt"
        unsupported_file.write_text("Some text content")
        
        # Analyze file
        packages = self.analyzer.analyze_file(unsupported_file)
        
        # Should return empty list
        assert packages == []
    
    def test_nonexistent_file(self):
        """Test handling of nonexistent files"""
        nonexistent_file = self.temp_dir / "nonexistent.py"
        
        # Should raise DependencyAnalysisError
        with pytest.raises(DependencyAnalysisError):
            self.analyzer.analyze_file(nonexistent_file)


class TestPackageInstaller:
    """Test cases for PackageInstaller"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.installer = PackageInstaller()
    
    @patch('subprocess.run')
    @patch.object(PackageInstaller, '_is_package_installed')
    def test_install_pip_package_success(self, mock_is_installed, mock_run):
        """Test successful pip package installation"""
        # Mock package not installed initially
        mock_is_installed.return_value = False

        # Mock successful subprocess call
        mock_run.return_value = Mock(returncode=0, stderr="")

        package_info = PackageInfo(
            name="requests",
            version=">=2.28.0",
            package_manager=PackageManager.PIP
        )

        # Install package
        result = self.installer.install_package(package_info)

        # Verify success
        assert result is True
        mock_run.assert_called_once()

        # Check command
        call_args = mock_run.call_args[0][0]
        assert call_args[0] == 'pip'
        assert call_args[1] == 'install'
        assert 'requests>=2.28.0' in call_args
    
    @patch('subprocess.run')
    @patch.object(PackageInstaller, '_is_package_installed')
    def test_install_npm_package_success(self, mock_is_installed, mock_run):
        """Test successful npm package installation"""
        # Mock package not installed initially
        mock_is_installed.return_value = False

        # Mock successful subprocess call
        mock_run.return_value = Mock(returncode=0, stderr="")

        package_info = PackageInfo(
            name="react",
            version="^18.0.0",
            package_manager=PackageManager.NPM,
            dependency_type=DependencyType.RUNTIME
        )

        # Install package
        result = self.installer.install_package(package_info)

        # Verify success
        assert result is True
        mock_run.assert_called_once()

        # Check command
        call_args = mock_run.call_args[0][0]
        assert call_args[0] == 'npm'
        assert call_args[1] == 'install'
        assert 'react@^18.0.0' in call_args
    
    @patch('subprocess.run')
    def test_install_package_failure(self, mock_run):
        """Test package installation failure"""
        # Mock failed subprocess call
        mock_run.return_value = Mock(returncode=1, stderr="Package not found")
        
        package_info = PackageInfo(
            name="nonexistent-package",
            package_manager=PackageManager.PIP
        )
        
        # Install package
        result = self.installer.install_package(package_info)
        
        # Verify failure
        assert result is False
    
    def test_skip_standard_library(self):
        """Test skipping standard library packages"""
        package_info = PackageInfo(
            name="os",
            package_manager=PackageManager.PIP,
            is_standard_library=True
        )
        
        # Install package
        result = self.installer.install_package(package_info)
        
        # Should skip and return True
        assert result is True
    
    @patch('subprocess.run')
    @patch.object(PackageInstaller, '_is_package_installed')
    def test_install_multiple_packages(self, mock_is_installed, mock_run):
        """Test installing multiple packages"""
        # Mock packages not installed initially
        mock_is_installed.return_value = False

        # Mock successful subprocess calls
        mock_run.return_value = Mock(returncode=0, stderr="")

        packages = [
            PackageInfo(name="requests", package_manager=PackageManager.PIP),
            PackageInfo(name="flask", package_manager=PackageManager.PIP),
            PackageInfo(name="react", package_manager=PackageManager.NPM)
        ]

        # Install packages
        results = self.installer.install_packages(packages)

        # Verify results
        assert len(results) == 3
        assert all(results.values())
        assert mock_run.call_count == 3


class TestConflictResolver:
    """Test cases for ConflictResolver"""

    def setup_method(self):
        """Set up test fixtures"""
        self.resolver = ConflictResolver()

    def test_detect_version_conflicts(self):
        """Test detection of version conflicts"""
        packages = [
            PackageInfo(name="requests", version="==2.28.0", package_manager=PackageManager.PIP),
            PackageInfo(name="requests", version=">=2.30.0", package_manager=PackageManager.PIP),
            PackageInfo(name="flask", version="==2.0.0", package_manager=PackageManager.PIP)
        ]

        # Detect conflicts
        conflicts = self.resolver.detect_conflicts(packages)

        # Verify conflict detection
        assert len(conflicts) == 1
        conflict = conflicts[0]
        assert conflict.package_name == "requests"
        assert "==2.28.0" in conflict.required_versions
        assert ">=2.30.0" in conflict.required_versions

    def test_no_conflicts(self):
        """Test when no conflicts exist"""
        packages = [
            PackageInfo(name="requests", version="==2.28.0", package_manager=PackageManager.PIP),
            PackageInfo(name="flask", version="==2.0.0", package_manager=PackageManager.PIP),
            PackageInfo(name="react", version="^18.0.0", package_manager=PackageManager.NPM)
        ]

        # Detect conflicts
        conflicts = self.resolver.detect_conflicts(packages)

        # Should be no conflicts
        assert len(conflicts) == 0

    def test_known_incompatibilities(self):
        """Test detection of known incompatible packages"""
        packages = [
            PackageInfo(name="django", package_manager=PackageManager.PIP),
            PackageInfo(name="flask", package_manager=PackageManager.PIP)
        ]

        # Detect conflicts
        conflicts = self.resolver.detect_conflicts(packages)

        # Should detect incompatibility
        assert len(conflicts) > 0
        conflict_packages = [c.package_name for c in conflicts]
        assert "django" in conflict_packages or "flask" in conflict_packages

    def test_resolve_conflicts(self):
        """Test conflict resolution"""
        conflicts = [
            ConflictInfo(
                package_name="requests",
                required_versions=["==2.28.0", ">=2.30.0"],
                current_version=None,
                conflicting_packages=[]
            )
        ]

        # Resolve conflicts
        resolutions = self.resolver.resolve_conflicts(conflicts)

        # Verify resolution
        assert "requests" in resolutions
        assert resolutions["requests"] is not None

    def test_assess_conflict_severity(self):
        """Test conflict severity assessment"""
        # Test major version conflict (critical)
        severity = self.resolver._assess_conflict_severity("package", ["1.0.0", "2.0.0"])
        assert severity == "critical"

        # Test minor version conflict (medium/high)
        severity = self.resolver._assess_conflict_severity("package", ["1.1.0", "1.2.0"])
        assert severity in ["medium", "high"]


class TestDependencyCache:
    """Test cases for DependencyCache"""

    def setup_method(self):
        """Set up test fixtures"""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.cache = DependencyCache(self.temp_dir / "cache")

        # Create test file
        self.test_file = self.temp_dir / "test.py"
        self.test_file.write_text("import requests")

    def teardown_method(self):
        """Clean up test fixtures"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_cache_miss_and_store(self):
        """Test cache miss and storing results"""
        # Should be cache miss initially
        result = self.cache.get_file_analysis(self.test_file)
        assert result is None

        # Store analysis result
        packages = [PackageInfo(name="requests", package_manager=PackageManager.PIP)]
        self.cache.store_file_analysis(self.test_file, packages)

        # Should now be cache hit
        cached_result = self.cache.get_file_analysis(self.test_file)
        assert cached_result is not None
        assert len(cached_result) == 1
        assert cached_result[0].name == "requests"

    def test_cache_expiration(self):
        """Test cache expiration mechanism"""
        packages = [PackageInfo(name="requests", package_manager=PackageManager.PIP)]
        self.cache.store_file_analysis(self.test_file, packages)

        # Should be cache hit immediately
        result = self.cache.get_file_analysis(self.test_file)
        assert result is not None

        # Clear memory cache to simulate expiration
        self.cache._memory_cache.clear()

        # Should still get result from file cache
        result = self.cache.get_file_analysis(self.test_file)
        assert result is not None  # File cache should still work

        # Clear both caches
        self.cache.clear_cache()

        # Should be cache miss now
        result = self.cache.get_file_analysis(self.test_file)
        assert result is None

    def test_package_info_cache(self):
        """Test package information caching"""
        # Store package info
        info = {"version": "2.28.0", "description": "HTTP library"}
        self.cache.store_package_info("requests", PackageManager.PIP, info)

        # Retrieve package info
        cached_info = self.cache.get_package_info("requests", PackageManager.PIP)
        assert cached_info == info

        # Non-existent package should return None
        missing_info = self.cache.get_package_info("nonexistent", PackageManager.PIP)
        assert missing_info is None

    def test_cache_stats(self):
        """Test cache statistics"""
        # Initial stats
        stats = self.cache.get_cache_stats()
        assert stats['total_requests'] == 0

        # Perform cache operations
        self.cache.get_file_analysis(self.test_file)  # Miss

        packages = [PackageInfo(name="requests", package_manager=PackageManager.PIP)]
        self.cache.store_file_analysis(self.test_file, packages)

        self.cache.get_file_analysis(self.test_file)  # Hit

        # Check updated stats
        stats = self.cache.get_cache_stats()
        assert stats['total_requests'] == 2
        assert stats['hits'] == 1
        assert stats['misses'] == 1

    def test_clear_cache(self):
        """Test cache clearing"""
        # Store some data
        packages = [PackageInfo(name="requests", package_manager=PackageManager.PIP)]
        self.cache.store_file_analysis(self.test_file, packages)

        # Verify data exists
        result = self.cache.get_file_analysis(self.test_file)
        assert result is not None

        # Clear cache
        self.cache.clear_cache()

        # Verify data is gone
        result = self.cache.get_file_analysis(self.test_file)
        assert result is None


class TestDependencyManager:
    """Test cases for DependencyManager"""

    def setup_method(self):
        """Set up test fixtures"""
        self.manager = DependencyManager()
        self.temp_dir = Path(tempfile.mkdtemp())

    def teardown_method(self):
        """Clean up test fixtures"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    @patch.object(PackageInstaller, 'install_packages')
    def test_analyze_and_install_dependencies(self, mock_install):
        """Test complete dependency workflow"""
        # Mock successful installation
        mock_install.return_value = {"requests": True, "flask": True}

        # Create test project
        (self.temp_dir / "app.py").write_text("import requests\nfrom flask import Flask")
        (self.temp_dir / "requirements.txt").write_text("requests>=2.28.0\nflask>=2.0.0")

        # Run workflow
        result = self.manager.analyze_and_install_dependencies(
            self.temp_dir, auto_install=True, resolve_conflicts=True
        )

        # Verify results
        assert result['success'] is True
        assert 'analysis_results' in result
        assert 'detected_packages' in result
        assert 'installation_results' in result

        # Check that packages were detected
        package_names = [pkg.name for pkg in result['detected_packages']]
        assert 'requests' in package_names
        assert 'Flask' in package_names  # Mapped from flask

    def test_get_dependency_summary(self):
        """Test dependency summary generation"""
        # Create test project with mixed languages
        (self.temp_dir / "app.py").write_text("import requests\nimport flask")
        (self.temp_dir / "script.js").write_text("const express = require('express');")

        # Get summary
        summary = self.manager.get_dependency_summary(self.temp_dir)

        # Verify summary
        assert 'total_files_analyzed' in summary
        assert 'python_packages' in summary
        assert 'javascript_packages' in summary
        assert summary['total_files_analyzed'] == 2
        assert summary['python_packages'] > 0
        assert summary['javascript_packages'] > 0


class TestEnhancedDependencyManager:
    """Test cases for EnhancedDependencyManager with caching"""

    def setup_method(self):
        """Set up test fixtures"""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.manager = EnhancedDependencyManager(self.temp_dir / "cache")

    def teardown_method(self):
        """Clean up test fixtures"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_cached_analysis(self):
        """Test that analysis results are cached"""
        # Create test file
        test_file = self.temp_dir / "test.py"
        test_file.write_text("import requests")

        # First analysis (cache miss)
        packages1 = self.manager.code_analyzer.analyze_file(test_file)

        # Second analysis (cache hit)
        packages2 = self.manager.code_analyzer.analyze_file(test_file)

        # Results should be identical
        assert len(packages1) == len(packages2)
        assert packages1[0].name == packages2[0].name

        # Check cache stats
        stats = self.manager.cache.get_cache_stats()
        assert stats['hits'] >= 1
