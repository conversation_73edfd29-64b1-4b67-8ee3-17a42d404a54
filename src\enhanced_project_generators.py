# AI Coding Agent - Enhanced Project Generators
"""
Enhanced project generators addressing critical capability gaps:
- Frontend Framework Support (React, Vue, Angular)
- Design System Integration (CSS frameworks, custom styling)
- Technology Stack Expansion (Node.js, Django, Spring Boot)
- Advanced Feature Templates (Authentication, real-time, business logic)
- Mobile Development Support (React Native, Flutter)
"""

import os
import json
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from src.logger import get_logger

logger = get_logger(__name__)

@dataclass
class ProjectTemplate:
    """Enhanced project template with comprehensive configuration"""
    name: str
    framework: str
    language: str
    features: List[str]
    dependencies: List[str]
    dev_dependencies: List[str]
    build_commands: List[str]
    start_commands: List[str]
    file_structure: Dict[str, Any]
    styling_framework: Optional[str] = None
    authentication: bool = False
    database: Optional[str] = None
    real_time: bool = False

class EnhancedProjectGenerator:
    """Enhanced project generator with modern framework support"""

    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        self.templates = self._initialize_templates()

    def _initialize_templates(self) -> Dict[str, ProjectTemplate]:
        """Initialize comprehensive project templates"""
        return {
            'react_app': self._create_react_template(),
            'vue_app': self._create_vue_template(),
            'angular_app': self._create_angular_template(),
            'nodejs_api': self._create_nodejs_template(),
            'django_app': self._create_django_template(),
            'spring_boot_api': self._create_spring_boot_template(),
            'react_native_app': self._create_react_native_template(),
            'flutter_app': self._create_flutter_template(),
            'fullstack_react_node': self._create_fullstack_template()
        }

    def generate_project(self, project_type: str, project_name: str,
                        project_path: str, requirements: Dict[str, Any]) -> Dict[str, Any]:
        """Generate enhanced project with modern frameworks and features"""
        try:
            self.logger.info(f"Generating enhanced {project_type} project: {project_name}")

            # Get template
            template = self.templates.get(project_type)
            if not template:
                raise ValueError(f"Unsupported project type: {project_type}")

            # Create project directory
            project_dir = Path(project_path) / project_name
            project_dir.mkdir(parents=True, exist_ok=True)

            # Generate project files
            generated_files = []

            # Create package.json or equivalent
            if template.framework in ['react', 'vue', 'angular', 'nodejs']:
                package_json = self._create_package_json(template, project_name, requirements)
                self._write_file(project_dir / 'package.json', json.dumps(package_json, indent=2))
                generated_files.append('package.json')

            # Create main application files
            app_files = self._generate_application_files(template, requirements)
            for file_path, content in app_files.items():
                full_path = project_dir / file_path
                full_path.parent.mkdir(parents=True, exist_ok=True)
                self._write_file(full_path, content)
                generated_files.append(file_path)

            # Create configuration files
            config_files = self._generate_configuration_files(template, requirements)
            for file_path, content in config_files.items():
                full_path = project_dir / file_path
                full_path.parent.mkdir(parents=True, exist_ok=True)
                self._write_file(full_path, content)
                generated_files.append(file_path)

            # Create styling files
            if template.styling_framework:
                style_files = self._generate_styling_files(template, requirements)
                for file_path, content in style_files.items():
                    full_path = project_dir / file_path
                    full_path.parent.mkdir(parents=True, exist_ok=True)
                    self._write_file(full_path, content)
                    generated_files.append(file_path)

            # Create README and documentation
            readme_content = self._generate_readme(template, project_name, requirements)
            self._write_file(project_dir / 'README.md', readme_content)
            generated_files.append('README.md')

            self.logger.info(f"Successfully generated {len(generated_files)} files for {project_type} project")

            return {
                'success': True,
                'project_path': str(project_dir),
                'generated_files': generated_files,
                'template': template.name,
                'framework': template.framework,
                'features': template.features,
                'next_steps': self._generate_next_steps(template, project_name)
            }

        except Exception as e:
            self.logger.error(f"Failed to generate enhanced project: {e}")
            return {
                'success': False,
                'error': str(e),
                'project_path': None,
                'generated_files': []
            }

    def _create_react_template(self) -> ProjectTemplate:
        """Create React application template with modern tooling"""
        return ProjectTemplate(
            name="Modern React Application",
            framework="react",
            language="javascript",
            features=["components", "routing", "state_management", "api_integration"],
            dependencies=[
                "react@^18.2.0",
                "react-dom@^18.2.0",
                "react-router-dom@^6.8.0",
                "axios@^1.3.0"
            ],
            dev_dependencies=[
                "@vitejs/plugin-react@^3.1.0",
                "vite@^4.1.0",
                "eslint@^8.34.0",
                "@types/react@^18.0.28",
                "@types/react-dom@^18.0.11"
            ],
            build_commands=["npm run build"],
            start_commands=["npm run dev"],
            file_structure={
                "src": {
                    "components": {},
                    "pages": {},
                    "hooks": {},
                    "utils": {},
                    "styles": {}
                },
                "public": {}
            },
            styling_framework="tailwind",
            authentication=False,
            database=None,
            real_time=False
        )

    def _create_vue_template(self) -> ProjectTemplate:
        """Create Vue.js application template with modern tooling"""
        return ProjectTemplate(
            name="Modern Vue.js Application",
            framework="vue",
            language="javascript",
            features=["components", "routing", "state_management", "api_integration"],
            dependencies=[
                "vue@^3.3.0",
                "vue-router@^4.2.0",
                "pinia@^2.1.0",
                "axios@^1.3.0"
            ],
            dev_dependencies=[
                "@vitejs/plugin-vue@^4.2.0",
                "vite@^4.1.0",
                "eslint@^8.34.0",
                "@vue/eslint-config-prettier@^7.1.0"
            ],
            build_commands=["npm run build"],
            start_commands=["npm run dev"],
            file_structure={
                "src": {
                    "components": {},
                    "views": {},
                    "stores": {},
                    "composables": {},
                    "assets": {}
                },
                "public": {}
            },
            styling_framework="vuetify",
            authentication=False,
            database=None,
            real_time=False
        )

    def _create_angular_template(self) -> ProjectTemplate:
        """Create Angular application template with modern tooling"""
        return ProjectTemplate(
            name="Modern Angular Application",
            framework="angular",
            language="typescript",
            features=["components", "routing", "services", "reactive_forms"],
            dependencies=[
                "@angular/core@^16.0.0",
                "@angular/common@^16.0.0",
                "@angular/router@^16.0.0",
                "@angular/forms@^16.0.0",
                "rxjs@^7.8.0"
            ],
            dev_dependencies=[
                "@angular/cli@^16.0.0",
                "@angular-devkit/build-angular@^16.0.0",
                "typescript@^5.0.0",
                "@types/node@^18.0.0"
            ],
            build_commands=["ng build"],
            start_commands=["ng serve"],
            file_structure={
                "src": {
                    "app": {
                        "components": {},
                        "services": {},
                        "models": {},
                        "guards": {}
                    },
                    "assets": {},
                    "environments": {}
                }
            },
            styling_framework="angular_material",
            authentication=False,
            database=None,
            real_time=False
        )

    def _create_nodejs_template(self) -> ProjectTemplate:
        """Create Node.js API template with modern tooling"""
        return ProjectTemplate(
            name="Modern Node.js API",
            framework="nodejs",
            language="javascript",
            features=["express", "middleware", "routing", "validation"],
            dependencies=[
                "express@^4.18.0",
                "cors@^2.8.5",
                "helmet@^7.0.0",
                "morgan@^1.10.0",
                "joi@^17.9.0",
                "dotenv@^16.0.0"
            ],
            dev_dependencies=[
                "nodemon@^2.0.22",
                "jest@^29.5.0",
                "supertest@^6.3.0",
                "eslint@^8.34.0"
            ],
            build_commands=["npm run build"],
            start_commands=["npm run dev"],
            file_structure={
                "src": {
                    "controllers": {},
                    "middleware": {},
                    "models": {},
                    "routes": {},
                    "utils": {}
                },
                "tests": {}
            },
            styling_framework=None,
            authentication=True,
            database="mongodb",
            real_time=False
        )

    def _create_django_template(self) -> ProjectTemplate:
        """Create Django application template with modern tooling"""
        return ProjectTemplate(
            name="Modern Django Application",
            framework="django",
            language="python",
            features=["mvc", "admin", "orm", "authentication"],
            dependencies=[
                "Django>=4.2.0",
                "djangorestframework>=3.14.0",
                "django-cors-headers>=4.0.0",
                "python-decouple>=3.8",
                "Pillow>=9.5.0"
            ],
            dev_dependencies=[
                "pytest-django>=4.5.0",
                "black>=23.0.0",
                "flake8>=6.0.0",
                "django-debug-toolbar>=4.0.0"
            ],
            build_commands=["python manage.py collectstatic"],
            start_commands=["python manage.py runserver"],
            file_structure={
                "apps": {},
                "static": {},
                "templates": {},
                "media": {},
                "config": {}
            },
            styling_framework="bootstrap",
            authentication=True,
            database="postgresql",
            real_time=False
        )

    def _create_spring_boot_template(self) -> ProjectTemplate:
        """Create Spring Boot API template with modern tooling"""
        return ProjectTemplate(
            name="Modern Spring Boot API",
            framework="spring_boot",
            language="java",
            features=["rest_api", "jpa", "security", "validation"],
            dependencies=[
                "org.springframework.boot:spring-boot-starter-web",
                "org.springframework.boot:spring-boot-starter-data-jpa",
                "org.springframework.boot:spring-boot-starter-security",
                "org.springframework.boot:spring-boot-starter-validation",
                "com.h2database:h2"
            ],
            dev_dependencies=[
                "org.springframework.boot:spring-boot-starter-test",
                "org.springframework.security:spring-security-test"
            ],
            build_commands=["./mvnw clean package"],
            start_commands=["./mvnw spring-boot:run"],
            file_structure={
                "src/main/java": {
                    "controller": {},
                    "service": {},
                    "repository": {},
                    "model": {},
                    "config": {}
                },
                "src/main/resources": {},
                "src/test/java": {}
            },
            styling_framework=None,
            authentication=True,
            database="h2",
            real_time=False
        )

    def _create_react_native_template(self) -> ProjectTemplate:
        """Create React Native mobile app template"""
        return ProjectTemplate(
            name="Modern React Native App",
            framework="react_native",
            language="javascript",
            features=["navigation", "state_management", "api_integration", "native_modules"],
            dependencies=[
                "react-native@^0.72.0",
                "@react-navigation/native@^6.1.0",
                "@react-navigation/stack@^6.3.0",
                "@reduxjs/toolkit@^1.9.0",
                "react-redux@^8.1.0"
            ],
            dev_dependencies=[
                "@babel/core@^7.20.0",
                "@babel/preset-env@^7.20.0",
                "@babel/runtime@^7.20.0",
                "metro-react-native-babel-preset@^0.76.0"
            ],
            build_commands=["npx react-native run-android", "npx react-native run-ios"],
            start_commands=["npx react-native start"],
            file_structure={
                "src": {
                    "components": {},
                    "screens": {},
                    "navigation": {},
                    "store": {},
                    "services": {}
                },
                "android": {},
                "ios": {}
            },
            styling_framework="native_base",
            authentication=True,
            database=None,
            real_time=False
        )

    def _create_flutter_template(self) -> ProjectTemplate:
        """Create Flutter mobile app template"""
        return ProjectTemplate(
            name="Modern Flutter App",
            framework="flutter",
            language="dart",
            features=["widgets", "state_management", "navigation", "api_integration"],
            dependencies=[
                "flutter",
                "provider: ^6.0.5",
                "http: ^1.1.0",
                "shared_preferences: ^2.2.0"
            ],
            dev_dependencies=[
                "flutter_test",
                "flutter_lints: ^2.0.0"
            ],
            build_commands=["flutter build apk", "flutter build ios"],
            start_commands=["flutter run"],
            file_structure={
                "lib": {
                    "screens": {},
                    "widgets": {},
                    "models": {},
                    "services": {},
                    "providers": {}
                },
                "assets": {},
                "test": {}
            },
            styling_framework="material_design",
            authentication=True,
            database=None,
            real_time=False
        )

    def _create_fullstack_template(self) -> ProjectTemplate:
        """Create full-stack React + Node.js template"""
        return ProjectTemplate(
            name="Full-Stack React + Node.js Application",
            framework="fullstack",
            language="javascript",
            features=["frontend", "backend", "api", "database", "authentication"],
            dependencies=[
                "react@^18.2.0",
                "react-dom@^18.2.0",
                "express@^4.18.0",
                "mongoose@^7.3.0",
                "jsonwebtoken@^9.0.0"
            ],
            dev_dependencies=[
                "@vitejs/plugin-react@^3.1.0",
                "vite@^4.1.0",
                "nodemon@^2.0.22",
                "concurrently@^8.2.0"
            ],
            build_commands=["npm run build"],
            start_commands=["npm run dev"],
            file_structure={
                "client": {
                    "src": {
                        "components": {},
                        "pages": {},
                        "hooks": {},
                        "services": {}
                    }
                },
                "server": {
                    "controllers": {},
                    "models": {},
                    "routes": {},
                    "middleware": {}
                }
            },
            styling_framework="tailwind",
            authentication=True,
            database="mongodb",
            real_time=True
        )

    def _create_package_json(self, template: ProjectTemplate, project_name: str,
                           requirements: Dict[str, Any]) -> Dict[str, Any]:
        """Create package.json for JavaScript/TypeScript projects"""
        return {
            "name": project_name.lower().replace(" ", "-"),
            "version": "1.0.0",
            "description": f"A {template.name} project",
            "main": "index.js" if template.framework == "nodejs" else "src/main.jsx",
            "scripts": {
                "dev": template.start_commands[0].replace("npm run ", ""),
                "build": template.build_commands[0].replace("npm run ", ""),
                "test": "jest",
                "lint": "eslint src --ext .js,.jsx,.ts,.tsx"
            },
            "dependencies": {dep.split("@")[0]: dep.split("@")[1] if "@" in dep else "latest"
                           for dep in template.dependencies},
            "devDependencies": {dep.split("@")[0]: dep.split("@")[1] if "@" in dep else "latest"
                              for dep in template.dev_dependencies},
            "keywords": template.features,
            "author": "AI Coding Agent",
            "license": "MIT"
        }

    def _generate_application_files(self, template: ProjectTemplate,
                                  requirements: Dict[str, Any]) -> Dict[str, str]:
        """Generate main application files based on template"""
        files = {}

        if template.framework == "react":
            files.update(self._generate_react_files(template, requirements))
        elif template.framework == "vue":
            files.update(self._generate_vue_files(template, requirements))
        elif template.framework == "angular":
            files.update(self._generate_angular_files(template, requirements))
        elif template.framework == "nodejs":
            files.update(self._generate_nodejs_files(template, requirements))
        elif template.framework == "django":
            files.update(self._generate_django_files(template, requirements))
        elif template.framework == "spring_boot":
            files.update(self._generate_spring_boot_files(template, requirements))
        elif template.framework == "react_native":
            files.update(self._generate_react_native_files(template, requirements))
        elif template.framework == "flutter":
            files.update(self._generate_flutter_files(template, requirements))
        elif template.framework == "fullstack":
            files.update(self._generate_fullstack_files(template, requirements))

        return files

    def _generate_configuration_files(self, template: ProjectTemplate,
                                    requirements: Dict[str, Any]) -> Dict[str, str]:
        """Generate configuration files"""
        files = {}

        # Common configuration files
        if template.framework in ["react", "vue", "angular"]:
            files["vite.config.js"] = self._create_vite_config(template)
            files[".eslintrc.js"] = self._create_eslint_config(template)

        if template.framework == "nodejs":
            files[".env.example"] = self._create_env_example(template)
            files["nodemon.json"] = self._create_nodemon_config()

        if template.framework == "django":
            files["requirements.txt"] = "\n".join(template.dependencies)
            files["settings.py"] = self._create_django_settings(template)

        return files

    def _generate_styling_files(self, template: ProjectTemplate,
                              requirements: Dict[str, Any]) -> Dict[str, str]:
        """Generate styling and CSS framework files"""
        files = {}

        if template.styling_framework == "tailwind":
            files["tailwind.config.js"] = self._create_tailwind_config()
            files["src/index.css"] = self._create_tailwind_css()
        elif template.styling_framework == "bootstrap":
            files["src/styles.css"] = self._create_bootstrap_css()
        elif template.styling_framework == "material_design":
            files["lib/theme.dart"] = self._create_material_theme()

        return files

    def _generate_react_files(self, template: ProjectTemplate, requirements: Dict[str, Any]) -> Dict[str, str]:
        """Generate React application files"""
        return {
            "src/main.jsx": self._create_react_main(),
            "src/App.jsx": self._create_react_app(requirements),
            "src/components/Header.jsx": self._create_react_header(),
            "index.html": self._create_react_index_html(),
            "public/vite.svg": ""  # Placeholder for Vite logo
        }

    def _generate_nodejs_files(self, template: ProjectTemplate, requirements: Dict[str, Any]) -> Dict[str, str]:
        """Generate Node.js application files"""
        return {
            "src/app.js": self._create_nodejs_app(requirements),
            "src/routes/index.js": self._create_nodejs_routes(),
            "src/controllers/homeController.js": self._create_nodejs_controller(),
            "src/middleware/auth.js": self._create_nodejs_auth_middleware() if template.authentication else "",
            "server.js": self._create_nodejs_server()
        }

    def _generate_readme(self, template: ProjectTemplate, project_name: str,
                        requirements: Dict[str, Any]) -> str:
        """Generate comprehensive README.md"""
        return f"""# {project_name}

A {template.name} built with the AI Coding Agent.

## Features

{chr(10).join(f"- {feature.replace('_', ' ').title()}" for feature in template.features)}

## Technology Stack

- **Framework**: {template.framework.title()}
- **Language**: {template.language.title()}
- **Styling**: {template.styling_framework or 'Custom CSS'}
- **Database**: {template.database or 'None'}
- **Authentication**: {'Yes' if template.authentication else 'No'}

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   {template.start_commands[0]}
   ```

### Build for Production

```bash
{template.build_commands[0]}
```

## Project Structure

```
{project_name}/
├── src/
│   ├── components/
│   ├── pages/
│   └── utils/
├── public/
└── package.json
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## License

MIT License
"""

    def _generate_next_steps(self, template: ProjectTemplate, project_name: str) -> List[str]:
        """Generate next steps for the user"""
        steps = [
            f"Navigate to the project directory: cd {project_name}",
            "Install dependencies: npm install",
            f"Start development server: {template.start_commands[0]}",
            "Open your browser and navigate to http://localhost:3000"
        ]

        if template.authentication:
            steps.append("Configure authentication settings in the .env file")

        if template.database:
            steps.append(f"Set up {template.database} database connection")

        return steps

    def _write_file(self, file_path: Path, content: str):
        """Write content to file"""
        try:
            file_path.parent.mkdir(parents=True, exist_ok=True)
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
        except Exception as e:
            self.logger.error(f"Failed to write file {file_path}: {e}")

    # React file templates
    def _create_react_main(self) -> str:
        return """import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.jsx'
import './index.css'

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)
"""

    def _create_react_app(self, requirements: Dict[str, Any]) -> str:
        app_name = requirements.get('name', 'My App')
        return f"""import {{ useState }} from 'react'
import Header from './components/Header'
import './App.css'

function App() {{
  const [count, setCount] = useState(0)

  return (
    <div className="App">
      <Header title="{app_name}" />
      <main>
        <h1>Welcome to {app_name}</h1>
        <div className="card">
          <button onClick={{() => setCount((count) => count + 1)}}>
            count is {{count}}
          </button>
          <p>
            Edit <code>src/App.jsx</code> and save to test HMR
          </p>
        </div>
      </main>
    </div>
  )
}}

export default App
"""

    def _create_react_header(self) -> str:
        return """import React from 'react'

const Header = ({ title }) => {
  return (
    <header className="header">
      <nav>
        <h1>{title}</h1>
        <ul>
          <li><a href="/">Home</a></li>
          <li><a href="/about">About</a></li>
        </ul>
      </nav>
    </header>
  )
}

export default Header
"""

    def _create_react_index_html(self) -> str:
        return """<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>React App</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
"""

    # Node.js file templates
    def _create_nodejs_app(self, requirements: Dict[str, Any]) -> str:
        return """const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
require('dotenv').config();

const app = express();

// Middleware
app.use(helmet());
app.use(cors());
app.use(morgan('combined'));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Routes
app.use('/api', require('./routes'));

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});

module.exports = app;
"""

    def _create_nodejs_routes(self) -> str:
        return """const express = require('express');
const router = express.Router();
const homeController = require('../controllers/homeController');

router.get('/', homeController.getHome);
router.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

module.exports = router;
"""

    def _create_nodejs_controller(self) -> str:
        return """const getHome = (req, res) => {
  res.json({
    message: 'Welcome to the API',
    version: '1.0.0',
    endpoints: [
      'GET /api/',
      'GET /api/health'
    ]
  });
};

module.exports = {
  getHome
};
"""

    def _create_nodejs_server(self) -> str:
        return """const app = require('./src/app');

const PORT = process.env.PORT || 3000;

app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
});
"""

    # Placeholder methods for missing functionality
    def _generate_vue_files(self, template: ProjectTemplate, requirements: Dict[str, Any]) -> Dict[str, str]:
        return {"src/main.js": "// Vue.js main file", "src/App.vue": "<!-- Vue.js App component -->"}

    def _generate_angular_files(self, template: ProjectTemplate, requirements: Dict[str, Any]) -> Dict[str, str]:
        return {"src/main.ts": "// Angular main file", "src/app/app.component.ts": "// Angular App component"}

    def _generate_django_files(self, template: ProjectTemplate, requirements: Dict[str, Any]) -> Dict[str, str]:
        return {"manage.py": "# Django management script", "myapp/views.py": "# Django views"}

    def _generate_spring_boot_files(self, template: ProjectTemplate, requirements: Dict[str, Any]) -> Dict[str, str]:
        return {"src/main/java/Application.java": "// Spring Boot main class"}

    def _generate_react_native_files(self, template: ProjectTemplate, requirements: Dict[str, Any]) -> Dict[str, str]:
        return {"App.js": "// React Native App component", "index.js": "// React Native entry point"}

    def _generate_flutter_files(self, template: ProjectTemplate, requirements: Dict[str, Any]) -> Dict[str, str]:
        return {"lib/main.dart": "// Flutter main file", "pubspec.yaml": "# Flutter dependencies"}

    def _generate_fullstack_files(self, template: ProjectTemplate, requirements: Dict[str, Any]) -> Dict[str, str]:
        files = {}
        files.update(self._generate_react_files(template, requirements))
        files.update(self._generate_nodejs_files(template, requirements))
        return files

    def _create_vite_config(self, template: ProjectTemplate) -> str:
        return """import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000
  }
})
"""

    def _create_eslint_config(self, template: ProjectTemplate) -> str:
        return """module.exports = {
  env: {
    browser: true,
    es2021: true,
    node: true
  },
  extends: [
    'eslint:recommended',
    '@typescript-eslint/recommended'
  ],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 12,
    sourceType: 'module'
  },
  rules: {
    // Add custom rules here
  }
}
"""

    def _create_env_example(self, template: ProjectTemplate) -> str:
        return """# Environment Variables
PORT=3000
NODE_ENV=development
DATABASE_URL=mongodb://localhost:27017/myapp
JWT_SECRET=your-secret-key
"""

    def _create_nodemon_config(self) -> str:
        return """{
  "watch": ["src"],
  "ext": "js,json",
  "ignore": ["src/**/*.test.js"],
  "exec": "node src/app.js"
}
"""

    def _create_django_settings(self, template: ProjectTemplate) -> str:
        return """# Django settings placeholder
DEBUG = True
ALLOWED_HOSTS = []
"""

    def _create_tailwind_config(self) -> str:
        return """module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {},
  },
  plugins: [],
}
"""

    def _create_tailwind_css(self) -> str:
        return """@tailwind base;
@tailwind components;
@tailwind utilities;
"""

    def _create_bootstrap_css(self) -> str:
        return """@import 'bootstrap/dist/css/bootstrap.min.css';

/* Custom styles */
.header {
  background-color: #f8f9fa;
  padding: 1rem;
}
"""

    def _create_material_theme(self) -> str:
        return """import 'package:flutter/material.dart';

final ThemeData appTheme = ThemeData(
  primarySwatch: Colors.blue,
  visualDensity: VisualDensity.adaptivePlatformDensity,
);
"""

    def _create_nodejs_auth_middleware(self) -> str:
        return """const jwt = require('jsonwebtoken');

const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (token == null) return res.sendStatus(401);

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) return res.sendStatus(403);
    req.user = user;
    next();
  });
};

module.exports = { authenticateToken };
"""