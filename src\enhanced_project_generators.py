# AI Coding Agent - Enhanced Project Generators
"""
Enhanced project generators addressing critical capability gaps:
- Frontend Framework Support (React, Vue, Angular)
- Design System Integration (CSS frameworks, custom styling)
- Technology Stack Expansion (Node.js, Django, Spring Boot)
- Advanced Feature Templates (Authentication, real-time, business logic)
- Mobile Development Support (React Native, Flutter)
"""

import os
import json
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from logger import get_logger

logger = get_logger(__name__)

@dataclass
class ProjectTemplate:
    """Enhanced project template with comprehensive configuration"""
    name: str
    framework: str
    language: str
    features: List[str]
    dependencies: List[str]
    dev_dependencies: List[str]
    build_commands: List[str]
    start_commands: List[str]
    file_structure: Dict[str, Any]
    styling_framework: Optional[str] = None
    authentication: bool = False
    database: Optional[str] = None
    real_time: bool = False

class EnhancedProjectGenerator:
    """Enhanced project generator with modern framework support"""

    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        self.templates = self._initialize_templates()

    def _initialize_templates(self) -> Dict[str, ProjectTemplate]:
        """Initialize comprehensive project templates"""
        return {
            'react_app': self._create_react_template(),
            'vue_app': self._create_vue_template(),
            'angular_app': self._create_angular_template(),
            'nodejs_api': self._create_nodejs_template(),
            'django_app': self._create_django_template(),
            'spring_boot_api': self._create_spring_boot_template(),
            'react_native_app': self._create_react_native_template(),
            'flutter_app': self._create_flutter_template(),
            'fullstack_react_node': self._create_fullstack_template()
        }

    def generate_project(self, project_type: str, project_name: str,
                        project_path: str, requirements: Dict[str, Any]) -> Dict[str, Any]:
        """Generate enhanced project with modern frameworks and features"""
        try:
            self.logger.info(f"Generating enhanced {project_type} project: {project_name}")

            # Get template
            template = self.templates.get(project_type)
            if not template:
                raise ValueError(f"Unsupported project type: {project_type}")

            # Create project directory
            project_dir = Path(project_path) / project_name
            project_dir.mkdir(parents=True, exist_ok=True)

            # Generate project files
            generated_files = []

            # Create package.json or equivalent
            if template.framework in ['react', 'vue', 'angular', 'nodejs']:
                package_json = self._create_package_json(template, project_name, requirements)
                self._write_file(project_dir / 'package.json', json.dumps(package_json, indent=2))
                generated_files.append('package.json')

            # Create main application files
            app_files = self._generate_application_files(template, requirements)
            for file_path, content in app_files.items():
                full_path = project_dir / file_path
                full_path.parent.mkdir(parents=True, exist_ok=True)
                self._write_file(full_path, content)
                generated_files.append(file_path)

            # Create configuration files
            config_files = self._generate_configuration_files(template, requirements)
            for file_path, content in config_files.items():
                full_path = project_dir / file_path
                full_path.parent.mkdir(parents=True, exist_ok=True)
                self._write_file(full_path, content)
                generated_files.append(file_path)

            # Create styling files
            if template.styling_framework:
                style_files = self._generate_styling_files(template, requirements)
                for file_path, content in style_files.items():
                    full_path = project_dir / file_path
                    full_path.parent.mkdir(parents=True, exist_ok=True)
                    self._write_file(full_path, content)
                    generated_files.append(file_path)

            # Create README and documentation
            readme_content = self._generate_readme(template, project_name, requirements)
            self._write_file(project_dir / 'README.md', readme_content)
            generated_files.append('README.md')

            self.logger.info(f"Successfully generated {len(generated_files)} files for {project_type} project")

            return {
                'success': True,
                'project_path': str(project_dir),
                'generated_files': generated_files,
                'template': template.name,
                'framework': template.framework,
                'features': template.features,
                'next_steps': self._generate_next_steps(template, project_name)
            }

        except Exception as e:
            self.logger.error(f"Failed to generate enhanced project: {e}")
            return {
                'success': False,
                'error': str(e),
                'project_path': None,
                'generated_files': []
            }

    def _create_react_template(self) -> ProjectTemplate:
        """Create React application template with modern tooling"""
        return ProjectTemplate(
            name="Modern React Application",
            framework="react",
            language="javascript",
            features=["components", "routing", "state_management", "api_integration"],
            dependencies=[
                "react@^18.2.0",
                "react-dom@^18.2.0",
                "react-router-dom@^6.8.0",
                "axios@^1.3.0"
            ],
            dev_dependencies=[
                "@vitejs/plugin-react@^3.1.0",
                "vite@^4.1.0",
                "eslint@^8.34.0",
                "@types/react@^18.0.28",
                "@types/react-dom@^18.0.11"
            ],
            build_commands=["npm run build"],
            start_commands=["npm run dev"],
            file_structure={
                "src": {
                    "components": {},
                    "pages": {},
                    "hooks": {},
                    "utils": {},
                    "styles": {}
                },
                "public": {}
            },
            styling_framework="tailwind",
            authentication=False,
            database=None,
            real_time=False
        )