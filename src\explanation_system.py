# AI Coding Agent - Context-Aware Explanation System
"""
Intelligent explanation system that adapts technical depth and communication style
based on user's technical level and previous interactions
"""

from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import re

from src.logger import get_logger
from src.models import LLMManager
from src.requirements_parser import UserProfile, TechnicalLevel, ProjectRequirement
from src.conversation import ConversationManager


class ExplanationType(Enum):
    """Types of explanations the system can provide"""
    CONCEPT_EXPLANATION = "concept_explanation"
    TECHNICAL_BREAKDOWN = "technical_breakdown"
    STEP_BY_STEP_GUIDE = "step_by_step_guide"
    COMPARISON = "comparison"
    EXAMPLE_DEMONSTRATION = "example_demonstration"
    TROUBLESHOOTING = "troubleshooting"
    BEST_PRACTICES = "best_practices"


class CommunicationStyle(Enum):
    """Communication styles for different user preferences"""
    FRIENDLY = "friendly"
    PROFESSIONAL = "professional"
    TECHNICAL = "technical"
    ENCOURAGING = "encouraging"
    CONCISE = "concise"


@dataclass
class ExplanationContext:
    """Context information for generating explanations"""
    topic: str
    user_question: str
    technical_level: TechnicalLevel
    communication_style: CommunicationStyle
    previous_interactions: List[str]
    project_context: Optional[ProjectRequirement] = None
    conversation_history: str = ""
    user_confusion_indicators: List[str] = None
    preferred_learning_style: str = "mixed"  # visual, textual, example-based, mixed


@dataclass
class Explanation:
    """Represents a context-aware explanation"""
    id: str
    content: str
    explanation_type: ExplanationType
    technical_level: TechnicalLevel
    communication_style: CommunicationStyle
    examples: List[str]
    follow_up_suggestions: List[str]
    related_concepts: List[str]
    confidence_score: float
    metadata: Dict[str, Any]


class ExplanationSystem:
    """
    Context-aware explanation system that adapts to user's technical level,
    communication preferences, and learning patterns
    """
    
    def __init__(self, llm_manager: Optional[LLMManager] = None,
                 conversation_manager: Optional[ConversationManager] = None):
        """Initialize the explanation system"""
        self.logger = get_logger(__name__)
        self.llm_manager = llm_manager or LLMManager()
        self.conversation_manager = conversation_manager or ConversationManager()
        
        # Communication style templates
        self.style_templates = self._initialize_style_templates()
        
        # Technical level adaptations
        self.level_adaptations = self._initialize_level_adaptations()
        
        # Concept complexity mappings
        self.concept_complexity = self._initialize_concept_complexity()
        
        self.logger.info("ExplanationSystem initialized with adaptive communication capabilities")
    
    def explain(self, topic: str, user_question: str, context: ExplanationContext) -> Explanation:
        """
        Generate a context-aware explanation adapted to the user's needs
        
        Args:
            topic: The main topic to explain
            user_question: The specific question asked by the user
            context: Context information including user profile and history
            
        Returns:
            Explanation object with adapted content
        """
        try:
            self.logger.info(f"Generating explanation for topic: {topic}")
            
            # Analyze user's current understanding level
            understanding_level = self._assess_user_understanding(context)
            
            # Determine optimal explanation type
            explanation_type = self._determine_explanation_type(topic, user_question, context)
            
            # Generate base explanation using LLM
            base_explanation = self._generate_base_explanation(topic, user_question, context, explanation_type)
            
            # Adapt explanation to user's technical level
            adapted_content = self._adapt_to_technical_level(base_explanation, context.technical_level, understanding_level)
            
            # Apply communication style
            styled_content = self._apply_communication_style(adapted_content, context.communication_style)
            
            # Generate examples and follow-ups
            examples = self._generate_examples(topic, context)
            follow_ups = self._generate_follow_up_suggestions(topic, context)
            related_concepts = self._identify_related_concepts(topic, context.technical_level)
            
            # Calculate confidence score
            confidence = self._calculate_explanation_confidence(styled_content, context)
            
            explanation = Explanation(
                id=f"exp_{hash(topic + user_question) % 10000}",
                content=styled_content,
                explanation_type=explanation_type,
                technical_level=context.technical_level,
                communication_style=context.communication_style,
                examples=examples,
                follow_up_suggestions=follow_ups,
                related_concepts=related_concepts,
                confidence_score=confidence,
                metadata={
                    "topic": topic,
                    "understanding_level": understanding_level,
                    "adaptation_applied": True
                }
            )
            
            self.logger.info(f"Generated explanation with confidence: {confidence:.2f}")
            return explanation
            
        except Exception as e:
            self.logger.error(f"Failed to generate explanation: {str(e)}")
            return self._generate_fallback_explanation(topic, user_question, context)
    
    def _assess_user_understanding(self, context: ExplanationContext) -> str:
        """Assess user's current understanding level based on context"""
        understanding_indicators = {
            "novice": ["what is", "how do", "explain", "don't understand", "confused"],
            "basic": ["difference between", "why", "when to use", "best practice"],
            "intermediate": ["how to implement", "optimize", "integrate", "customize"],
            "advanced": ["architecture", "performance", "scalability", "security"]
        }
        
        question_lower = context.user_question.lower()
        
        # Check for confusion indicators
        if context.user_confusion_indicators:
            confusion_words = ["confused", "lost", "don't get it", "unclear", "help"]
            if any(word in question_lower for word in confusion_words):
                return "needs_clarification"
        
        # Analyze question complexity
        for level, indicators in understanding_indicators.items():
            if any(indicator in question_lower for indicator in indicators):
                return level
        
        # Default based on technical level
        level_mapping = {
            TechnicalLevel.BEGINNER: "novice",
            TechnicalLevel.INTERMEDIATE: "basic",
            TechnicalLevel.ADVANCED: "intermediate",
            TechnicalLevel.EXPERT: "advanced"
        }
        
        return level_mapping.get(context.technical_level, "basic")
    
    def _determine_explanation_type(self, topic: str, user_question: str, 
                                  context: ExplanationContext) -> ExplanationType:
        """Determine the most appropriate explanation type"""
        question_lower = user_question.lower()
        
        # Pattern matching for explanation types
        if any(word in question_lower for word in ["what is", "define", "explain"]):
            return ExplanationType.CONCEPT_EXPLANATION
        elif any(word in question_lower for word in ["how to", "steps", "guide"]):
            return ExplanationType.STEP_BY_STEP_GUIDE
        elif any(word in question_lower for word in ["difference", "compare", "vs", "versus"]):
            return ExplanationType.COMPARISON
        elif any(word in question_lower for word in ["example", "show me", "demonstrate"]):
            return ExplanationType.EXAMPLE_DEMONSTRATION
        elif any(word in question_lower for word in ["error", "problem", "issue", "fix"]):
            return ExplanationType.TROUBLESHOOTING
        elif any(word in question_lower for word in ["best", "recommended", "should"]):
            return ExplanationType.BEST_PRACTICES
        elif context.technical_level in [TechnicalLevel.ADVANCED, TechnicalLevel.EXPERT]:
            return ExplanationType.TECHNICAL_BREAKDOWN
        else:
            return ExplanationType.CONCEPT_EXPLANATION
    
    def _generate_base_explanation(self, topic: str, user_question: str,
                                 context: ExplanationContext, 
                                 explanation_type: ExplanationType) -> str:
        """Generate base explanation using LLM"""
        prompt = self._build_explanation_prompt(topic, user_question, context, explanation_type)
        
        try:
            response = self.llm_manager.generate_response_with_context(
                prompt=prompt,
                context=context.conversation_history,
                role="assistant"
            )
            return response
        except Exception as e:
            self.logger.warning(f"LLM explanation generation failed: {e}")
            # Return None to trigger fallback explanation in main method
            raise e
    
    def _build_explanation_prompt(self, topic: str, user_question: str,
                                context: ExplanationContext,
                                explanation_type: ExplanationType) -> str:
        """Build sophisticated prompt for explanation generation"""
        
        # Get style guidance
        style_guidance = self.style_templates.get(context.communication_style, {})
        level_guidance = self.level_adaptations.get(context.technical_level, {})
        
        prompt = f"""You are an expert technical communicator. Provide a clear, helpful explanation.

User Question: "{user_question}"
Topic: {topic}
Explanation Type: {explanation_type.value}

User Profile:
- Technical Level: {context.technical_level.value}
- Communication Style: {context.communication_style.value}
- Previous Context: {context.conversation_history[:200] if context.conversation_history else 'None'}

Style Guidelines:
- Tone: {style_guidance.get('tone', 'helpful and clear')}
- Language: {level_guidance.get('language', 'appropriate for level')}
- Detail Level: {level_guidance.get('detail_level', 'balanced')}

Requirements:
1. {level_guidance.get('requirements', ['Be clear and helpful'])[0] if level_guidance.get('requirements') else 'Be clear and helpful'}
2. Use {level_guidance.get('preferred_format', 'structured explanation')}
3. {style_guidance.get('personality', 'Be professional and supportive')}

Provide a comprehensive explanation that matches the user's level and style preferences."""
        
        return prompt

    def _adapt_to_technical_level(self, content: str, technical_level: TechnicalLevel,
                                understanding_level: str) -> str:
        """Adapt explanation content to user's technical level"""

        if technical_level == TechnicalLevel.BEGINNER or understanding_level == "novice":
            # Simplify technical terms
            content = self._simplify_technical_terms(content)
            # Add more context and background
            content = self._add_beginner_context(content)

        elif technical_level == TechnicalLevel.EXPERT or understanding_level == "advanced":
            # Add technical depth
            content = self._add_technical_depth(content)
            # Include advanced considerations
            content = self._add_advanced_considerations(content)

        return content

    def _simplify_technical_terms(self, content: str) -> str:
        """Replace complex technical terms with simpler explanations"""
        simplifications = {
            "API": "API (a way for different programs to talk to each other)",
            "database": "database (a place where information is stored)",
            "framework": "framework (a set of tools that makes building software easier)",
            "algorithm": "algorithm (a set of steps to solve a problem)",
            "deployment": "deployment (putting your application online so others can use it)",
            "authentication": "authentication (verifying who someone is, like logging in)",
            "scalability": "scalability (ability to handle more users as your app grows)",
            "microservices": "microservices (breaking a big application into smaller, independent parts)"
        }

        for term, explanation in simplifications.items():
            # Case-insensitive replacement
            pattern = re.compile(re.escape(term), re.IGNORECASE)
            content = pattern.sub(explanation, content)

        return content

    def _add_beginner_context(self, content: str) -> str:
        """Add helpful context for beginners"""
        # Add encouraging introduction if not present
        if not any(phrase in content.lower() for phrase in ["great question", "let me explain", "don't worry"]):
            content = "Great question! Let me break this down for you. " + content

        # Add reassuring conclusion
        if not content.endswith(("!", ".", "?")):
            content += "."

        content += "\n\nDon't worry if this seems complex at first - these concepts become clearer with practice!"

        return content

    def _add_technical_depth(self, content: str) -> str:
        """Add technical depth for advanced users"""
        # This would typically involve more sophisticated analysis
        # For now, we'll add a note about implementation details
        if "implementation" not in content.lower():
            content += "\n\nFrom an implementation perspective, consider the architectural patterns and performance implications of your chosen approach."

        return content

    def _add_advanced_considerations(self, content: str) -> str:
        """Add advanced considerations for expert users"""
        advanced_topics = [
            "Consider scalability and performance implications.",
            "Think about security and compliance requirements.",
            "Evaluate maintainability and technical debt.",
            "Consider integration patterns and system boundaries."
        ]

        # Add relevant advanced considerations
        content += "\n\nAdvanced considerations:\n"
        content += "\n".join(f"• {topic}" for topic in advanced_topics[:2])

        return content

    def _apply_communication_style(self, content: str, style: CommunicationStyle) -> str:
        """Apply specific communication style to the content"""

        if style == CommunicationStyle.FRIENDLY:
            # Make it more conversational and warm
            content = self._make_friendly(content)
        elif style == CommunicationStyle.PROFESSIONAL:
            # Keep it formal and structured
            content = self._make_professional(content)
        elif style == CommunicationStyle.ENCOURAGING:
            # Add motivational elements
            content = self._make_encouraging(content)
        elif style == CommunicationStyle.CONCISE:
            # Make it more direct and brief
            content = self._make_concise(content)

        return content

    def _make_friendly(self, content: str) -> str:
        """Apply friendly communication style"""
        friendly_starters = [
            "I'd be happy to help you understand this!",
            "This is a great question!",
            "Let me walk you through this step by step."
        ]

        if not any(starter.lower() in content.lower() for starter in friendly_starters):
            content = friendly_starters[0] + " " + content

        # Add friendly transitions
        content = content.replace("However,", "That said,")
        content = content.replace("Therefore,", "So,")

        return content

    def _make_professional(self, content: str) -> str:
        """Apply professional communication style"""
        # Ensure formal language
        content = content.replace("you'll", "you will")
        content = content.replace("You'll", "You will")
        content = content.replace("don't", "do not")
        content = content.replace("Don't", "Do not")
        content = content.replace("can't", "cannot")
        content = content.replace("Can't", "Cannot")

        return content

    def _make_encouraging(self, content: str) -> str:
        """Apply encouraging communication style"""
        encouraging_phrases = [
            "You're on the right track!",
            "This is a smart question to ask.",
            "You're thinking about this correctly."
        ]

        # Add encouraging elements
        if not any(phrase in content for phrase in encouraging_phrases):
            content = encouraging_phrases[1] + " " + content

        # Add positive conclusion
        content += "\n\nYou've got this! Feel free to ask if you need any clarification."

        return content

    def _make_concise(self, content: str) -> str:
        """Apply concise communication style"""
        # Remove unnecessary words and phrases
        content = content.replace("In order to", "To")
        content = content.replace("It is important to note that", "Note:")
        content = content.replace("Please be aware that", "")

        # Split into shorter sentences
        sentences = content.split('. ')
        concise_sentences = []

        for sentence in sentences:
            if len(sentence) > 100:  # Long sentence
                # Try to split on conjunctions
                parts = re.split(r'\s+(and|but|however|therefore)\s+', sentence)
                concise_sentences.extend(parts)
            else:
                concise_sentences.append(sentence)

        return '. '.join(concise_sentences)

    def _generate_examples(self, topic: str, context: ExplanationContext) -> List[str]:
        """Generate relevant examples based on topic and context"""
        examples = []

        # Project-specific examples
        if context.project_context:
            project_type = context.project_context.project_type.value
            examples.append(f"In your {project_type} project, this would mean...")

        # Level-appropriate examples
        if context.technical_level == TechnicalLevel.BEGINNER:
            examples.extend([
                "Think of it like organizing files on your computer",
                "Similar to how you might organize a recipe book"
            ])
        else:
            examples.extend([
                "For instance, in a typical web application...",
                "Consider a real-world scenario where..."
            ])

        return examples[:3]  # Limit to 3 examples

    def _generate_follow_up_suggestions(self, topic: str, context: ExplanationContext) -> List[str]:
        """Generate helpful follow-up suggestions"""
        suggestions = [
            f"Would you like me to explain how {topic} relates to your specific project?",
            "Should we dive deeper into any particular aspect?",
            "Are there any specific challenges you're facing with this?"
        ]

        # Customize based on technical level
        if context.technical_level == TechnicalLevel.BEGINNER:
            suggestions.append("Would you like to see a simple example to make this clearer?")
        elif context.technical_level in [TechnicalLevel.ADVANCED, TechnicalLevel.EXPERT]:
            suggestions.append("Would you like to discuss implementation strategies or best practices?")

        return suggestions[:3]

    def _identify_related_concepts(self, topic: str, technical_level: TechnicalLevel) -> List[str]:
        """Identify related concepts based on topic and user level"""
        concept_map = {
            "api": ["REST", "HTTP", "JSON", "endpoints", "authentication"],
            "database": ["SQL", "tables", "queries", "relationships", "indexing"],
            "web app": ["HTML", "CSS", "JavaScript", "frontend", "backend"],
            "authentication": ["security", "passwords", "tokens", "sessions", "authorization"],
            "deployment": ["hosting", "servers", "cloud", "CI/CD", "monitoring"]
        }

        topic_lower = topic.lower()
        related = []

        for key, concepts in concept_map.items():
            if key in topic_lower:
                # Filter concepts based on technical level
                if technical_level == TechnicalLevel.BEGINNER:
                    related.extend(concepts[:2])  # Basic concepts only
                else:
                    related.extend(concepts)
                break

        return related[:5]  # Limit to 5 related concepts

    def _calculate_explanation_confidence(self, content: str, context: ExplanationContext) -> float:
        """Calculate confidence score for the explanation"""
        score = 0.5  # Base score

        # Content quality indicators
        if len(content) > 100:  # Substantial content
            score += 0.2

        if any(word in content.lower() for word in ["example", "for instance", "such as"]):
            score += 0.1  # Has examples

        if context.technical_level != TechnicalLevel.BEGINNER and "implementation" in content.lower():
            score += 0.1  # Technical depth for advanced users

        # Context relevance
        if context.project_context and context.project_context.project_type.value in content.lower():
            score += 0.1  # Project-relevant

        return min(score, 1.0)

    def _generate_fallback_explanation(self, topic: str, user_question: str,
                                     context: ExplanationContext) -> Explanation:
        """Generate a basic fallback explanation when main generation fails"""
        fallback_content = f"I'd be happy to help explain {topic}. "

        if context.technical_level == TechnicalLevel.BEGINNER:
            fallback_content += f"Let me break down {topic} in simple terms. "
        else:
            fallback_content += f"Here's an overview of {topic}. "

        fallback_content += "Could you let me know what specific aspect you'd like me to focus on?"

        return Explanation(
            id=f"fallback_{hash(topic) % 1000}",
            content=fallback_content,
            explanation_type=ExplanationType.CONCEPT_EXPLANATION,
            technical_level=context.technical_level,
            communication_style=context.communication_style,
            examples=[],
            follow_up_suggestions=["What specific aspect interests you most?"],
            related_concepts=[],
            confidence_score=0.3,
            metadata={"fallback": True}
        )

    def _generate_simple_explanation(self, topic: str, technical_level: TechnicalLevel) -> str:
        """Generate a simple explanation when LLM is unavailable"""
        simple_explanations = {
            "api": "An API is a way for different software programs to communicate with each other.",
            "database": "A database is a structured way to store and organize information.",
            "web app": "A web application is a program that runs in a web browser.",
            "authentication": "Authentication is the process of verifying who someone is.",
            "deployment": "Deployment is the process of making your application available to users."
        }

        topic_lower = topic.lower()
        for key, explanation in simple_explanations.items():
            if key in topic_lower:
                return explanation

        return f"{topic} is an important concept in software development that I'd be happy to explain further."

    def _initialize_style_templates(self) -> Dict[CommunicationStyle, Dict[str, Any]]:
        """Initialize communication style templates"""
        return {
            CommunicationStyle.FRIENDLY: {
                "tone": "warm and conversational",
                "personality": "Be approachable and use inclusive language",
                "starters": ["I'd love to help!", "Great question!", "Let me explain this!"]
            },
            CommunicationStyle.PROFESSIONAL: {
                "tone": "formal and structured",
                "personality": "Be precise and authoritative",
                "starters": ["I will explain", "The concept involves", "This refers to"]
            },
            CommunicationStyle.TECHNICAL: {
                "tone": "precise and detailed",
                "personality": "Focus on accuracy and technical depth",
                "starters": ["Technically speaking", "From an implementation standpoint", "The architecture involves"]
            },
            CommunicationStyle.ENCOURAGING: {
                "tone": "supportive and motivating",
                "personality": "Build confidence and provide positive reinforcement",
                "starters": ["You're asking great questions!", "This is totally manageable!", "You're on the right track!"]
            },
            CommunicationStyle.CONCISE: {
                "tone": "direct and brief",
                "personality": "Get straight to the point",
                "starters": ["Simply put", "In essence", "The key point is"]
            }
        }

    def _initialize_level_adaptations(self) -> Dict[TechnicalLevel, Dict[str, Any]]:
        """Initialize technical level adaptations"""
        return {
            TechnicalLevel.BEGINNER: {
                "language": "simple and jargon-free",
                "detail_level": "high-level overview with context",
                "preferred_format": "step-by-step explanations with analogies",
                "requirements": [
                    "Avoid technical jargon",
                    "Provide plenty of context",
                    "Use analogies and examples",
                    "Be encouraging and patient"
                ]
            },
            TechnicalLevel.INTERMEDIATE: {
                "language": "balanced technical and accessible",
                "detail_level": "moderate detail with practical focus",
                "preferred_format": "structured explanation with examples",
                "requirements": [
                    "Balance technical accuracy with clarity",
                    "Include practical applications",
                    "Provide relevant examples",
                    "Suggest next steps"
                ]
            },
            TechnicalLevel.ADVANCED: {
                "language": "technical with depth",
                "detail_level": "detailed with implementation focus",
                "preferred_format": "comprehensive analysis with alternatives",
                "requirements": [
                    "Include technical depth",
                    "Discuss implementation details",
                    "Compare different approaches",
                    "Address edge cases"
                ]
            },
            TechnicalLevel.EXPERT: {
                "language": "highly technical and precise",
                "detail_level": "comprehensive with architectural considerations",
                "preferred_format": "detailed technical analysis",
                "requirements": [
                    "Provide comprehensive technical details",
                    "Discuss architectural implications",
                    "Include performance considerations",
                    "Address scalability and maintainability"
                ]
            }
        }

    def _initialize_concept_complexity(self) -> Dict[str, int]:
        """Initialize concept complexity ratings (1-10)"""
        return {
            "variable": 2,
            "function": 3,
            "api": 5,
            "database": 6,
            "authentication": 7,
            "microservices": 9,
            "distributed systems": 10,
            "machine learning": 8,
            "blockchain": 9,
            "quantum computing": 10
        }
