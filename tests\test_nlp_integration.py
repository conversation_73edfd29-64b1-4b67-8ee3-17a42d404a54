# AI Coding Agent - NLP Integration Tests
"""
Comprehensive test suite with diverse natural language inputs to validate
the NLP system handles various communication styles and project types
"""

import pytest
from unittest.mock import Mock
import json

from src.requirements_parser import RequirementParser, ProjectType, TechnicalLevel
from src.question_generator import QuestionGenerator
from src.explanation_system import ExplanationSystem, ExplanationContext, CommunicationStyle


class TestNLPIntegrationVariedInputs:
    """Test NLP system with diverse natural language inputs"""
    
    def setup_method(self):
        """Set up test fixtures"""
        # Mock LLM manager for consistent testing
        self.mock_llm_manager = Mock()
        
        # Initialize NLP components
        self.parser = RequirementParser(llm_manager=self.mock_llm_manager)
        self.question_generator = QuestionGenerator(llm_manager=self.mock_llm_manager)
        self.explanation_system = ExplanationSystem(llm_manager=self.mock_llm_manager)
    
    def test_casual_communication_style(self):
        """Test handling of casual, informal communication"""
        casual_inputs = [
            "hey can you help me build a cool app?",
            "i wanna make something like instagram but simpler",
            "need a website where ppl can share pics and stuff",
            "thinking about creating an app... not sure what tho",
            "my friend said i should build a web thing for my business"
        ]
        
        for user_input in casual_inputs:
            # Mock LLM response for casual input
            self.mock_llm_manager.generate_response_with_context.return_value = json.dumps({
                "project_type": "web_app",
                "title": "Social Media App",
                "description": "A simple photo sharing application",
                "features": ["photo upload", "user profiles", "sharing"],
                "technologies": ["html", "css", "javascript"],
                "user_stories": ["As a user, I want to share photos"],
                "technical_requirements": {"database": "sqlite"},
                "estimated_complexity": "medium",
                "constraints": []
            })
            
            requirements = self.parser.parse_requirements(user_input, "casual_user")
            
            # Should successfully parse casual input
            assert requirements is not None
            assert requirements.project_type in [ProjectType.WEB_APP, ProjectType.MOBILE_APP, ProjectType.UNKNOWN]
            assert requirements.confidence_score > 0.0
    
    def test_formal_technical_communication(self):
        """Test handling of formal, technical communication"""
        formal_inputs = [
            "I require a RESTful API service with microservices architecture for enterprise data management.",
            "Please develop a scalable web application utilizing React frontend and Node.js backend with PostgreSQL database.",
            "I need to implement a distributed system with load balancing and fault tolerance capabilities.",
            "Seeking to create a machine learning pipeline with automated model training and deployment.",
            "Require development of a secure authentication system with OAuth2 and JWT token management."
        ]
        
        for user_input in formal_inputs:
            # Mock LLM response for formal input
            self.mock_llm_manager.generate_response_with_context.return_value = json.dumps({
                "project_type": "api",
                "title": "Enterprise API System",
                "description": "A scalable enterprise-grade API service",
                "features": ["authentication", "data management", "scalability"],
                "technologies": ["node.js", "postgresql", "docker"],
                "user_stories": ["As an admin, I need secure API access"],
                "technical_requirements": {
                    "database": "postgresql",
                    "authentication": "oauth2",
                    "deployment": "microservices"
                },
                "estimated_complexity": "high",
                "constraints": ["enterprise security", "scalability"]
            })
            
            requirements = self.parser.parse_requirements(user_input, "technical_user")
            
            # Should handle technical terminology
            assert requirements is not None
            assert len(requirements.technologies) > 0
            assert requirements.estimated_complexity in ["medium", "high"]
    
    def test_beginner_communication_style(self):
        """Test handling of beginner-level communication with uncertainty"""
        beginner_inputs = [
            "I'm new to programming and want to make a simple website. Not sure where to start.",
            "Can you help me? I don't know much about coding but I have an idea for an app.",
            "I want to learn by building something. Maybe a game? I'm not sure what's possible.",
            "Complete beginner here. I heard about databases but don't understand them. Can you explain?",
            "I don't know the difference between frontend and backend. Can you help me build something simple?"
        ]
        
        for user_input in beginner_inputs:
            # Mock LLM response for beginner input
            self.mock_llm_manager.generate_response_with_context.return_value = json.dumps({
                "project_type": "web_app",
                "title": "Beginner Project",
                "description": "A simple learning project for beginners",
                "features": ["basic functionality", "simple interface"],
                "technologies": ["html", "css", "javascript"],
                "user_stories": ["As a beginner, I want to learn by doing"],
                "technical_requirements": {"database": "sqlite"},
                "estimated_complexity": "low",
                "constraints": ["beginner-friendly"]
            })
            
            requirements = self.parser.parse_requirements(user_input, "beginner_user")
            
            # Should identify beginner-level projects
            assert requirements is not None
            assert requirements.estimated_complexity in ["low", "medium"]
            assert len(requirements.clarifications_needed) > 0  # Should ask clarifying questions
    
    def test_specific_domain_projects(self):
        """Test handling of domain-specific project requests"""
        domain_inputs = [
            # E-commerce
            "I want to build an online store where customers can buy products and make payments.",
            # Healthcare
            "Need a patient management system for my medical practice with appointment scheduling.",
            # Education
            "Creating an online learning platform with courses, quizzes, and student progress tracking.",
            # Finance
            "Building a personal finance tracker with budget management and expense categorization.",
            # Gaming
            "Want to develop a 2D platformer game with multiple levels and character progression."
        ]
        
        expected_types = [
            ProjectType.WEB_APP,
            ProjectType.WEB_APP,
            ProjectType.WEB_APP,
            ProjectType.WEB_APP,
            ProjectType.GAME
        ]
        
        for i, user_input in enumerate(domain_inputs):
            # Mock appropriate LLM response based on domain
            domain_responses = [
                {"project_type": "web_app", "features": ["shopping cart", "payment", "inventory"]},
                {"project_type": "web_app", "features": ["appointments", "patient records", "scheduling"]},
                {"project_type": "web_app", "features": ["courses", "quizzes", "progress tracking"]},
                {"project_type": "web_app", "features": ["budgets", "expenses", "reports"]},
                {"project_type": "game", "features": ["levels", "characters", "progression"]}
            ]
            
            mock_response = {
                **domain_responses[i],
                "title": f"Domain Project {i}",
                "description": "Domain-specific application",
                "technologies": ["python", "html", "css"],
                "user_stories": ["As a user, I want domain functionality"],
                "technical_requirements": {"database": "sqlite"},
                "estimated_complexity": "medium",
                "constraints": []
            }
            
            self.mock_llm_manager.generate_response_with_context.return_value = json.dumps(mock_response)
            
            requirements = self.parser.parse_requirements(user_input, f"domain_user_{i}")
            
            # Should correctly identify domain-specific project types
            assert requirements is not None
            assert requirements.project_type == expected_types[i] or requirements.project_type == ProjectType.WEB_APP
            assert len(requirements.features) > 0
    
    def test_ambiguous_and_vague_inputs(self):
        """Test handling of ambiguous or vague project descriptions"""
        vague_inputs = [
            "I want to build something cool",
            "Can you help me with a project?",
            "I have an idea but it's complicated",
            "Need to create something for work",
            "Want to make an app that does stuff"
        ]
        
        for user_input in vague_inputs:
            # Mock LLM response for vague input
            self.mock_llm_manager.generate_response_with_context.return_value = json.dumps({
                "project_type": "unknown",
                "title": "Unclear Project",
                "description": "Project needs clarification",
                "features": [],
                "technologies": [],
                "user_stories": [],
                "technical_requirements": {},
                "estimated_complexity": "medium",
                "constraints": []
            })
            
            requirements = self.parser.parse_requirements(user_input, "vague_user")
            
            # Should handle vague input gracefully
            assert requirements is not None
            assert len(requirements.clarifications_needed) > 0  # Should ask for clarification
            assert requirements.confidence_score < 0.7  # Should have low confidence
    
    def test_multilingual_and_mixed_inputs(self):
        """Test handling of mixed language or non-standard inputs"""
        mixed_inputs = [
            "I want to créer une application web for my restaurant",
            "Need help building app... muy importante for business",
            "Can u help w/ website? thx!",
            "Building smth 4 mobile users - social media type thing",
            "API needed ASAP!!! Very urgent project!!!"
        ]
        
        for user_input in mixed_inputs:
            # Mock LLM response that handles mixed input
            self.mock_llm_manager.generate_response_with_context.return_value = json.dumps({
                "project_type": "web_app",
                "title": "Mixed Language Project",
                "description": "Project with mixed language input",
                "features": ["basic functionality"],
                "technologies": ["html", "css"],
                "user_stories": ["As a user, I want functionality"],
                "technical_requirements": {"database": "sqlite"},
                "estimated_complexity": "medium",
                "constraints": []
            })
            
            requirements = self.parser.parse_requirements(user_input, "mixed_user")
            
            # Should handle mixed inputs without crashing
            assert requirements is not None
            assert requirements.project_type != ProjectType.UNKNOWN or len(requirements.clarifications_needed) > 0
    
    def test_question_generation_variety(self):
        """Test question generation with different user profiles and contexts"""
        test_scenarios = [
            {
                "user_level": TechnicalLevel.BEGINNER,
                "project_type": ProjectType.WEB_APP,
                "description": "Simple website for local business"
            },
            {
                "user_level": TechnicalLevel.EXPERT,
                "project_type": ProjectType.API,
                "description": "High-performance microservices architecture"
            },
            {
                "user_level": TechnicalLevel.INTERMEDIATE,
                "project_type": ProjectType.MOBILE_APP,
                "description": "Cross-platform mobile application"
            }
        ]
        
        for scenario in test_scenarios:
            # Create requirements based on scenario
            from src.requirements_parser import ProjectRequirement, UserProfile
            
            requirements = ProjectRequirement(
                project_type=scenario["project_type"],
                description=scenario["description"],
                features=[],  # Missing features to trigger questions
                technologies=[]  # Missing technologies to trigger questions
            )
            
            user_profile = UserProfile(technical_level=scenario["user_level"])
            
            # Mock LLM response for question generation
            self.mock_llm_manager.generate_response_with_context.return_value = """
Q: What specific features would you like in your application?
Type: feature_clarification
Priority: 8
Hint: Think about what users will do most often

Q: Who will be using this application?
Type: user_experience
Priority: 7
Hint: Consider your target audience
"""
            
            questions = self.question_generator.generate_questions(requirements, user_profile)
            
            # Should generate appropriate questions for each scenario
            assert len(questions) > 0
            assert all(q.technical_level == scenario["user_level"] for q in questions)
    
    def test_explanation_adaptation(self):
        """Test explanation system adaptation to different communication styles"""
        explanation_scenarios = [
            {
                "topic": "API",
                "question": "What is an API?",
                "level": TechnicalLevel.BEGINNER,
                "style": CommunicationStyle.FRIENDLY
            },
            {
                "topic": "microservices",
                "question": "How do microservices work?",
                "level": TechnicalLevel.EXPERT,
                "style": CommunicationStyle.TECHNICAL
            },
            {
                "topic": "database",
                "question": "What database should I use?",
                "level": TechnicalLevel.INTERMEDIATE,
                "style": CommunicationStyle.PROFESSIONAL
            }
        ]
        
        for scenario in explanation_scenarios:
            # Mock LLM response for explanation
            self.mock_llm_manager.generate_response_with_context.return_value = f"""
            {scenario['topic']} is an important concept in software development.
            It allows different parts of your application to work together effectively.
            """
            
            context = ExplanationContext(
                topic=scenario["topic"],
                user_question=scenario["question"],
                technical_level=scenario["level"],
                communication_style=scenario["style"],
                previous_interactions=[]
            )
            
            explanation = self.explanation_system.explain(scenario["topic"], scenario["question"], context)
            
            # Should adapt explanation to user level and style
            assert explanation is not None
            assert explanation.technical_level == scenario["level"]
            assert explanation.communication_style == scenario["style"]
            assert len(explanation.content) > 0
    
    def test_error_handling_with_invalid_inputs(self):
        """Test system robustness with invalid or problematic inputs"""
        problematic_inputs = [
            "",  # Empty input
            "   ",  # Whitespace only
            "a" * 10000,  # Extremely long input
            "SELECT * FROM users; DROP TABLE users;",  # SQL injection attempt
            "<script>alert('xss')</script>",  # XSS attempt
            "🚀🎉💻🔥⭐",  # Emoji only
            "NULL",  # Null string
            "undefined",  # JavaScript undefined
        ]
        
        for problematic_input in problematic_inputs:
            try:
                # Mock LLM response for problematic input
                self.mock_llm_manager.generate_response_with_context.return_value = json.dumps({
                    "project_type": "unknown",
                    "title": "Invalid Input",
                    "description": "Input could not be processed",
                    "features": [],
                    "technologies": [],
                    "user_stories": [],
                    "technical_requirements": {},
                    "estimated_complexity": "low",
                    "constraints": []
                })
                
                requirements = self.parser.parse_requirements(problematic_input, "test_user")
                
                # Should handle problematic input gracefully
                assert requirements is not None
                # Should either have low confidence or ask for clarification
                assert (requirements.confidence_score < 0.5 or 
                       len(requirements.clarifications_needed) > 0)
                
            except Exception as e:
                # Should not crash, but if it does, it should be a known exception type
                from src.exceptions import RequirementParsingError
                assert isinstance(e, RequirementParsingError)
    
    def test_integration_workflow(self):
        """Test complete integration workflow from input to explanation"""
        user_input = "I want to build a social media app for photographers"
        user_id = "integration_test_user"
        
        # Step 1: Parse requirements
        self.mock_llm_manager.generate_response_with_context.return_value = json.dumps({
            "project_type": "web_app",
            "title": "Photography Social Network",
            "description": "A social media platform for photographers to share and discover photos",
            "features": ["photo upload", "user profiles", "social features", "discovery"],
            "technologies": ["react", "node.js", "mongodb"],
            "user_stories": [
                "As a photographer, I want to share my photos",
                "As a user, I want to discover new photographers"
            ],
            "technical_requirements": {
                "database": "mongodb",
                "authentication": "oauth",
                "storage": "cloud"
            },
            "estimated_complexity": "high",
            "constraints": ["mobile-friendly", "fast loading"]
        })
        
        requirements = self.parser.parse_requirements(user_input, user_id)
        
        # Step 2: Generate clarifying questions if needed
        from src.requirements_parser import UserProfile
        user_profile = UserProfile(technical_level=TechnicalLevel.INTERMEDIATE)
        
        self.mock_llm_manager.generate_response_with_context.return_value = """
Q: What makes your photography platform different from existing ones?
Type: feature_clarification
Priority: 9
Hint: Think about unique features for photographers

Q: Do you want to focus on any specific photography genre?
Type: scope_definition
Priority: 7
Hint: Portrait, landscape, street photography, etc.
"""
        
        questions = self.question_generator.generate_questions(requirements, user_profile)
        
        # Step 3: Provide explanations for technical concepts
        context = ExplanationContext(
            topic="web application architecture",
            user_question="How should I structure my photography app?",
            technical_level=TechnicalLevel.INTERMEDIATE,
            communication_style=CommunicationStyle.FRIENDLY,
            previous_interactions=[],
            project_context=requirements
        )
        
        self.mock_llm_manager.generate_response_with_context.return_value = """
        For a photography social media app, you'll want a modern web application architecture.
        This typically involves a frontend (what users see) built with React, a backend API
        (server logic) built with Node.js, and a database like MongoDB to store photos and user data.
        """
        
        explanation = self.explanation_system.explain(
            "web application architecture",
            "How should I structure my photography app?",
            context
        )
        
        # Verify complete workflow
        assert requirements is not None
        assert requirements.project_type == ProjectType.WEB_APP
        assert len(questions) > 0
        assert explanation is not None
        assert "photography" in explanation.content.lower() or "photo" in explanation.content.lower()
