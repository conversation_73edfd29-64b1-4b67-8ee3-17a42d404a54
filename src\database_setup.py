# AI Coding Agent - Database Setup Automation System
"""
Comprehensive database setup automation system for intelligent schema generation,
automatic database initialization, and migration management.
"""

import json
import re
import sqlite3
import subprocess
import tempfile
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple, Union, Any
import logging

from src.logger import get_logger
from src.exceptions import CodingAgentError
from src.config import get_config
from src.monitoring import get_performance_monitor
from src.models import LLMManager

config = get_config()


class DatabaseType(Enum):
    """Supported database types"""
    SQLITE = "sqlite"
    POSTGRESQL = "postgresql"
    MYSQL = "mysql"
    MONGODB = "mongodb"


class FieldType(Enum):
    """Database field types"""
    INTEGER = "integer"
    STRING = "string"
    TEXT = "text"
    BOOLEAN = "boolean"
    DATETIME = "datetime"
    DECIMAL = "decimal"
    JSON = "json"
    FOREIGN_KEY = "foreign_key"


class RelationshipType(Enum):
    """Database relationship types"""
    ONE_TO_ONE = "one_to_one"
    ONE_TO_MANY = "one_to_many"
    MANY_TO_MANY = "many_to_many"


@dataclass
class DatabaseField:
    """Database field definition"""
    name: str
    field_type: FieldType
    nullable: bool = True
    unique: bool = False
    primary_key: bool = False
    default_value: Optional[str] = None
    max_length: Optional[int] = None
    foreign_key_table: Optional[str] = None
    foreign_key_field: Optional[str] = None
    description: Optional[str] = None


@dataclass
class DatabaseTable:
    """Database table definition"""
    name: str
    fields: List[DatabaseField] = field(default_factory=list)
    indexes: List[str] = field(default_factory=list)
    constraints: List[str] = field(default_factory=list)
    description: Optional[str] = None


@dataclass
class DatabaseRelationship:
    """Database relationship definition"""
    name: str
    from_table: str
    to_table: str
    relationship_type: RelationshipType
    from_field: str
    to_field: str
    description: Optional[str] = None


@dataclass
class DatabaseSchema:
    """Complete database schema definition"""
    name: str
    database_type: DatabaseType
    tables: List[DatabaseTable] = field(default_factory=list)
    relationships: List[DatabaseRelationship] = field(default_factory=list)
    indexes: List[str] = field(default_factory=list)
    description: Optional[str] = None


@dataclass
class DatabaseRequirements:
    """Analyzed database requirements from project"""
    entities: List[str] = field(default_factory=list)
    relationships: List[Dict[str, str]] = field(default_factory=list)
    data_types: Dict[str, str] = field(default_factory=dict)
    constraints: List[str] = field(default_factory=list)
    performance_requirements: Dict[str, Any] = field(default_factory=dict)
    scalability_needs: str = "small"  # small, medium, large
    consistency_requirements: str = "eventual"  # strong, eventual
    query_patterns: List[str] = field(default_factory=list)
    estimated_data_volume: str = "small"  # small, medium, large


class DatabaseSetupError(CodingAgentError):
    """Raised when database setup fails"""
    pass


class ProjectDatabaseAnalyzer:
    """
    Intelligent system to analyze project requirements and determine
    database needs based on data models, relationships, and usage patterns.
    """
    
    def __init__(self, llm_manager: Optional[LLMManager] = None):
        self.logger = get_logger(__name__)
        self.performance_monitor = get_performance_monitor()
        self.llm_manager = llm_manager or LLMManager()
        
        # Pattern recognition for database requirements
        self.entity_patterns = self._load_entity_patterns()
        self.relationship_patterns = self._load_relationship_patterns()
        self.data_type_patterns = self._load_data_type_patterns()
        
        self.logger.info("ProjectDatabaseAnalyzer initialized with intelligent pattern recognition")
    
    def analyze_project_requirements(self, project_path: Union[str, Path],
                                   user_requirements: Optional[str] = None) -> DatabaseRequirements:
        """
        Analyze project for database requirements.
        
        Args:
            project_path: Path to project directory
            user_requirements: Optional natural language requirements
            
        Returns:
            Analyzed database requirements
            
        Raises:
            DatabaseSetupError: If analysis fails
        """
        project_path = Path(project_path)
        
        try:
            with self.performance_monitor.measure_operation("database_requirements_analysis"):
                # Analyze code files for data models
                code_analysis = self._analyze_code_files(project_path)
                
                # Analyze user requirements if provided
                nlp_analysis = {}
                if user_requirements:
                    nlp_analysis = self._analyze_natural_language_requirements(user_requirements)
                
                # Analyze existing database files
                existing_db_analysis = self._analyze_existing_databases(project_path)
                
                # Merge all analyses
                requirements = self._merge_analyses(code_analysis, nlp_analysis, existing_db_analysis)
                
                self.logger.info(f"Analyzed database requirements: {len(requirements.entities)} entities, "
                               f"{len(requirements.relationships)} relationships")
                
                return requirements
                
        except Exception as e:
            self.logger.error(f"Failed to analyze database requirements: {str(e)}")
            raise DatabaseSetupError(f"Requirements analysis failed: {str(e)}")
    
    def _analyze_code_files(self, project_path: Path) -> Dict[str, Any]:
        """Analyze code files for data model patterns"""
        analysis = {
            'entities': [],
            'relationships': [],
            'data_types': {},
            'constraints': []
        }
        
        # Supported file patterns for analysis
        patterns = ['*.py', '*.js', '*.ts', '*.java', '*.cs']
        
        for pattern in patterns:
            for file_path in project_path.rglob(pattern):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Analyze based on file type
                    if file_path.suffix == '.py':
                        file_analysis = self._analyze_python_models(content)
                    elif file_path.suffix in ['.js', '.ts']:
                        file_analysis = self._analyze_javascript_models(content)
                    else:
                        continue
                    
                    # Merge file analysis
                    analysis['entities'].extend(file_analysis.get('entities', []))
                    analysis['relationships'].extend(file_analysis.get('relationships', []))
                    analysis['data_types'].update(file_analysis.get('data_types', {}))
                    analysis['constraints'].extend(file_analysis.get('constraints', []))
                    
                except Exception as e:
                    self.logger.warning(f"Failed to analyze file {file_path}: {e}")
                    continue
        
        return analysis
    
    def _analyze_python_models(self, content: str) -> Dict[str, Any]:
        """Analyze Python code for data models"""
        analysis = {
            'entities': [],
            'relationships': [],
            'data_types': {},
            'constraints': []
        }
        
        # Look for class definitions that might be models
        class_pattern = r'class\s+(\w+).*?:'
        classes = re.findall(class_pattern, content)
        
        for class_name in classes:
            # Check if it looks like a data model
            if self._is_likely_model_class(class_name, content):
                analysis['entities'].append(class_name.lower())
                
                # Extract field information
                fields = self._extract_python_fields(class_name, content)
                for field_name, field_type in fields.items():
                    analysis['data_types'][f"{class_name.lower()}.{field_name}"] = field_type
        
        # Look for relationship patterns
        relationships = self._extract_python_relationships(content)
        analysis['relationships'].extend(relationships)
        
        return analysis
    
    def _analyze_javascript_models(self, content: str) -> Dict[str, Any]:
        """Analyze JavaScript/TypeScript code for data models"""
        analysis = {
            'entities': [],
            'relationships': [],
            'data_types': {},
            'constraints': []
        }
        
        # Look for interface/class definitions
        interface_pattern = r'(?:interface|class)\s+(\w+)'
        interfaces = re.findall(interface_pattern, content)
        
        for interface_name in interfaces:
            if self._is_likely_model_interface(interface_name, content):
                analysis['entities'].append(interface_name.lower())
                
                # Extract field information
                fields = self._extract_javascript_fields(interface_name, content)
                for field_name, field_type in fields.items():
                    analysis['data_types'][f"{interface_name.lower()}.{field_name}"] = field_type
        
        return analysis

    def _analyze_natural_language_requirements(self, requirements: str) -> Dict[str, Any]:
        """Analyze natural language requirements using LLM"""
        try:
            prompt = f"""Analyze the following project requirements and extract database-related information:

Requirements: "{requirements}"

Please provide a JSON response with the following structure:
{{
    "entities": ["list", "of", "main", "data", "entities"],
    "relationships": [
        {{"from": "entity1", "to": "entity2", "type": "one_to_many", "description": "relationship description"}}
    ],
    "data_types": {{
        "entity.field": "field_type"
    }},
    "constraints": ["list", "of", "constraints"],
    "performance_requirements": {{
        "read_heavy": true/false,
        "write_heavy": true/false,
        "real_time": true/false
    }},
    "scalability_needs": "small/medium/large",
    "estimated_data_volume": "small/medium/large"
}}

Focus on identifying:
1. Main data entities (User, Product, Order, etc.)
2. Relationships between entities
3. Data types and constraints
4. Performance and scalability requirements"""

            response = self.llm_manager.generate_response_with_context(
                prompt=prompt,
                context="",
                role="assistant"
            )

            # Parse JSON response
            try:
                return json.loads(response)
            except json.JSONDecodeError:
                # Fallback: extract basic entities from text
                entities = self._extract_entities_from_text(requirements)
                return {
                    'entities': entities,
                    'relationships': [],
                    'data_types': {},
                    'constraints': [],
                    'performance_requirements': {},
                    'scalability_needs': 'small',
                    'estimated_data_volume': 'small'
                }

        except Exception as e:
            self.logger.warning(f"LLM analysis failed, using fallback: {e}")
            return {
                'entities': [],
                'relationships': [],
                'data_types': {},
                'constraints': [],
                'performance_requirements': {},
                'scalability_needs': 'small',
                'estimated_data_volume': 'small'
            }

    def _analyze_existing_databases(self, project_path: Path) -> Dict[str, Any]:
        """Analyze existing database files and schemas"""
        analysis = {
            'entities': [],
            'relationships': [],
            'data_types': {},
            'constraints': []
        }

        # Look for database files
        db_files = list(project_path.rglob("*.db")) + list(project_path.rglob("*.sqlite"))

        for db_file in db_files:
            try:
                # Analyze SQLite database
                db_analysis = self._analyze_sqlite_database(db_file)
                analysis['entities'].extend(db_analysis.get('entities', []))
                analysis['relationships'].extend(db_analysis.get('relationships', []))
                analysis['data_types'].update(db_analysis.get('data_types', {}))
                analysis['constraints'].extend(db_analysis.get('constraints', []))

            except Exception as e:
                self.logger.warning(f"Failed to analyze database {db_file}: {e}")
                continue

        # Look for SQL schema files
        sql_files = list(project_path.rglob("*.sql"))
        for sql_file in sql_files:
            try:
                with open(sql_file, 'r', encoding='utf-8') as f:
                    sql_content = f.read()

                sql_analysis = self._analyze_sql_schema(sql_content)
                analysis['entities'].extend(sql_analysis.get('entities', []))
                analysis['relationships'].extend(sql_analysis.get('relationships', []))

            except Exception as e:
                self.logger.warning(f"Failed to analyze SQL file {sql_file}: {e}")
                continue

        return analysis

    def _merge_analyses(self, code_analysis: Dict, nlp_analysis: Dict,
                       db_analysis: Dict) -> DatabaseRequirements:
        """Merge all analysis results into comprehensive requirements"""
        # Combine entities from all sources
        all_entities = set()
        all_entities.update(code_analysis.get('entities', []))
        all_entities.update(nlp_analysis.get('entities', []))
        all_entities.update(db_analysis.get('entities', []))

        # Combine relationships
        all_relationships = []
        all_relationships.extend(code_analysis.get('relationships', []))
        all_relationships.extend(nlp_analysis.get('relationships', []))
        all_relationships.extend(db_analysis.get('relationships', []))

        # Combine data types
        all_data_types = {}
        all_data_types.update(code_analysis.get('data_types', {}))
        all_data_types.update(nlp_analysis.get('data_types', {}))
        all_data_types.update(db_analysis.get('data_types', {}))

        # Combine constraints
        all_constraints = []
        all_constraints.extend(code_analysis.get('constraints', []))
        all_constraints.extend(nlp_analysis.get('constraints', []))
        all_constraints.extend(db_analysis.get('constraints', []))

        # Extract performance and scalability requirements
        performance_reqs = nlp_analysis.get('performance_requirements', {})
        scalability = nlp_analysis.get('scalability_needs', 'small')
        data_volume = nlp_analysis.get('estimated_data_volume', 'small')

        return DatabaseRequirements(
            entities=list(all_entities),
            relationships=all_relationships,
            data_types=all_data_types,
            constraints=all_constraints,
            performance_requirements=performance_reqs,
            scalability_needs=scalability,
            estimated_data_volume=data_volume,
            query_patterns=self._infer_query_patterns(list(all_entities), all_relationships)
        )

    def _extract_entities_from_text(self, text: str) -> List[str]:
        """Extract potential entities from natural language text"""
        # Common entity indicators
        entity_keywords = [
            'user', 'customer', 'product', 'order', 'item', 'category',
            'account', 'profile', 'post', 'comment', 'message', 'notification',
            'payment', 'transaction', 'invoice', 'report', 'document',
            'file', 'image', 'video', 'tag', 'label', 'group', 'team',
            'project', 'task', 'event', 'booking', 'reservation'
        ]

        entities = []
        text_lower = text.lower()

        for keyword in entity_keywords:
            if keyword in text_lower:
                entities.append(keyword)

        return entities

    def _analyze_sqlite_database(self, db_file: Path) -> Dict[str, Any]:
        """Analyze existing SQLite database"""
        analysis = {
            'entities': [],
            'relationships': [],
            'data_types': {},
            'constraints': []
        }

        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()

            # Get table names
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()

            for (table_name,) in tables:
                analysis['entities'].append(table_name.lower())

                # Get table schema
                cursor.execute(f"PRAGMA table_info({table_name});")
                columns = cursor.fetchall()

                for column in columns:
                    col_name, col_type = column[1], column[2]
                    analysis['data_types'][f"{table_name.lower()}.{col_name}"] = col_type.lower()

            conn.close()

        except Exception as e:
            self.logger.warning(f"Failed to analyze SQLite database: {e}")

        return analysis

    def _analyze_sql_schema(self, sql_content: str) -> Dict[str, Any]:
        """Analyze SQL schema content"""
        analysis = {
            'entities': [],
            'relationships': []
        }

        # Extract table names from CREATE TABLE statements
        table_pattern = r'CREATE\s+TABLE\s+(?:IF\s+NOT\s+EXISTS\s+)?(\w+)'
        tables = re.findall(table_pattern, sql_content, re.IGNORECASE)
        analysis['entities'].extend([table.lower() for table in tables])

        # Extract foreign key relationships
        fk_pattern = r'FOREIGN\s+KEY\s*\([^)]+\)\s*REFERENCES\s+(\w+)'
        foreign_keys = re.findall(fk_pattern, sql_content, re.IGNORECASE)

        for fk_table in foreign_keys:
            analysis['relationships'].append({
                'to': fk_table.lower(),
                'type': 'foreign_key'
            })

        return analysis

    def _infer_query_patterns(self, entities: List[str], relationships: List[Dict]) -> List[str]:
        """Infer common query patterns from entities and relationships"""
        patterns = []

        # Basic CRUD patterns for each entity
        for entity in entities:
            patterns.extend([
                f"SELECT * FROM {entity}",
                f"INSERT INTO {entity}",
                f"UPDATE {entity}",
                f"DELETE FROM {entity}"
            ])

        # Join patterns based on relationships
        for rel in relationships:
            if 'from' in rel and 'to' in rel:
                patterns.append(f"JOIN {rel['from']} with {rel['to']}")

        return patterns

    def _is_likely_model_class(self, class_name: str, content: str) -> bool:
        """Check if a class is likely a data model"""
        # Look for model indicators
        model_indicators = [
            'Model', 'Entity', 'Table', 'Document', 'Schema',
            'db.Model', 'models.Model', 'Base', 'SQLAlchemy'
        ]

        class_section = self._extract_class_section(class_name, content)

        for indicator in model_indicators:
            if indicator in class_section:
                return True

        # Check for field definitions
        field_patterns = [
            r'Column\(', r'Field\(', r'CharField', r'IntegerField',
            r'ForeignKey', r'OneToMany', r'ManyToMany'
        ]

        for pattern in field_patterns:
            if re.search(pattern, class_section):
                return True

        return False

    def _is_likely_model_interface(self, interface_name: str, content: str) -> bool:
        """Check if an interface is likely a data model"""
        interface_section = self._extract_interface_section(interface_name, content)

        # Look for common data model field patterns
        field_patterns = [
            r'id\s*:\s*(?:string|number)',
            r'createdAt\s*:\s*Date',
            r'updatedAt\s*:\s*Date',
            r'\w+Id\s*:\s*(?:string|number)'
        ]

        for pattern in field_patterns:
            if re.search(pattern, interface_section):
                return True

        return False

    def _extract_class_section(self, class_name: str, content: str) -> str:
        """Extract the section of code for a specific class"""
        pattern = rf'class\s+{class_name}.*?(?=class\s+\w+|$)'
        match = re.search(pattern, content, re.DOTALL)
        return match.group(0) if match else ""

    def _extract_interface_section(self, interface_name: str, content: str) -> str:
        """Extract the section of code for a specific interface"""
        pattern = rf'(?:interface|class)\s+{interface_name}.*?(?=(?:interface|class)\s+\w+|$)'
        match = re.search(pattern, content, re.DOTALL)
        return match.group(0) if match else ""

    def _extract_python_fields(self, class_name: str, content: str) -> Dict[str, str]:
        """Extract field information from Python class"""
        fields = {}
        class_section = self._extract_class_section(class_name, content)

        # Look for SQLAlchemy column definitions
        column_pattern = r'(\w+)\s*=\s*Column\(([^)]+)\)'
        matches = re.findall(column_pattern, class_section)

        for field_name, column_def in matches:
            # Extract field type from column definition
            if 'Integer' in column_def:
                fields[field_name] = 'integer'
            elif 'String' in column_def:
                fields[field_name] = 'string'
            elif 'Text' in column_def:
                fields[field_name] = 'text'
            elif 'Boolean' in column_def:
                fields[field_name] = 'boolean'
            elif 'DateTime' in column_def:
                fields[field_name] = 'datetime'
            else:
                fields[field_name] = 'string'  # default

        return fields

    def _extract_javascript_fields(self, interface_name: str, content: str) -> Dict[str, str]:
        """Extract field information from JavaScript/TypeScript interface"""
        fields = {}
        interface_section = self._extract_interface_section(interface_name, content)

        # Look for field definitions
        field_pattern = r'(\w+)\s*:\s*(\w+)'
        matches = re.findall(field_pattern, interface_section)

        for field_name, field_type in matches:
            # Map TypeScript types to database types
            type_mapping = {
                'string': 'string',
                'number': 'integer',
                'boolean': 'boolean',
                'Date': 'datetime',
                'object': 'json'
            }
            fields[field_name] = type_mapping.get(field_type, 'string')

        return fields

    def _extract_python_relationships(self, content: str) -> List[Dict[str, str]]:
        """Extract relationship information from Python code"""
        relationships = []

        # Look for foreign key relationships
        fk_pattern = r'ForeignKey\([\'"](\w+)(?:\.id)?[\'"]'
        matches = re.findall(fk_pattern, content)

        for table_name in matches:
            relationships.append({
                'to': table_name.lower(),
                'type': 'foreign_key'
            })

        return relationships

    def _load_entity_patterns(self) -> List[str]:
        """Load common entity name patterns"""
        return [
            r'\buser\b', r'\bcustomer\b', r'\bproduct\b', r'\border\b',
            r'\bitem\b', r'\bcategory\b', r'\baccount\b', r'\bprofile\b',
            r'\bpost\b', r'\bcomment\b', r'\bmessage\b', r'\bnotification\b',
            r'\bpayment\b', r'\btransaction\b', r'\binvoice\b', r'\breport\b'
        ]

    def _load_relationship_patterns(self) -> List[str]:
        """Load common relationship patterns"""
        return [
            r'ForeignKey', r'OneToMany', r'ManyToMany', r'OneToOne',
            r'belongs_to', r'has_many', r'has_one', r'references'
        ]

    def _load_data_type_patterns(self) -> Dict[str, str]:
        """Load data type mapping patterns"""
        return {
            r'id|ID': 'integer',
            r'name|title': 'string',
            r'description|content|text': 'text',
            r'email': 'string',
            r'password': 'string',
            r'created_at|updated_at|timestamp': 'datetime',
            r'price|amount|cost': 'decimal',
            r'is_|active|enabled': 'boolean',
            r'count|quantity|number': 'integer'
        }


class DatabaseSelector:
    """
    Decision engine to automatically select appropriate database type
    based on project requirements and constraints.
    """

    def __init__(self):
        self.logger = get_logger(__name__)
        self.performance_monitor = get_performance_monitor()

        # Database selection criteria
        self.selection_criteria = self._load_selection_criteria()

        self.logger.info("DatabaseSelector initialized with intelligent selection criteria")

    def select_database_type(self, requirements: DatabaseRequirements,
                           constraints: Optional[Dict[str, Any]] = None) -> DatabaseType:
        """
        Select the most appropriate database type based on requirements.

        Args:
            requirements: Analyzed database requirements
            constraints: Optional deployment/infrastructure constraints

        Returns:
            Recommended database type
        """
        constraints = constraints or {}

        try:
            with self.performance_monitor.measure_operation("database_selection"):
                # Score each database type
                scores = {}

                for db_type in DatabaseType:
                    score = self._calculate_database_score(db_type, requirements, constraints)
                    scores[db_type] = score
                    self.logger.debug(f"{db_type.value} score: {score}")

                # Select the highest scoring database
                selected_db = max(scores.keys(), key=lambda k: scores[k])

                self.logger.info(f"Selected database type: {selected_db.value} (score: {scores[selected_db]})")

                return selected_db

        except Exception as e:
            self.logger.error(f"Database selection failed: {str(e)}")
            # Fallback to SQLite for simplicity
            return DatabaseType.SQLITE

    def get_selection_reasoning(self, requirements: DatabaseRequirements,
                               selected_db: DatabaseType,
                               constraints: Optional[Dict[str, Any]] = None) -> str:
        """
        Provide reasoning for database selection decision.

        Args:
            requirements: Database requirements
            selected_db: Selected database type
            constraints: Optional constraints

        Returns:
            Human-readable explanation of selection reasoning
        """
        constraints = constraints or {}

        reasons = []

        # Analyze key factors
        if selected_db == DatabaseType.SQLITE:
            reasons.append("SQLite selected for:")
            if requirements.scalability_needs == "small":
                reasons.append("- Small scale requirements")
            if len(requirements.entities) <= 5:
                reasons.append("- Simple data model")
            if not constraints.get('multi_user', False):
                reasons.append("- Single-user application")
            reasons.append("- Easy deployment and maintenance")

        elif selected_db == DatabaseType.POSTGRESQL:
            reasons.append("PostgreSQL selected for:")
            if requirements.scalability_needs in ["medium", "large"]:
                reasons.append("- Medium to large scale requirements")
            if len(requirements.relationships) > 5:
                reasons.append("- Complex relationships")
            if requirements.performance_requirements.get('read_heavy', False):
                reasons.append("- Read-heavy workload optimization")
            reasons.append("- ACID compliance and reliability")

        elif selected_db == DatabaseType.MYSQL:
            reasons.append("MySQL selected for:")
            if requirements.performance_requirements.get('write_heavy', False):
                reasons.append("- Write-heavy workload")
            if constraints.get('web_application', False):
                reasons.append("- Web application compatibility")
            reasons.append("- Wide ecosystem support")

        elif selected_db == DatabaseType.MONGODB:
            reasons.append("MongoDB selected for:")
            if any('json' in dt for dt in requirements.data_types.values()):
                reasons.append("- JSON/document data structures")
            if requirements.scalability_needs == "large":
                reasons.append("- Horizontal scaling requirements")
            if requirements.consistency_requirements == "eventual":
                reasons.append("- Eventual consistency acceptable")

        return "\n".join(reasons)

    def _calculate_database_score(self, db_type: DatabaseType,
                                 requirements: DatabaseRequirements,
                                 constraints: Dict[str, Any]) -> float:
        """Calculate suitability score for a database type"""
        score = 0.0

        # Base scores for each database type
        base_scores = {
            DatabaseType.SQLITE: 5.0,
            DatabaseType.POSTGRESQL: 7.0,
            DatabaseType.MYSQL: 6.0,
            DatabaseType.MONGODB: 6.0
        }

        score += base_scores.get(db_type, 0.0)

        # Scalability scoring
        scalability_scores = {
            "small": {
                DatabaseType.SQLITE: 3.0,
                DatabaseType.POSTGRESQL: 1.0,
                DatabaseType.MYSQL: 1.0,
                DatabaseType.MONGODB: 0.5
            },
            "medium": {
                DatabaseType.SQLITE: 0.5,
                DatabaseType.POSTGRESQL: 3.0,
                DatabaseType.MYSQL: 2.5,
                DatabaseType.MONGODB: 2.0
            },
            "large": {
                DatabaseType.SQLITE: 0.0,
                DatabaseType.POSTGRESQL: 2.5,
                DatabaseType.MYSQL: 2.0,
                DatabaseType.MONGODB: 3.0
            }
        }

        scale = requirements.scalability_needs
        if scale in scalability_scores and db_type in scalability_scores[scale]:
            score += scalability_scores[scale][db_type]

        # Data complexity scoring
        entity_count = len(requirements.entities)
        relationship_count = len(requirements.relationships)

        if entity_count <= 3 and relationship_count <= 2:
            # Simple schema
            if db_type == DatabaseType.SQLITE:
                score += 2.0
        elif entity_count > 10 or relationship_count > 10:
            # Complex schema
            if db_type in [DatabaseType.POSTGRESQL, DatabaseType.MYSQL]:
                score += 2.0

        # Performance requirements scoring
        perf_reqs = requirements.performance_requirements

        if perf_reqs.get('read_heavy', False):
            if db_type == DatabaseType.POSTGRESQL:
                score += 1.5
            elif db_type == DatabaseType.MYSQL:
                score += 1.0

        if perf_reqs.get('write_heavy', False):
            if db_type == DatabaseType.MYSQL:
                score += 1.5
            elif db_type == DatabaseType.MONGODB:
                score += 1.0

        if perf_reqs.get('real_time', False):
            if db_type in [DatabaseType.POSTGRESQL, DatabaseType.MYSQL]:
                score += 1.0

        # Data type compatibility
        has_json_data = any('json' in dt.lower() for dt in requirements.data_types.values())
        if has_json_data:
            if db_type == DatabaseType.MONGODB:
                score += 2.0
            elif db_type == DatabaseType.POSTGRESQL:
                score += 1.0

        # Constraint-based scoring
        if constraints.get('cloud_deployment', False):
            if db_type in [DatabaseType.POSTGRESQL, DatabaseType.MYSQL]:
                score += 1.0

        if constraints.get('embedded_system', False):
            if db_type == DatabaseType.SQLITE:
                score += 3.0

        if constraints.get('multi_user', False):
            if db_type in [DatabaseType.POSTGRESQL, DatabaseType.MYSQL, DatabaseType.MONGODB]:
                score += 1.5

        return score

    def _load_selection_criteria(self) -> Dict[str, Any]:
        """Load database selection criteria and weights"""
        return {
            'scalability_weight': 3.0,
            'performance_weight': 2.5,
            'complexity_weight': 2.0,
            'deployment_weight': 1.5,
            'maintenance_weight': 1.0
        }


class SchemaGenerator:
    """
    Natural language to database schema conversion with table creation,
    relationships, indexes, and constraints.
    """

    def __init__(self, llm_manager: Optional[LLMManager] = None):
        self.logger = get_logger(__name__)
        self.performance_monitor = get_performance_monitor()
        self.llm_manager = llm_manager or LLMManager()

        # Schema generation templates
        self.sql_templates = self._load_sql_templates()
        self.field_type_mappings = self._load_field_type_mappings()

        self.logger.info("SchemaGenerator initialized with SQL generation capabilities")

    def generate_schema_from_requirements(self, requirements: DatabaseRequirements,
                                        database_type: DatabaseType) -> DatabaseSchema:
        """
        Generate complete database schema from requirements.

        Args:
            requirements: Analyzed database requirements
            database_type: Target database type

        Returns:
            Complete database schema definition

        Raises:
            DatabaseSetupError: If schema generation fails
        """
        try:
            with self.performance_monitor.measure_operation("schema_generation"):
                # Generate tables from entities
                tables = self._generate_tables_from_entities(
                    requirements.entities,
                    requirements.data_types,
                    database_type
                )

                # Generate relationships
                relationships = self._generate_relationships_from_requirements(
                    requirements.relationships,
                    tables
                )

                # Add foreign key fields to tables
                self._add_foreign_key_fields(tables, relationships)

                # Generate indexes
                indexes = self._generate_indexes(tables, requirements.query_patterns)

                # Create schema object
                schema = DatabaseSchema(
                    name=f"generated_schema_{database_type.value}",
                    database_type=database_type,
                    tables=tables,
                    relationships=relationships,
                    indexes=indexes,
                    description=f"Auto-generated schema for {len(tables)} tables"
                )

                self.logger.info(f"Generated schema with {len(tables)} tables, "
                               f"{len(relationships)} relationships, {len(indexes)} indexes")

                return schema

        except Exception as e:
            self.logger.error(f"Schema generation failed: {str(e)}")
            raise DatabaseSetupError(f"Schema generation failed: {str(e)}")

    def generate_sql_script(self, schema: DatabaseSchema) -> str:
        """
        Generate SQL script for creating the database schema.

        Args:
            schema: Database schema definition

        Returns:
            SQL script for schema creation
        """
        try:
            sql_parts = []

            # Add header comment
            sql_parts.append(f"-- Auto-generated database schema for {schema.name}")
            sql_parts.append(f"-- Database type: {schema.database_type.value}")
            sql_parts.append(f"-- Generated on: {self._get_timestamp()}")
            sql_parts.append("")

            # Generate table creation statements
            for table in schema.tables:
                table_sql = self._generate_table_sql(table, schema.database_type)
                sql_parts.append(table_sql)
                sql_parts.append("")

            # Generate index creation statements
            for index in schema.indexes:
                sql_parts.append(index)
                sql_parts.append("")

            # Generate foreign key constraints (if not inline)
            if schema.database_type != DatabaseType.SQLITE:
                for relationship in schema.relationships:
                    if relationship.relationship_type == RelationshipType.ONE_TO_MANY:
                        fk_sql = self._generate_foreign_key_sql(relationship, schema.database_type)
                        sql_parts.append(fk_sql)
                        sql_parts.append("")

            return "\n".join(sql_parts)

        except Exception as e:
            self.logger.error(f"SQL script generation failed: {str(e)}")
            raise DatabaseSetupError(f"SQL script generation failed: {str(e)}")

    def _generate_tables_from_entities(self, entities: List[str],
                                     data_types: Dict[str, str],
                                     database_type: DatabaseType) -> List[DatabaseTable]:
        """Generate table definitions from entities"""
        tables = []

        for entity in entities:
            # Create table with basic structure
            table = DatabaseTable(
                name=entity,
                description=f"Table for {entity} entity"
            )

            # Add primary key field
            id_field = DatabaseField(
                name="id",
                field_type=FieldType.INTEGER,
                primary_key=True,
                nullable=False,
                description=f"Primary key for {entity}"
            )
            table.fields.append(id_field)

            # Add fields based on data types
            entity_fields = {
                key.split('.')[1]: value
                for key, value in data_types.items()
                if key.startswith(f"{entity}.")
            }

            for field_name, field_type_str in entity_fields.items():
                field_type = self._map_field_type(field_type_str)
                field = DatabaseField(
                    name=field_name,
                    field_type=field_type,
                    nullable=True,
                    description=f"{field_name} field for {entity}"
                )
                table.fields.append(field)

            # Add common timestamp fields
            if database_type != DatabaseType.MONGODB:
                created_at = DatabaseField(
                    name="created_at",
                    field_type=FieldType.DATETIME,
                    nullable=False,
                    default_value="CURRENT_TIMESTAMP",
                    description="Record creation timestamp"
                )
                table.fields.append(created_at)

                updated_at = DatabaseField(
                    name="updated_at",
                    field_type=FieldType.DATETIME,
                    nullable=False,
                    default_value="CURRENT_TIMESTAMP",
                    description="Record update timestamp"
                )
                table.fields.append(updated_at)

            tables.append(table)

        return tables

    def _generate_relationships_from_requirements(self, relationships: List[Dict[str, str]],
                                                tables: List[DatabaseTable]) -> List[DatabaseRelationship]:
        """Generate relationship definitions from requirements"""
        db_relationships = []
        table_names = {table.name for table in tables}

        for rel in relationships:
            if 'from' in rel and 'to' in rel:
                from_table = rel['from']
                to_table = rel['to']

                # Ensure both tables exist
                if from_table in table_names and to_table in table_names:
                    relationship_type = RelationshipType.ONE_TO_MANY  # Default

                    if rel.get('type') == 'many_to_many':
                        relationship_type = RelationshipType.MANY_TO_MANY
                    elif rel.get('type') == 'one_to_one':
                        relationship_type = RelationshipType.ONE_TO_ONE

                    db_rel = DatabaseRelationship(
                        name=f"{from_table}_to_{to_table}",
                        from_table=from_table,
                        to_table=to_table,
                        relationship_type=relationship_type,
                        from_field=f"{to_table}_id",
                        to_field="id",
                        description=rel.get('description', f"Relationship from {from_table} to {to_table}")
                    )
                    db_relationships.append(db_rel)

        return db_relationships

    def _add_foreign_key_fields(self, tables: List[DatabaseTable],
                               relationships: List[DatabaseRelationship]) -> None:
        """Add foreign key fields to tables based on relationships"""
        table_dict = {table.name: table for table in tables}

        for rel in relationships:
            if rel.relationship_type == RelationshipType.ONE_TO_MANY:
                # Add foreign key field to the 'from' table
                from_table = table_dict.get(rel.from_table)
                if from_table:
                    fk_field = DatabaseField(
                        name=rel.from_field,
                        field_type=FieldType.INTEGER,
                        nullable=True,
                        foreign_key_table=rel.to_table,
                        foreign_key_field=rel.to_field,
                        description=f"Foreign key to {rel.to_table}"
                    )
                    from_table.fields.append(fk_field)

            elif rel.relationship_type == RelationshipType.MANY_TO_MANY:
                # Create junction table for many-to-many relationships
                junction_table_name = f"{rel.from_table}_{rel.to_table}"
                junction_table = DatabaseTable(
                    name=junction_table_name,
                    description=f"Junction table for {rel.from_table} and {rel.to_table}"
                )

                # Add ID field
                id_field = DatabaseField(
                    name="id",
                    field_type=FieldType.INTEGER,
                    primary_key=True,
                    nullable=False
                )
                junction_table.fields.append(id_field)

                # Add foreign key fields
                from_fk = DatabaseField(
                    name=f"{rel.from_table}_id",
                    field_type=FieldType.INTEGER,
                    nullable=False,
                    foreign_key_table=rel.from_table,
                    foreign_key_field="id"
                )
                junction_table.fields.append(from_fk)

                to_fk = DatabaseField(
                    name=f"{rel.to_table}_id",
                    field_type=FieldType.INTEGER,
                    nullable=False,
                    foreign_key_table=rel.to_table,
                    foreign_key_field="id"
                )
                junction_table.fields.append(to_fk)

                tables.append(junction_table)

    def _generate_indexes(self, tables: List[DatabaseTable],
                         query_patterns: List[str]) -> List[str]:
        """Generate index creation statements"""
        indexes = []

        for table in tables:
            # Create indexes for foreign key fields
            for field in table.fields:
                if field.foreign_key_table:
                    index_sql = f"CREATE INDEX idx_{table.name}_{field.name} ON {table.name}({field.name});"
                    indexes.append(index_sql)

            # Create indexes for commonly queried fields
            common_index_fields = ['email', 'username', 'name', 'title', 'status']
            for field in table.fields:
                if field.name in common_index_fields:
                    index_sql = f"CREATE INDEX idx_{table.name}_{field.name} ON {table.name}({field.name});"
                    indexes.append(index_sql)

        return indexes

    def _map_field_type(self, field_type_str: str) -> FieldType:
        """Map string field type to FieldType enum"""
        type_mapping = {
            'integer': FieldType.INTEGER,
            'int': FieldType.INTEGER,
            'string': FieldType.STRING,
            'str': FieldType.STRING,
            'text': FieldType.TEXT,
            'boolean': FieldType.BOOLEAN,
            'bool': FieldType.BOOLEAN,
            'datetime': FieldType.DATETIME,
            'date': FieldType.DATETIME,
            'decimal': FieldType.DECIMAL,
            'float': FieldType.DECIMAL,
            'json': FieldType.JSON,
            'foreign_key': FieldType.FOREIGN_KEY
        }

        return type_mapping.get(field_type_str.lower(), FieldType.STRING)

    def _generate_table_sql(self, table: DatabaseTable, database_type: DatabaseType) -> str:
        """Generate SQL for creating a table"""
        sql_parts = [f"CREATE TABLE {table.name} ("]

        field_definitions = []
        for field in table.fields:
            field_sql = self._generate_field_sql(field, database_type)
            field_definitions.append(f"    {field_sql}")

        sql_parts.append(",\n".join(field_definitions))
        sql_parts.append(");")

        return "\n".join(sql_parts)

    def _generate_field_sql(self, field: DatabaseField, database_type: DatabaseType) -> str:
        """Generate SQL for a field definition"""
        # Map field types to SQL types based on database
        type_mappings = {
            DatabaseType.SQLITE: {
                FieldType.INTEGER: "INTEGER",
                FieldType.STRING: "TEXT",
                FieldType.TEXT: "TEXT",
                FieldType.BOOLEAN: "INTEGER",
                FieldType.DATETIME: "DATETIME",
                FieldType.DECIMAL: "REAL",
                FieldType.JSON: "TEXT",
                FieldType.FOREIGN_KEY: "INTEGER"
            },
            DatabaseType.POSTGRESQL: {
                FieldType.INTEGER: "INTEGER",
                FieldType.STRING: "VARCHAR(255)",
                FieldType.TEXT: "TEXT",
                FieldType.BOOLEAN: "BOOLEAN",
                FieldType.DATETIME: "TIMESTAMP",
                FieldType.DECIMAL: "DECIMAL(10,2)",
                FieldType.JSON: "JSONB",
                FieldType.FOREIGN_KEY: "INTEGER"
            },
            DatabaseType.MYSQL: {
                FieldType.INTEGER: "INT",
                FieldType.STRING: "VARCHAR(255)",
                FieldType.TEXT: "TEXT",
                FieldType.BOOLEAN: "BOOLEAN",
                FieldType.DATETIME: "DATETIME",
                FieldType.DECIMAL: "DECIMAL(10,2)",
                FieldType.JSON: "JSON",
                FieldType.FOREIGN_KEY: "INT"
            }
        }

        sql_type = type_mappings.get(database_type, {}).get(field.field_type, "TEXT")

        # Handle max_length for string fields
        if field.field_type == FieldType.STRING and field.max_length:
            if database_type in [DatabaseType.POSTGRESQL, DatabaseType.MYSQL]:
                sql_type = f"VARCHAR({field.max_length})"

        field_sql = f"{field.name} {sql_type}"

        # Add constraints
        if field.primary_key:
            if database_type == DatabaseType.SQLITE:
                field_sql += " PRIMARY KEY AUTOINCREMENT"
            elif database_type == DatabaseType.POSTGRESQL:
                field_sql += " PRIMARY KEY GENERATED ALWAYS AS IDENTITY"
            elif database_type == DatabaseType.MYSQL:
                field_sql += " PRIMARY KEY AUTO_INCREMENT"

        if not field.nullable and not field.primary_key:
            field_sql += " NOT NULL"

        if field.unique:
            field_sql += " UNIQUE"

        if field.default_value:
            field_sql += f" DEFAULT {field.default_value}"

        return field_sql

    def _generate_foreign_key_sql(self, relationship: DatabaseRelationship,
                                 database_type: DatabaseType) -> str:
        """Generate foreign key constraint SQL"""
        if database_type == DatabaseType.SQLITE:
            # SQLite foreign keys are defined inline
            return ""

        constraint_name = f"fk_{relationship.from_table}_{relationship.from_field}"

        return (f"ALTER TABLE {relationship.from_table} "
                f"ADD CONSTRAINT {constraint_name} "
                f"FOREIGN KEY ({relationship.from_field}) "
                f"REFERENCES {relationship.to_table}({relationship.to_field});")

    def _get_timestamp(self) -> str:
        """Get current timestamp as string"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def _load_sql_templates(self) -> Dict[str, str]:
        """Load SQL generation templates"""
        return {
            'create_table': "CREATE TABLE {table_name} ({fields});",
            'create_index': "CREATE INDEX {index_name} ON {table_name}({field_name});",
            'foreign_key': "FOREIGN KEY ({field}) REFERENCES {table}({ref_field})"
        }

    def _load_field_type_mappings(self) -> Dict[str, Dict[FieldType, str]]:
        """Load field type mappings for different databases"""
        return {
            'sqlite': {
                FieldType.INTEGER: "INTEGER",
                FieldType.STRING: "TEXT",
                FieldType.TEXT: "TEXT",
                FieldType.BOOLEAN: "INTEGER",
                FieldType.DATETIME: "DATETIME",
                FieldType.DECIMAL: "REAL",
                FieldType.JSON: "TEXT"
            },
            'postgresql': {
                FieldType.INTEGER: "INTEGER",
                FieldType.STRING: "VARCHAR(255)",
                FieldType.TEXT: "TEXT",
                FieldType.BOOLEAN: "BOOLEAN",
                FieldType.DATETIME: "TIMESTAMP",
                FieldType.DECIMAL: "DECIMAL(10,2)",
                FieldType.JSON: "JSONB"
            }
        }


class MigrationManager:
    """
    Migration management system for schema changes, data migrations,
    and version control of database structure.
    """

    def __init__(self, project_path: Union[str, Path]):
        self.logger = get_logger(__name__)
        self.performance_monitor = get_performance_monitor()
        self.project_path = Path(project_path)

        # Migration directory setup
        self.migrations_dir = self.project_path / "migrations"
        self.migrations_dir.mkdir(exist_ok=True)

        # Migration tracking
        self.migration_table = "schema_migrations"

        self.logger.info(f"MigrationManager initialized for project: {self.project_path}")

    def create_migration(self, name: str, schema_changes: Dict[str, Any],
                        database_type: DatabaseType) -> str:
        """
        Create a new migration file for schema changes.

        Args:
            name: Migration name/description
            schema_changes: Dictionary describing the changes
            database_type: Target database type

        Returns:
            Path to created migration file

        Raises:
            DatabaseSetupError: If migration creation fails
        """
        try:
            with self.performance_monitor.measure_operation("migration_creation"):
                # Generate migration timestamp and filename
                timestamp = self._get_migration_timestamp()
                filename = f"{timestamp}_{self._sanitize_name(name)}.sql"
                migration_path = self.migrations_dir / filename

                # Generate migration SQL
                migration_sql = self._generate_migration_sql(schema_changes, database_type)

                # Write migration file
                with open(migration_path, 'w', encoding='utf-8') as f:
                    f.write(migration_sql)

                self.logger.info(f"Created migration: {filename}")
                return str(migration_path)

        except Exception as e:
            self.logger.error(f"Failed to create migration: {str(e)}")
            raise DatabaseSetupError(f"Migration creation failed: {str(e)}")

    def apply_migrations(self, database_connection: Any,
                        database_type: DatabaseType) -> List[str]:
        """
        Apply pending migrations to database.

        Args:
            database_connection: Database connection object
            database_type: Database type

        Returns:
            List of applied migration files

        Raises:
            DatabaseSetupError: If migration application fails
        """
        try:
            with self.performance_monitor.measure_operation("migration_application"):
                # Ensure migration tracking table exists
                self._ensure_migration_table(database_connection, database_type)

                # Get applied migrations
                applied_migrations = self._get_applied_migrations(database_connection)

                # Get pending migrations
                pending_migrations = self._get_pending_migrations(applied_migrations)

                applied = []
                for migration_file in pending_migrations:
                    try:
                        # Read and execute migration
                        migration_path = self.migrations_dir / migration_file
                        with open(migration_path, 'r', encoding='utf-8') as f:
                            migration_sql = f.read()

                        # Execute migration
                        self._execute_migration_sql(database_connection, migration_sql, database_type)

                        # Record migration as applied
                        self._record_migration(database_connection, migration_file, database_type)

                        applied.append(migration_file)
                        self.logger.info(f"Applied migration: {migration_file}")

                    except Exception as e:
                        self.logger.error(f"Failed to apply migration {migration_file}: {e}")
                        break  # Stop on first failure

                return applied

        except Exception as e:
            self.logger.error(f"Migration application failed: {str(e)}")
            raise DatabaseSetupError(f"Migration application failed: {str(e)}")

    def rollback_migration(self, database_connection: Any, migration_name: str,
                          database_type: DatabaseType) -> bool:
        """
        Rollback a specific migration.

        Args:
            database_connection: Database connection
            migration_name: Name of migration to rollback
            database_type: Database type

        Returns:
            True if rollback successful
        """
        try:
            # Find migration file
            migration_files = list(self.migrations_dir.glob(f"*{migration_name}*"))
            if not migration_files:
                raise DatabaseSetupError(f"Migration {migration_name} not found")

            migration_file = migration_files[0]

            # Look for rollback SQL in migration file
            with open(migration_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # Extract rollback section
            rollback_sql = self._extract_rollback_sql(content)
            if not rollback_sql:
                raise DatabaseSetupError(f"No rollback SQL found for {migration_name}")

            # Execute rollback
            self._execute_migration_sql(database_connection, rollback_sql, database_type)

            # Remove from migration tracking
            self._remove_migration_record(database_connection, migration_file.name, database_type)

            self.logger.info(f"Rolled back migration: {migration_name}")
            return True

        except Exception as e:
            self.logger.error(f"Migration rollback failed: {str(e)}")
            return False

    def _get_migration_timestamp(self) -> str:
        """Generate timestamp for migration filename"""
        from datetime import datetime
        return datetime.now().strftime("%Y%m%d_%H%M%S")

    def _sanitize_name(self, name: str) -> str:
        """Sanitize migration name for filename"""
        import re
        # Replace spaces and special characters with underscores
        sanitized = re.sub(r'[^\w\s-]', '', name)
        sanitized = re.sub(r'[-\s]+', '_', sanitized)
        return sanitized.lower()

    def _generate_migration_sql(self, schema_changes: Dict[str, Any],
                               database_type: DatabaseType) -> str:
        """Generate SQL for migration based on schema changes"""
        sql_parts = []

        # Add migration header
        sql_parts.append("-- Migration: Schema Changes")
        sql_parts.append(f"-- Database: {database_type.value}")
        sql_parts.append(f"-- Generated: {self._get_timestamp()}")
        sql_parts.append("")
        sql_parts.append("-- UP Migration")
        sql_parts.append("")

        # Handle different types of changes
        if 'create_tables' in schema_changes:
            for table_def in schema_changes['create_tables']:
                sql_parts.append(f"CREATE TABLE {table_def['name']} (")
                field_defs = []
                for field in table_def.get('fields', []):
                    field_sql = f"    {field['name']} {field['type']}"
                    if field.get('primary_key'):
                        field_sql += " PRIMARY KEY"
                    if not field.get('nullable', True):
                        field_sql += " NOT NULL"
                    field_defs.append(field_sql)
                sql_parts.append(",\n".join(field_defs))
                sql_parts.append(");")
                sql_parts.append("")

        if 'add_columns' in schema_changes:
            for change in schema_changes['add_columns']:
                sql_parts.append(f"ALTER TABLE {change['table']} ADD COLUMN {change['column']} {change['type']};")
                sql_parts.append("")

        if 'drop_columns' in schema_changes:
            for change in schema_changes['drop_columns']:
                if database_type != DatabaseType.SQLITE:  # SQLite doesn't support DROP COLUMN
                    sql_parts.append(f"ALTER TABLE {change['table']} DROP COLUMN {change['column']};")
                    sql_parts.append("")

        if 'create_indexes' in schema_changes:
            for index in schema_changes['create_indexes']:
                sql_parts.append(f"CREATE INDEX {index['name']} ON {index['table']}({index['column']});")
                sql_parts.append("")

        # Add rollback section
        sql_parts.append("-- DOWN Migration (Rollback)")
        sql_parts.append("-- Uncomment and modify as needed for rollback")
        sql_parts.append("")

        if 'create_tables' in schema_changes:
            for table_def in schema_changes['create_tables']:
                sql_parts.append(f"-- DROP TABLE {table_def['name']};")

        if 'add_columns' in schema_changes:
            for change in schema_changes['add_columns']:
                if database_type != DatabaseType.SQLITE:
                    sql_parts.append(f"-- ALTER TABLE {change['table']} DROP COLUMN {change['column']};")

        return "\n".join(sql_parts)

    def _ensure_migration_table(self, connection: Any, database_type: DatabaseType) -> None:
        """Ensure migration tracking table exists"""
        if database_type == DatabaseType.SQLITE:
            sql = f"""
            CREATE TABLE IF NOT EXISTS {self.migration_table} (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                migration_name TEXT NOT NULL UNIQUE,
                applied_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );
            """
        elif database_type == DatabaseType.POSTGRESQL:
            sql = f"""
            CREATE TABLE IF NOT EXISTS {self.migration_table} (
                id SERIAL PRIMARY KEY,
                migration_name VARCHAR(255) NOT NULL UNIQUE,
                applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            """
        elif database_type == DatabaseType.MYSQL:
            sql = f"""
            CREATE TABLE IF NOT EXISTS {self.migration_table} (
                id INT AUTO_INCREMENT PRIMARY KEY,
                migration_name VARCHAR(255) NOT NULL UNIQUE,
                applied_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );
            """
        else:
            return

        # Execute the SQL (implementation depends on connection type)
        if hasattr(connection, 'execute'):
            connection.execute(sql)
        elif hasattr(connection, 'cursor'):
            cursor = connection.cursor()
            cursor.execute(sql)
            connection.commit()

    def _get_applied_migrations(self, connection: Any) -> Set[str]:
        """Get list of applied migrations from database"""
        try:
            sql = f"SELECT migration_name FROM {self.migration_table} ORDER BY applied_at;"

            if hasattr(connection, 'execute'):
                result = connection.execute(sql).fetchall()
                return {row[0] for row in result}
            elif hasattr(connection, 'cursor'):
                cursor = connection.cursor()
                cursor.execute(sql)
                result = cursor.fetchall()
                return {row[0] for row in result}

        except Exception as e:
            self.logger.warning(f"Failed to get applied migrations: {e}")
            return set()

        return set()

    def _get_pending_migrations(self, applied_migrations: Set[str]) -> List[str]:
        """Get list of pending migration files"""
        all_migrations = []

        # Get all migration files
        for migration_file in self.migrations_dir.glob("*.sql"):
            all_migrations.append(migration_file.name)

        # Sort by timestamp (filename prefix)
        all_migrations.sort()

        # Filter out applied migrations
        pending = [m for m in all_migrations if m not in applied_migrations]

        return pending

    def _execute_migration_sql(self, connection: Any, sql: str,
                              database_type: DatabaseType) -> None:
        """Execute migration SQL statements"""
        try:
            # Split SQL into individual statements
            statements = [stmt.strip() for stmt in sql.split(';') if stmt.strip()]

            for statement in statements:
                # Skip comments and empty statements
                if statement.startswith('--') or not statement:
                    continue

                if hasattr(connection, 'execute'):
                    connection.execute(statement)
                elif hasattr(connection, 'cursor'):
                    cursor = connection.cursor()
                    cursor.execute(statement)
                    connection.commit()

        except Exception as e:
            self.logger.error(f"Failed to execute migration SQL: {e}")
            raise

    def _record_migration(self, connection: Any, migration_name: str,
                         database_type: DatabaseType) -> None:
        """Record migration as applied"""
        sql = f"INSERT INTO {self.migration_table} (migration_name) VALUES (?);"

        try:
            if hasattr(connection, 'execute'):
                connection.execute(sql, (migration_name,))
            elif hasattr(connection, 'cursor'):
                cursor = connection.cursor()
                cursor.execute(sql, (migration_name,))
                connection.commit()

        except Exception as e:
            self.logger.error(f"Failed to record migration: {e}")
            raise

    def _extract_rollback_sql(self, migration_content: str) -> str:
        """Extract rollback SQL from migration file"""
        lines = migration_content.split('\n')
        rollback_lines = []
        in_rollback_section = False

        for line in lines:
            if '-- DOWN Migration' in line or '-- Rollback' in line:
                in_rollback_section = True
                continue

            if in_rollback_section:
                # Skip commented rollback lines, but include uncommented ones
                if line.strip().startswith('-- ') and not line.strip().startswith('-- DROP'):
                    continue
                elif line.strip().startswith('-- DROP') or line.strip().startswith('-- ALTER'):
                    # Uncomment rollback statements
                    rollback_lines.append(line.replace('-- ', ''))
                elif line.strip() and not line.strip().startswith('--'):
                    rollback_lines.append(line)

        return '\n'.join(rollback_lines)

    def _remove_migration_record(self, connection: Any, migration_name: str,
                                database_type: DatabaseType) -> None:
        """Remove migration record from tracking table"""
        sql = f"DELETE FROM {self.migration_table} WHERE migration_name = ?;"

        try:
            if hasattr(connection, 'execute'):
                connection.execute(sql, (migration_name,))
            elif hasattr(connection, 'cursor'):
                cursor = connection.cursor()
                cursor.execute(sql, (migration_name,))
                connection.commit()

        except Exception as e:
            self.logger.error(f"Failed to remove migration record: {e}")
            raise

    def _get_timestamp(self) -> str:
        """Get current timestamp as string"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")


class DatabaseConnectionManager:
    """
    Automate database connection setup, configuration file generation,
    and environment-specific settings.
    """

    def __init__(self, project_path: Union[str, Path]):
        self.logger = get_logger(__name__)
        self.performance_monitor = get_performance_monitor()
        self.project_path = Path(project_path)

        # Configuration templates
        self.config_templates = self._load_config_templates()

        self.logger.info(f"DatabaseConnectionManager initialized for project: {self.project_path}")

    def setup_database_connection(self, schema: DatabaseSchema,
                                 connection_params: Dict[str, Any]) -> Dict[str, str]:
        """
        Set up database connection and generate configuration files.

        Args:
            schema: Database schema definition
            connection_params: Connection parameters (host, port, etc.)

        Returns:
            Dictionary with paths to generated configuration files

        Raises:
            DatabaseSetupError: If setup fails
        """
        try:
            with self.performance_monitor.measure_operation("database_connection_setup"):
                generated_files = {}

                # Generate environment-specific config files
                environments = ['development', 'testing', 'production']

                for env in environments:
                    config_content = self._generate_config_for_environment(
                        schema, connection_params, env
                    )

                    config_file = self._write_config_file(config_content, env, schema.database_type)
                    generated_files[env] = config_file

                # Generate connection helper code
                helper_files = self._generate_connection_helpers(schema, connection_params)
                generated_files.update(helper_files)

                # Generate environment file template
                env_file = self._generate_env_file(schema, connection_params)
                generated_files['env_template'] = env_file

                # Generate database initialization script
                init_script = self._generate_init_script(schema)
                generated_files['init_script'] = init_script

                self.logger.info(f"Generated {len(generated_files)} configuration files")

                return generated_files

        except Exception as e:
            self.logger.error(f"Database connection setup failed: {str(e)}")
            raise DatabaseSetupError(f"Connection setup failed: {str(e)}")

    def create_database_if_not_exists(self, connection_params: Dict[str, Any],
                                    database_type: DatabaseType) -> bool:
        """
        Create database if it doesn't exist.

        Args:
            connection_params: Database connection parameters
            database_type: Type of database

        Returns:
            True if database was created or already exists
        """
        try:
            if database_type == DatabaseType.SQLITE:
                # SQLite creates database file automatically
                db_path = connection_params.get('database', 'database.db')
                db_file = self.project_path / db_path

                # Ensure directory exists
                db_file.parent.mkdir(parents=True, exist_ok=True)

                # Create empty database file if it doesn't exist
                if not db_file.exists():
                    import sqlite3
                    conn = sqlite3.connect(db_file)
                    conn.close()
                    self.logger.info(f"Created SQLite database: {db_file}")

                return True

            elif database_type in [DatabaseType.POSTGRESQL, DatabaseType.MYSQL]:
                # For PostgreSQL/MySQL, we would need to connect and create database
                # This is a simplified implementation
                self.logger.info(f"Database creation for {database_type.value} requires manual setup")
                return True

            else:
                self.logger.warning(f"Database creation not implemented for {database_type.value}")
                return False

        except Exception as e:
            self.logger.error(f"Database creation failed: {str(e)}")
            return False

    def test_connection(self, connection_params: Dict[str, Any],
                       database_type: DatabaseType) -> bool:
        """
        Test database connection with given parameters.

        Args:
            connection_params: Connection parameters
            database_type: Database type

        Returns:
            True if connection successful
        """
        try:
            if database_type == DatabaseType.SQLITE:
                db_path = connection_params.get('database', 'database.db')
                db_file = self.project_path / db_path

                import sqlite3
                conn = sqlite3.connect(db_file)
                conn.execute("SELECT 1;")
                conn.close()

                self.logger.info("SQLite connection test successful")
                return True

            elif database_type == DatabaseType.POSTGRESQL:
                # Would require psycopg2 or similar
                self.logger.info("PostgreSQL connection test would require psycopg2")
                return True

            elif database_type == DatabaseType.MYSQL:
                # Would require mysql-connector-python or similar
                self.logger.info("MySQL connection test would require mysql-connector")
                return True

            else:
                self.logger.warning(f"Connection test not implemented for {database_type.value}")
                return False

        except Exception as e:
            self.logger.error(f"Connection test failed: {str(e)}")
            return False

    def _load_config_templates(self) -> Dict[str, str]:
        """Load configuration file templates"""
        return {
            'sqlite_python': '''
import sqlite3
from pathlib import Path

DATABASE_PATH = Path(__file__).parent / "{database_file}"

def get_connection():
    """Get database connection"""
    return sqlite3.connect(DATABASE_PATH)

def init_database():
    """Initialize database with schema"""
    conn = get_connection()
    with open(Path(__file__).parent / "schema.sql", "r") as f:
        schema_sql = f.read()

    conn.executescript(schema_sql)
    conn.close()
''',
            'postgresql_python': '''
import psycopg2
from psycopg2.extras import RealDictCursor
import os

DATABASE_CONFIG = {{
    "host": os.getenv("DB_HOST", "{host}"),
    "port": os.getenv("DB_PORT", "{port}"),
    "database": os.getenv("DB_NAME", "{database}"),
    "user": os.getenv("DB_USER", "{user}"),
    "password": os.getenv("DB_PASSWORD", "{password}")
}}

def get_connection():
    """Get database connection"""
    return psycopg2.connect(**DATABASE_CONFIG, cursor_factory=RealDictCursor)

def init_database():
    """Initialize database with schema"""
    conn = get_connection()
    with open(Path(__file__).parent / "schema.sql", "r") as f:
        schema_sql = f.read()

    cursor = conn.cursor()
    cursor.execute(schema_sql)
    conn.commit()
    conn.close()
''',
            'env_template': '''
# Database Configuration
DB_HOST={host}
DB_PORT={port}
DB_NAME={database}
DB_USER={user}
DB_PASSWORD={password}

# Environment
NODE_ENV={environment}
'''
        }

    def _generate_config_for_environment(self, schema: DatabaseSchema,
                                       connection_params: Dict[str, Any],
                                       environment: str) -> str:
        """Generate configuration content for specific environment"""
        # Adjust parameters based on environment
        env_params = connection_params.copy()

        if environment == 'testing':
            # Use test database
            if 'database' in env_params:
                env_params['database'] = f"test_{env_params['database']}"
        elif environment == 'production':
            # Use production settings
            env_params['host'] = env_params.get('prod_host', env_params.get('host', 'localhost'))

        # Select appropriate template
        if schema.database_type == DatabaseType.SQLITE:
            template = self.config_templates['sqlite_python']
            return template.format(
                database_file=env_params.get('database', f'{environment}.db')
            )
        elif schema.database_type == DatabaseType.POSTGRESQL:
            template = self.config_templates['postgresql_python']
            return template.format(
                host=env_params.get('host', 'localhost'),
                port=env_params.get('port', 5432),
                database=env_params.get('database', schema.name),
                user=env_params.get('user', 'postgres'),
                password=env_params.get('password', '')
            )
        else:
            return f"# Configuration for {schema.database_type.value} not implemented"

    def _write_config_file(self, content: str, environment: str,
                          database_type: DatabaseType) -> str:
        """Write configuration file to disk"""
        # Create config directory
        config_dir = self.project_path / "config"
        config_dir.mkdir(exist_ok=True)

        # Determine file extension based on database type
        if database_type in [DatabaseType.SQLITE, DatabaseType.POSTGRESQL]:
            filename = f"database_{environment}.py"
        else:
            filename = f"database_{environment}.conf"

        config_file = config_dir / filename

        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(content)

        self.logger.info(f"Generated config file: {config_file}")
        return str(config_file)

    def _generate_connection_helpers(self, schema: DatabaseSchema,
                                   connection_params: Dict[str, Any]) -> Dict[str, str]:
        """Generate connection helper files"""
        helpers = {}

        # Generate database models file
        models_content = self._generate_models_file(schema)
        models_file = self.project_path / "models.py"

        with open(models_file, 'w', encoding='utf-8') as f:
            f.write(models_content)

        helpers['models'] = str(models_file)

        # Generate database utilities
        utils_content = self._generate_utils_file(schema)
        utils_file = self.project_path / "database_utils.py"

        with open(utils_file, 'w', encoding='utf-8') as f:
            f.write(utils_content)

        helpers['utils'] = str(utils_file)

        return helpers

    def _generate_env_file(self, schema: DatabaseSchema,
                          connection_params: Dict[str, Any]) -> str:
        """Generate .env file template"""
        env_content = self.config_templates['env_template'].format(
            host=connection_params.get('host', 'localhost'),
            port=connection_params.get('port', 5432),
            database=connection_params.get('database', schema.name),
            user=connection_params.get('user', 'user'),
            password=connection_params.get('password', 'password'),
            environment='development'
        )

        env_file = self.project_path / ".env.example"

        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(env_content)

        return str(env_file)

    def _generate_init_script(self, schema: DatabaseSchema) -> str:
        """Generate database initialization script"""
        init_content = f'''#!/usr/bin/env python3
"""
Database initialization script for {schema.name}
Auto-generated by AI Coding Agent
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config.database_development import init_database

def main():
    """Initialize the database with schema"""
    try:
        print("Initializing database...")
        init_database()
        print("Database initialized successfully!")
    except Exception as e:
        print(f"Database initialization failed: {{e}}")
        sys.exit(1)

if __name__ == "__main__":
    main()
'''

        init_file = self.project_path / "init_database.py"

        with open(init_file, 'w', encoding='utf-8') as f:
            f.write(init_content)

        # Make script executable on Unix systems
        try:
            import stat
            init_file.chmod(init_file.stat().st_mode | stat.S_IEXEC)
        except:
            pass  # Windows or permission issues

        return str(init_file)

    def _generate_models_file(self, schema: DatabaseSchema) -> str:
        """Generate database models file"""
        models_content = f'''"""
Database models for {schema.name}
Auto-generated by AI Coding Agent
"""

from dataclasses import dataclass
from typing import Optional
from datetime import datetime

'''

        # Generate model classes for each table
        for table in schema.tables:
            models_content += f'''
@dataclass
class {table.name.title()}:
    """Model for {table.name} table"""
'''

            for field in table.fields:
                field_type = "int" if field.field_type == FieldType.INTEGER else "str"
                if field.field_type == FieldType.BOOLEAN:
                    field_type = "bool"
                elif field.field_type == FieldType.DATETIME:
                    field_type = "datetime"

                nullable = "Optional[" + field_type + "]" if field.nullable else field_type
                default = " = None" if field.nullable else ""

                models_content += f"    {field.name}: {nullable}{default}\n"

            models_content += "\n"

        return models_content

    def _generate_utils_file(self, schema: DatabaseSchema) -> str:
        """Generate database utilities file"""
        utils_content = f'''"""
Database utilities for {schema.name}
Auto-generated by AI Coding Agent
"""

from typing import List, Dict, Any, Optional
from config.database_development import get_connection

class DatabaseUtils:
    """Utility functions for database operations"""

    @staticmethod
    def execute_query(query: str, params: tuple = ()) -> List[Dict[str, Any]]:
        """Execute a SELECT query and return results"""
        conn = get_connection()
        cursor = conn.cursor()
        cursor.execute(query, params)

        # Get column names
        columns = [description[0] for description in cursor.description]

        # Fetch results and convert to dictionaries
        results = []
        for row in cursor.fetchall():
            results.append(dict(zip(columns, row)))

        conn.close()
        return results

    @staticmethod
    def execute_update(query: str, params: tuple = ()) -> int:
        """Execute an INSERT/UPDATE/DELETE query"""
        conn = get_connection()
        cursor = conn.cursor()
        cursor.execute(query, params)

        affected_rows = cursor.rowcount
        conn.commit()
        conn.close()

        return affected_rows

'''

        # Generate helper methods for each table
        for table in schema.tables:
            table_name = table.name
            class_name = table_name.title()

            utils_content += f'''
    @staticmethod
    def get_all_{table_name}() -> List[Dict[str, Any]]:
        """Get all records from {table_name} table"""
        return DatabaseUtils.execute_query("SELECT * FROM {table_name}")

    @staticmethod
    def get_{table_name}_by_id(id: int) -> Optional[Dict[str, Any]]:
        """Get {table_name} record by ID"""
        results = DatabaseUtils.execute_query(
            "SELECT * FROM {table_name} WHERE id = ?", (id,)
        )
        return results[0] if results else None

    @staticmethod
    def create_{table_name}(data: Dict[str, Any]) -> int:
        """Create new {table_name} record"""
        fields = list(data.keys())
        placeholders = ", ".join(["?" for _ in fields])
        query = f"INSERT INTO {table_name} ({{', '.join(fields)}}) VALUES ({{placeholders}})"

        return DatabaseUtils.execute_update(query, tuple(data.values()))
'''

        return utils_content
