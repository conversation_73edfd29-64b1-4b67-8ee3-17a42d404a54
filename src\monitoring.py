"""
Performance monitoring and health checks for the AI Coding Agent
"""

import time
import psutil
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from contextlib import contextmanager

from src.logger import get_logger
from src.config import get_config
from src.database import DatabaseManager

config = get_config()

@dataclass
class PerformanceMetric:
    """Performance metric data structure"""
    operation: str
    duration_ms: float
    timestamp: datetime
    model_used: Optional[str] = None
    tokens_used: int = 0
    memory_usage_mb: float = 0.0
    cpu_usage_percent: float = 0.0
    success: bool = True
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

@dataclass
class SystemHealth:
    """System health status"""
    timestamp: datetime
    cpu_usage_percent: float
    memory_usage_percent: float
    disk_usage_percent: float
    ollama_status: str
    database_status: str
    overall_status: str
    uptime_seconds: float

class PerformanceMonitor:
    """
    Performance monitoring and metrics collection
    """
    
    def __init__(self, database: Optional[DatabaseManager] = None):
        """Initialize performance monitor"""
        self.logger = get_logger(__name__)
        self.database = database
        self.metrics: List[PerformanceMetric] = []
        self.start_time = time.time()
        self.max_metrics_in_memory = 1000
        
        # Performance thresholds
        self.thresholds = {
            'llm_response_time_ms': 30000,  # 30 seconds
            'validation_time_ms': 1000,     # 1 second
            'database_query_ms': 500,       # 500ms
            'memory_usage_percent': 80,     # 80%
            'cpu_usage_percent': 90,        # 90%
        }
        
        self.logger.info("Performance monitor initialized")
    
    @contextmanager
    def measure_operation(self, operation: str, model_used: Optional[str] = None, 
                         tokens_used: int = 0, **metadata):
        """
        Context manager to measure operation performance
        
        Usage:
            with monitor.measure_operation("code_generation", model_used="phi3:mini"):
                result = generate_code()
        """
        start_time = time.time()
        start_memory = psutil.virtual_memory().percent
        start_cpu = psutil.cpu_percent()
        
        success = True
        error_message = None
        
        try:
            yield
        except Exception as e:
            success = False
            error_message = str(e)
            raise
        finally:
            end_time = time.time()
            duration_ms = (end_time - start_time) * 1000
            
            # Get final system metrics
            end_memory = psutil.virtual_memory().percent
            end_cpu = psutil.cpu_percent()
            
            metric = PerformanceMetric(
                operation=operation,
                duration_ms=duration_ms,
                timestamp=datetime.now(),
                model_used=model_used,
                tokens_used=tokens_used,
                memory_usage_mb=end_memory,
                cpu_usage_percent=end_cpu,
                success=success,
                error_message=error_message,
                metadata=metadata
            )
            
            self.record_metric(metric)
    
    def record_metric(self, metric: PerformanceMetric):
        """Record a performance metric"""
        # Add to in-memory storage
        self.metrics.append(metric)
        
        # Trim if too many metrics
        if len(self.metrics) > self.max_metrics_in_memory:
            self.metrics = self.metrics[-self.max_metrics_in_memory:]
        
        # Save to database if available
        if self.database:
            try:
                import uuid
                self.database.log_performance_metric(
                    str(uuid.uuid4()),
                    metric.operation,
                    int(metric.duration_ms),
                    metric.model_used,
                    metric.tokens_used,
                    asdict(metric)
                )
            except Exception as e:
                self.logger.warning(f"Failed to save metric to database: {e}")
        
        # Check thresholds and log warnings
        self._check_performance_thresholds(metric)
    
    def _check_performance_thresholds(self, metric: PerformanceMetric):
        """Check if metric exceeds performance thresholds"""
        operation_threshold_key = f"{metric.operation}_time_ms"
        
        # Check operation-specific threshold
        if operation_threshold_key in self.thresholds:
            threshold = self.thresholds[operation_threshold_key]
            if metric.duration_ms > threshold:
                self.logger.warning(
                    f"Performance threshold exceeded: {metric.operation} took "
                    f"{metric.duration_ms:.2f}ms (threshold: {threshold}ms)"
                )
        
        # Check system resource thresholds
        if metric.memory_usage_mb > self.thresholds['memory_usage_percent']:
            self.logger.warning(f"High memory usage: {metric.memory_usage_mb:.1f}%")
        
        if metric.cpu_usage_percent > self.thresholds['cpu_usage_percent']:
            self.logger.warning(f"High CPU usage: {metric.cpu_usage_percent:.1f}%")
    
    def get_performance_summary(self, hours: int = 24) -> Dict[str, Any]:
        """Get performance summary for the last N hours"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        recent_metrics = [m for m in self.metrics if m.timestamp > cutoff_time]
        
        if not recent_metrics:
            return {"message": "No metrics available", "hours": hours}
        
        # Calculate statistics
        total_operations = len(recent_metrics)
        successful_operations = len([m for m in recent_metrics if m.success])
        success_rate = (successful_operations / total_operations) * 100
        
        # Group by operation type
        operations = {}
        for metric in recent_metrics:
            if metric.operation not in operations:
                operations[metric.operation] = []
            operations[metric.operation].append(metric.duration_ms)
        
        # Calculate stats per operation
        operation_stats = {}
        for op, durations in operations.items():
            operation_stats[op] = {
                "count": len(durations),
                "avg_duration_ms": sum(durations) / len(durations),
                "min_duration_ms": min(durations),
                "max_duration_ms": max(durations),
            }
        
        return {
            "period_hours": hours,
            "total_operations": total_operations,
            "successful_operations": successful_operations,
            "success_rate_percent": success_rate,
            "operation_stats": operation_stats,
            "uptime_seconds": time.time() - self.start_time
        }
    
    def get_system_health(self) -> SystemHealth:
        """Get current system health status"""
        # System metrics
        cpu_usage = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # Check Ollama status
        ollama_status = self._check_ollama_health()
        
        # Check database status
        database_status = self._check_database_health()
        
        # Determine overall status
        overall_status = "healthy"
        if cpu_usage > 90 or memory.percent > 90 or disk.percent > 90:
            overall_status = "warning"
        if ollama_status != "healthy" or database_status != "healthy":
            overall_status = "degraded"
        
        return SystemHealth(
            timestamp=datetime.now(),
            cpu_usage_percent=cpu_usage,
            memory_usage_percent=memory.percent,
            disk_usage_percent=disk.percent,
            ollama_status=ollama_status,
            database_status=database_status,
            overall_status=overall_status,
            uptime_seconds=time.time() - self.start_time
        )
    
    def _check_ollama_health(self) -> str:
        """Check Ollama service health"""
        try:
            import requests
            response = requests.get("http://localhost:11434/api/tags", timeout=5)
            if response.status_code == 200:
                return "healthy"
            else:
                return "unhealthy"
        except Exception:
            return "unavailable"
    
    def _check_database_health(self) -> str:
        """Check database health"""
        if not self.database:
            return "not_configured"
        
        try:
            # Try a simple query
            conversations = self.database.list_conversations(limit=1)
            return "healthy"
        except Exception as e:
            self.logger.warning(f"Database health check failed: {e}")
            return "unhealthy"

class CircuitBreaker:
    """
    Circuit breaker pattern implementation for resilient service calls
    """
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60, 
                 expected_exception: type = Exception):
        """
        Initialize circuit breaker
        
        Args:
            failure_threshold: Number of failures before opening circuit
            recovery_timeout: Seconds to wait before trying again
            expected_exception: Exception type that triggers circuit breaker
        """
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "closed"  # closed, open, half_open
        
        self.logger = get_logger(__name__)
    
    def __call__(self, func):
        """Decorator to apply circuit breaker to a function"""
        def wrapper(*args, **kwargs):
            return self.call(func, *args, **kwargs)
        return wrapper
    
    def call(self, func, *args, **kwargs):
        """Call function with circuit breaker protection"""
        if self.state == "open":
            if self._should_attempt_reset():
                self.state = "half_open"
                self.logger.info("Circuit breaker attempting reset")
            else:
                raise Exception("Circuit breaker is open - service unavailable")
        
        try:
            result = func(*args, **kwargs)
            self._on_success()
            return result
        except self.expected_exception as e:
            self._on_failure()
            raise e
    
    def _should_attempt_reset(self) -> bool:
        """Check if enough time has passed to attempt reset"""
        if self.last_failure_time is None:
            return True
        return time.time() - self.last_failure_time >= self.recovery_timeout
    
    def _on_success(self):
        """Handle successful call"""
        self.failure_count = 0
        self.state = "closed"
    
    def _on_failure(self):
        """Handle failed call"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = "open"
            self.logger.warning(
                f"Circuit breaker opened after {self.failure_count} failures"
            )

# Global performance monitor instance
_performance_monitor = None

def get_performance_monitor(database: Optional[DatabaseManager] = None) -> PerformanceMonitor:
    """Get global performance monitor instance"""
    global _performance_monitor
    if _performance_monitor is None:
        _performance_monitor = PerformanceMonitor(database)
    return _performance_monitor
