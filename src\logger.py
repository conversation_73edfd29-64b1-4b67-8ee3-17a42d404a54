# AI Coding Agent - Logging Configuration
"""
Centralized logging configuration for the coding agent
"""

import logging
import logging.handlers
from typing import Optional
from rich.logging import <PERSON>Handler
from rich.console import Console

from src.config import get_config

config = get_config()

class CodingAgentLogger:
    """
    Centralized logger for the coding agent with rich formatting
    and file rotation
    """
    
    def __init__(self, name: str = "coding_agent"):
        self.name = name
        self.logger = logging.getLogger(name)
        self.console = Console()
        self._setup_logger()
    
    def _setup_logger(self):
        """Set up logger with console and file handlers"""
        
        # Clear any existing handlers
        self.logger.handlers.clear()
        
        # Set log level
        log_level = getattr(logging, config.LOG_LEVEL.upper(), logging.INFO)
        self.logger.setLevel(log_level)
        
        # Create formatters
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s'
        )
        
        # Console handler with Rich formatting
        console_handler = RichHandler(
            console=self.console,
            show_time=True,
            show_path=True,
            markup=True,
            rich_tracebacks=True
        )
        console_handler.setLevel(log_level)
        
        # File handler with rotation
        file_handler = logging.handlers.RotatingFileHandler(
            filename=config.LOG_FILE,
            maxBytes=config.LOG_MAX_BYTES,
            backupCount=config.LOG_BACKUP_COUNT,
            encoding='utf-8'
        )
        file_handler.setLevel(log_level)
        file_handler.setFormatter(file_formatter)
        
        # Add handlers to logger
        self.logger.addHandler(console_handler)
        self.logger.addHandler(file_handler)
        
        # Prevent propagation to root logger
        self.logger.propagate = False
    
    def get_logger(self) -> logging.Logger:
        """Get the configured logger instance"""
        return self.logger
    
    def log_startup(self):
        """Log startup information"""
        self.logger.info("🤖 AI Coding Agent Starting Up")
        self.logger.info(f"Environment: {config.ENVIRONMENT}")
        self.logger.info(f"Debug Mode: {config.DEBUG}")
        self.logger.info(f"Log Level: {config.LOG_LEVEL}")
        self.logger.info(f"Log File: {config.LOG_FILE}")
        self.logger.info(f"Ollama Host: {config.OLLAMA_HOST}")
    
    def log_shutdown(self):
        """Log shutdown information"""
        self.logger.info("🛑 AI Coding Agent Shutting Down")

# Global logger instance
_logger_instance: Optional[CodingAgentLogger] = None

def get_logger(name: str = "coding_agent") -> logging.Logger:
    """
    Get a logger instance with consistent configuration
    
    Args:
        name: Logger name
        
    Returns:
        Configured logger instance
    """
    global _logger_instance
    
    if _logger_instance is None:
        _logger_instance = CodingAgentLogger(name)
    
    return _logger_instance.get_logger()

def log_startup():
    """Log application startup"""
    global _logger_instance
    
    if _logger_instance is None:
        _logger_instance = CodingAgentLogger()
    
    _logger_instance.log_startup()

def log_shutdown():
    """Log application shutdown"""
    global _logger_instance
    
    if _logger_instance is not None:
        _logger_instance.log_shutdown()
