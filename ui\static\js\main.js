// AI Coding Agent - Main JavaScript
class AICodeAgent {
    constructor() {
        this.socket = null;
        this.currentProject = null;
        this.isConnected = false;
        this.messageHistory = [];
        
        this.init();
    }

    init() {
        this.initializeSocket();
        this.setupEventListeners();
        this.setupResizeHandle();
        this.loadMessageHistory();
    }

    initializeSocket() {
        this.socket = io();
        
        this.socket.on('connect', () => {
            this.isConnected = true;
            this.updateConnectionStatus(true);
            console.log('Connected to AI Coding Agent');
        });

        this.socket.on('disconnect', () => {
            this.isConnected = false;
            this.updateConnectionStatus(false);
            console.log('Disconnected from AI Coding Agent');
        });

        this.socket.on('new_message', (message) => {
            this.addMessage(message);
        });

        this.socket.on('project_state', (projectState) => {
            this.updateProjectState(projectState);
        });

        this.socket.on('typing_indicator', (data) => {
            this.updateTypingIndicator(data);
        });

        this.socket.on('search_results', (data) => {
            this.displaySearchResults(data.results);
        });

        this.socket.on('error', (error) => {
            this.showError(error.message);
        });
    }

    setupEventListeners() {
        // Send message button
        document.getElementById('send-btn').addEventListener('click', () => {
            this.sendMessage();
        });

        // Message input enter key and typing indicators
        const messageInput = document.getElementById('message-input');
        let typingTimer;

        messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            } else {
                // Send typing indicator
                this.socket.emit('typing', { typing: true });

                // Clear previous timer
                clearTimeout(typingTimer);

                // Stop typing after 1 second of inactivity
                typingTimer = setTimeout(() => {
                    this.socket.emit('typing', { typing: false });
                }, 1000);
            }
        });

        messageInput.addEventListener('blur', () => {
            this.socket.emit('typing', { typing: false });
        });

        // New project button
        document.getElementById('new-project-btn').addEventListener('click', () => {
            this.showNewProjectDialog();
        });

        // Voice input button
        document.getElementById('voice-btn').addEventListener('click', () => {
            this.toggleVoiceInput();
        });

        // Tab switching
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTab(e.target.closest('.tab-btn'));
            });
        });

        // File upload handling
        this.setupFileUpload();

        // Search functionality
        this.setupMessageSearch();
    }

    setupResizeHandle() {
        const resizeHandle = document.getElementById('resize-handle');
        const commPanel = document.getElementById('communication-panel');
        const projectPanel = document.getElementById('project-panel');
        
        let isResizing = false;

        resizeHandle.addEventListener('mousedown', (e) => {
            isResizing = true;
            document.addEventListener('mousemove', handleResize);
            document.addEventListener('mouseup', stopResize);
        });

        function handleResize(e) {
            if (!isResizing) return;
            
            const containerWidth = document.querySelector('#app > div:last-child').offsetWidth;
            const newWidth = (e.clientX / containerWidth) * 100;
            
            if (newWidth > 20 && newWidth < 80) {
                commPanel.style.width = `${newWidth}%`;
                projectPanel.style.width = `${100 - newWidth}%`;
            }
        }

        function stopResize() {
            isResizing = false;
            document.removeEventListener('mousemove', handleResize);
            document.removeEventListener('mouseup', stopResize);
        }
    }

    sendMessage() {
        const input = document.getElementById('message-input');
        const message = input.value.trim();

        if (!message) return;

        // Detect message type
        const messageType = this.detectMessageType(message);

        // Clear input
        input.value = '';

        // Stop typing indicator
        this.socket.emit('typing', { typing: false });

        // Send to server with enhanced data
        this.socket.emit('send_message', {
            message: message,
            type: messageType,
            attachments: this.pendingAttachments || []
        });

        // Clear pending attachments
        this.pendingAttachments = [];
        this.updateAttachmentDisplay();
    }

    detectMessageType(message) {
        // Simple detection for code blocks
        if (message.includes('```') || message.includes('function') || message.includes('class ')) {
            return 'code';
        }
        return 'text';
    }

    setupFileUpload() {
        // Create hidden file input
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.multiple = true;
        fileInput.style.display = 'none';
        document.body.appendChild(fileInput);

        // Add click handler to voice button (repurpose for file upload)
        const voiceBtn = document.getElementById('voice-btn');
        voiceBtn.innerHTML = '<i class="fas fa-paperclip"></i>';
        voiceBtn.title = 'Attach Files';

        voiceBtn.addEventListener('click', () => {
            fileInput.click();
        });

        fileInput.addEventListener('change', (e) => {
            this.handleFileUpload(e.target.files);
        });

        // Drag and drop support
        const messageContainer = document.getElementById('messages-container');

        messageContainer.addEventListener('dragover', (e) => {
            e.preventDefault();
            messageContainer.classList.add('drag-over');
        });

        messageContainer.addEventListener('dragleave', (e) => {
            e.preventDefault();
            messageContainer.classList.remove('drag-over');
        });

        messageContainer.addEventListener('drop', (e) => {
            e.preventDefault();
            messageContainer.classList.remove('drag-over');
            this.handleFileUpload(e.dataTransfer.files);
        });

        this.pendingAttachments = [];
    }

    setupMessageSearch() {
        // Add search functionality (can be enhanced with a search UI)
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'f') {
                e.preventDefault();
                this.showSearchDialog();
            }
        });
    }

    handleFileUpload(files) {
        Array.from(files).forEach(file => {
            if (file.size > 5 * 1024 * 1024) { // 5MB limit
                this.showError(`File ${file.name} is too large (max 5MB)`);
                return;
            }

            const reader = new FileReader();
            reader.onload = (e) => {
                const attachment = {
                    filename: file.name,
                    content: e.target.result.split(',')[1], // Remove data URL prefix
                    mime_type: file.type,
                    size: file.size
                };

                this.pendingAttachments.push(attachment);
                this.updateAttachmentDisplay();
            };
            reader.readAsDataURL(file);
        });
    }

    updateAttachmentDisplay() {
        // Create or update attachment display area
        let attachmentArea = document.getElementById('attachment-area');

        if (!attachmentArea) {
            attachmentArea = document.createElement('div');
            attachmentArea.id = 'attachment-area';
            attachmentArea.className = 'attachment-area p-2 border-t border-gray-200';

            const messageInput = document.getElementById('message-input');
            messageInput.parentNode.insertBefore(attachmentArea, messageInput);
        }

        if (this.pendingAttachments.length === 0) {
            attachmentArea.style.display = 'none';
            return;
        }

        attachmentArea.style.display = 'block';
        attachmentArea.innerHTML = `
            <div class="text-sm text-gray-600 mb-2">Attachments:</div>
            <div class="flex flex-wrap gap-2">
                ${this.pendingAttachments.map((attachment, index) => `
                    <div class="attachment-item bg-blue-100 px-3 py-1 rounded-full text-sm flex items-center">
                        <i class="fas fa-file mr-2"></i>
                        <span>${attachment.filename}</span>
                        <button onclick="aiCodeAgent.removeAttachment(${index})" class="ml-2 text-red-500 hover:text-red-700">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `).join('')}
            </div>
        `;
    }

    removeAttachment(index) {
        this.pendingAttachments.splice(index, 1);
        this.updateAttachmentDisplay();
    }

    addMessage(messageData) {
        const container = document.getElementById('messages-container');
        const messageElement = this.createMessageElement(messageData);
        
        container.appendChild(messageElement);
        container.scrollTop = container.scrollHeight;
        
        this.messageHistory.push(messageData);
    }

    createMessageElement(messageData) {
        const div = document.createElement('div');
        div.className = `message ${messageData.type}-message`;
        
        const timestamp = new Date(messageData.timestamp).toLocaleTimeString();
        
        if (messageData.type === 'user') {
            div.innerHTML = `
                <div class="flex items-start space-x-3 justify-end">
                    <div class="flex-1 text-right">
                        <div class="message-content bg-blue-600 text-white rounded-lg p-3 inline-block max-w-xs lg:max-w-md">
                            <p>${this.escapeHtml(messageData.content)}</p>
                        </div>
                        <span class="text-xs text-gray-500 mt-1 block">${timestamp}</span>
                    </div>
                    <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-user text-white text-sm"></i>
                    </div>
                </div>
            `;
        } else if (messageData.type === 'assistant') {
            div.innerHTML = `
                <div class="flex items-start space-x-3">
                    <div class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-robot text-white text-sm"></i>
                    </div>
                    <div class="flex-1">
                        <div class="message-content bg-gray-100 rounded-lg p-3">
                            <p>${this.formatMessage(messageData.content)}</p>
                        </div>
                        <span class="text-xs text-gray-500 mt-1 block">${timestamp}</span>
                    </div>
                </div>
            `;
        } else if (messageData.type === 'error') {
            div.innerHTML = `
                <div class="flex items-start space-x-3">
                    <div class="w-8 h-8 bg-red-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-exclamation-triangle text-white text-sm"></i>
                    </div>
                    <div class="flex-1">
                        <div class="message-content bg-red-50 border-l-4 border-red-500 rounded-lg p-3">
                            <p class="text-red-800">${this.escapeHtml(messageData.content)}</p>
                        </div>
                        <span class="text-xs text-gray-500 mt-1 block">${timestamp}</span>
                    </div>
                </div>
            `;
        }
        
        return div;
    }

    formatMessage(content) {
        // Enhanced formatting for messages
        content = this.escapeHtml(content);

        // Convert URLs to links
        content = content.replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank" class="text-blue-600 underline">$1</a>');

        // Convert code blocks with syntax highlighting
        content = content.replace(/```(\w+)?\n([\s\S]*?)```/g, (match, lang, code) => {
            return `<pre class="bg-gray-900 text-green-400 p-3 rounded-lg overflow-x-auto my-2"><code class="language-${lang || 'text'}">${code.trim()}</code></pre>`;
        });

        // Convert inline code
        content = content.replace(/`([^`]+)`/g, '<code class="bg-gray-200 px-1 py-0.5 rounded text-sm font-mono">$1</code>');

        // Convert markdown-style formatting
        content = content.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
        content = content.replace(/\*(.*?)\*/g, '<em>$1</em>');

        // Convert bullet points
        content = content.replace(/^• (.+)$/gm, '<li class="ml-4">$1</li>');
        content = content.replace(/(<li.*<\/li>)/s, '<ul class="list-disc list-inside">$1</ul>');

        return content;
    }

    updateTypingIndicator(data) {
        const container = document.getElementById('messages-container');
        let typingIndicator = document.getElementById('typing-indicator');

        if (data.typing) {
            if (!typingIndicator) {
                typingIndicator = document.createElement('div');
                typingIndicator.id = 'typing-indicator';
                typingIndicator.className = 'typing-indicator flex items-center space-x-2 p-3 text-gray-500';
                typingIndicator.innerHTML = `
                    <div class="flex space-x-1">
                        <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                        <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                        <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                    </div>
                    <span>${data.user} is typing...</span>
                `;
                container.appendChild(typingIndicator);
            }
        } else {
            if (typingIndicator) {
                typingIndicator.remove();
            }
        }

        container.scrollTop = container.scrollHeight;
    }

    showSearchDialog() {
        const query = prompt('Search messages:');
        if (query) {
            this.socket.emit('search_messages', { query: query });
        }
    }

    displaySearchResults(results) {
        if (results.length === 0) {
            this.showError('No messages found');
            return;
        }

        // Create search results modal (simplified)
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        modal.innerHTML = `
            <div class="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-96 overflow-y-auto">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold">Search Results (${results.length})</h3>
                    <button onclick="this.parentElement.parentElement.parentElement.remove()" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="space-y-3">
                    ${results.map(msg => `
                        <div class="border-l-4 border-blue-500 pl-3 py-2">
                            <div class="text-sm text-gray-600">${new Date(msg.timestamp).toLocaleString()}</div>
                            <div class="text-gray-800">${this.formatMessage(msg.content)}</div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    updateConnectionStatus(connected) {
        const statusElement = document.getElementById('connection-status');
        const dot = statusElement.querySelector('div');
        const text = statusElement.querySelector('span');
        
        if (connected) {
            dot.className = 'w-2 h-2 bg-green-500 rounded-full';
            text.textContent = 'Connected';
        } else {
            dot.className = 'w-2 h-2 bg-red-500 rounded-full';
            text.textContent = 'Disconnected';
        }
    }

    updateProjectState(projectState) {
        this.currentProject = projectState;
        
        const statusElement = document.getElementById('project-status');
        const dot = statusElement.querySelector('div');
        const text = statusElement.querySelector('span');
        
        // Update status indicator
        switch (projectState.status) {
            case 'idle':
                dot.className = 'w-2 h-2 bg-gray-400 rounded-full';
                text.textContent = 'No Project';
                break;
            case 'generating':
                dot.className = 'w-2 h-2 bg-yellow-500 rounded-full animate-pulse';
                text.textContent = 'Generating...';
                this.showLoadingOverlay(true);
                break;
            case 'ready':
                dot.className = 'w-2 h-2 bg-green-500 rounded-full';
                text.textContent = projectState.name || 'Project Ready';
                this.showLoadingOverlay(false);
                this.updateFileTree(projectState.files);
                break;
            case 'error':
                dot.className = 'w-2 h-2 bg-red-500 rounded-full';
                text.textContent = 'Error';
                this.showLoadingOverlay(false);
                break;
        }
    }

    updateFileTree(files) {
        const fileTree = document.getElementById('file-tree');
        
        if (!files || files.length === 0) {
            fileTree.innerHTML = '<div class="text-gray-500 italic">No files</div>';
            return;
        }
        
        fileTree.innerHTML = '';
        
        files.forEach(file => {
            const fileElement = document.createElement('div');
            fileElement.className = 'file-item flex items-center space-x-2 py-1';
            
            const icon = this.getFileIcon(file);
            fileElement.innerHTML = `
                <i class="${icon} text-sm"></i>
                <span class="text-sm">${file}</span>
            `;
            
            fileTree.appendChild(fileElement);
        });
    }

    getFileIcon(filename) {
        const ext = filename.split('.').pop().toLowerCase();
        
        switch (ext) {
            case 'js':
            case 'jsx':
                return 'fab fa-js-square text-yellow-500';
            case 'ts':
            case 'tsx':
                return 'fab fa-js-square text-blue-500';
            case 'html':
                return 'fab fa-html5 text-orange-500';
            case 'css':
                return 'fab fa-css3-alt text-blue-500';
            case 'py':
                return 'fab fa-python text-green-500';
            case 'json':
                return 'fas fa-file-code text-gray-500';
            case 'md':
                return 'fab fa-markdown text-gray-700';
            default:
                return 'fas fa-file text-gray-500';
        }
    }

    showLoadingOverlay(show) {
        const overlay = document.getElementById('loading-overlay');
        overlay.classList.toggle('hidden', !show);
    }

    showNewProjectDialog() {
        // Simple prompt for now - can be enhanced with a proper modal
        const projectType = prompt('What type of project would you like to create?\n\nOptions:\n- react_app\n- vue_app\n- angular_app\n- nodejs_api\n- django_app\n- fullstack_react_node', 'react_app');
        
        if (projectType) {
            const requirements = prompt('Describe your project requirements:');
            if (requirements) {
                this.generateProject(projectType, requirements);
            }
        }
    }

    generateProject(projectType, requirements) {
        fetch('/api/project/generate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                project_type: projectType,
                requirements: requirements
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                this.showError(data.error);
            }
        })
        .catch(error => {
            this.showError('Failed to start project generation');
        });
    }

    switchTab(tabBtn) {
        // Remove active class from all tabs
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active', 'text-blue-600', 'border-blue-600');
            btn.classList.add('text-gray-600');
        });
        
        // Add active class to clicked tab
        tabBtn.classList.add('active', 'text-blue-600', 'border-blue-600');
        tabBtn.classList.remove('text-gray-600');
        
        // Update content based on tab
        const tabText = tabBtn.textContent.trim();
        this.updatePreviewContent(tabText);
    }

    updatePreviewContent(tabType) {
        const content = document.getElementById('preview-content');
        
        switch (tabType) {
            case 'Preview':
                content.innerHTML = this.getPreviewContent();
                break;
            case 'Code':
                content.innerHTML = this.getCodeContent();
                break;
            case 'Terminal':
                content.innerHTML = this.getTerminalContent();
                break;
        }
    }

    getPreviewContent() {
        if (this.currentProject && this.currentProject.status === 'ready') {
            return `
                <iframe class="preview-iframe" src="about:blank">
                </iframe>
            `;
        }
        return `
            <div class="h-full flex items-center justify-center text-gray-500">
                <div class="text-center">
                    <i class="fas fa-eye text-6xl mb-4 text-gray-300"></i>
                    <h3 class="text-xl font-semibold mb-2">No Preview Available</h3>
                    <p>Generate a project to see the live preview</p>
                </div>
            </div>
        `;
    }

    getCodeContent() {
        return `
            <div class="h-full bg-gray-900 text-green-400 p-4 font-mono text-sm">
                <div class="mb-2">// Code editor will be implemented here</div>
                <div class="text-gray-500">Select a file from the file tree to view its contents</div>
            </div>
        `;
    }

    getTerminalContent() {
        return `
            <div class="terminal h-full">
                <div class="terminal-prompt">$ </div>
                <div class="terminal-output">Terminal ready. Project commands will appear here.</div>
            </div>
        `;
    }

    toggleVoiceInput() {
        // Voice input functionality - placeholder for now
        alert('Voice input feature coming soon!');
    }

    showError(message) {
        // Simple error display - can be enhanced with proper notifications
        console.error(message);
        alert(`Error: ${message}`);
    }

    loadMessageHistory() {
        fetch('/api/messages')
            .then(response => response.json())
            .then(messages => {
                messages.forEach(message => {
                    this.addMessage(message);
                });
            })
            .catch(error => {
                console.error('Failed to load message history:', error);
            });
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.aiCodeAgent = new AICodeAgent();
});
