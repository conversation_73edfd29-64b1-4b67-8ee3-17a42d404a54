// AI Coding Agent - Main JavaScript
class AICodeAgent {
    constructor() {
        this.socket = null;
        this.currentProject = null;
        this.isConnected = false;
        this.messageHistory = [];
        
        this.init();
    }

    init() {
        this.initializeSocket();
        this.setupEventListeners();
        this.setupResizeHandle();
        this.loadMessageHistory();
    }

    initializeSocket() {
        this.socket = io();
        
        this.socket.on('connect', () => {
            this.isConnected = true;
            this.updateConnectionStatus(true);
            console.log('Connected to AI Coding Agent');
        });

        this.socket.on('disconnect', () => {
            this.isConnected = false;
            this.updateConnectionStatus(false);
            console.log('Disconnected from AI Coding Agent');
        });

        this.socket.on('new_message', (message) => {
            this.addMessage(message);
        });

        this.socket.on('project_state', (projectState) => {
            this.updateProjectState(projectState);
        });

        this.socket.on('error', (error) => {
            this.showError(error.message);
        });
    }

    setupEventListeners() {
        // Send message button
        document.getElementById('send-btn').addEventListener('click', () => {
            this.sendMessage();
        });

        // Message input enter key
        document.getElementById('message-input').addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        // New project button
        document.getElementById('new-project-btn').addEventListener('click', () => {
            this.showNewProjectDialog();
        });

        // Voice input button
        document.getElementById('voice-btn').addEventListener('click', () => {
            this.toggleVoiceInput();
        });

        // Tab switching
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTab(e.target.closest('.tab-btn'));
            });
        });
    }

    setupResizeHandle() {
        const resizeHandle = document.getElementById('resize-handle');
        const commPanel = document.getElementById('communication-panel');
        const projectPanel = document.getElementById('project-panel');
        
        let isResizing = false;

        resizeHandle.addEventListener('mousedown', (e) => {
            isResizing = true;
            document.addEventListener('mousemove', handleResize);
            document.addEventListener('mouseup', stopResize);
        });

        function handleResize(e) {
            if (!isResizing) return;
            
            const containerWidth = document.querySelector('#app > div:last-child').offsetWidth;
            const newWidth = (e.clientX / containerWidth) * 100;
            
            if (newWidth > 20 && newWidth < 80) {
                commPanel.style.width = `${newWidth}%`;
                projectPanel.style.width = `${100 - newWidth}%`;
            }
        }

        function stopResize() {
            isResizing = false;
            document.removeEventListener('mousemove', handleResize);
            document.removeEventListener('mouseup', stopResize);
        }
    }

    sendMessage() {
        const input = document.getElementById('message-input');
        const message = input.value.trim();
        
        if (!message) return;
        
        // Clear input
        input.value = '';
        
        // Send to server
        this.socket.emit('send_message', { message: message });
    }

    addMessage(messageData) {
        const container = document.getElementById('messages-container');
        const messageElement = this.createMessageElement(messageData);
        
        container.appendChild(messageElement);
        container.scrollTop = container.scrollHeight;
        
        this.messageHistory.push(messageData);
    }

    createMessageElement(messageData) {
        const div = document.createElement('div');
        div.className = `message ${messageData.type}-message`;
        
        const timestamp = new Date(messageData.timestamp).toLocaleTimeString();
        
        if (messageData.type === 'user') {
            div.innerHTML = `
                <div class="flex items-start space-x-3 justify-end">
                    <div class="flex-1 text-right">
                        <div class="message-content bg-blue-600 text-white rounded-lg p-3 inline-block max-w-xs lg:max-w-md">
                            <p>${this.escapeHtml(messageData.content)}</p>
                        </div>
                        <span class="text-xs text-gray-500 mt-1 block">${timestamp}</span>
                    </div>
                    <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-user text-white text-sm"></i>
                    </div>
                </div>
            `;
        } else if (messageData.type === 'assistant') {
            div.innerHTML = `
                <div class="flex items-start space-x-3">
                    <div class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-robot text-white text-sm"></i>
                    </div>
                    <div class="flex-1">
                        <div class="message-content bg-gray-100 rounded-lg p-3">
                            <p>${this.formatMessage(messageData.content)}</p>
                        </div>
                        <span class="text-xs text-gray-500 mt-1 block">${timestamp}</span>
                    </div>
                </div>
            `;
        } else if (messageData.type === 'error') {
            div.innerHTML = `
                <div class="flex items-start space-x-3">
                    <div class="w-8 h-8 bg-red-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-exclamation-triangle text-white text-sm"></i>
                    </div>
                    <div class="flex-1">
                        <div class="message-content bg-red-50 border-l-4 border-red-500 rounded-lg p-3">
                            <p class="text-red-800">${this.escapeHtml(messageData.content)}</p>
                        </div>
                        <span class="text-xs text-gray-500 mt-1 block">${timestamp}</span>
                    </div>
                </div>
            `;
        }
        
        return div;
    }

    formatMessage(content) {
        // Basic formatting for messages
        content = this.escapeHtml(content);
        
        // Convert URLs to links
        content = content.replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank" class="text-blue-600 underline">$1</a>');
        
        // Convert code blocks
        content = content.replace(/`([^`]+)`/g, '<code class="bg-gray-200 px-1 py-0.5 rounded text-sm">$1</code>');
        
        return content;
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    updateConnectionStatus(connected) {
        const statusElement = document.getElementById('connection-status');
        const dot = statusElement.querySelector('div');
        const text = statusElement.querySelector('span');
        
        if (connected) {
            dot.className = 'w-2 h-2 bg-green-500 rounded-full';
            text.textContent = 'Connected';
        } else {
            dot.className = 'w-2 h-2 bg-red-500 rounded-full';
            text.textContent = 'Disconnected';
        }
    }

    updateProjectState(projectState) {
        this.currentProject = projectState;
        
        const statusElement = document.getElementById('project-status');
        const dot = statusElement.querySelector('div');
        const text = statusElement.querySelector('span');
        
        // Update status indicator
        switch (projectState.status) {
            case 'idle':
                dot.className = 'w-2 h-2 bg-gray-400 rounded-full';
                text.textContent = 'No Project';
                break;
            case 'generating':
                dot.className = 'w-2 h-2 bg-yellow-500 rounded-full animate-pulse';
                text.textContent = 'Generating...';
                this.showLoadingOverlay(true);
                break;
            case 'ready':
                dot.className = 'w-2 h-2 bg-green-500 rounded-full';
                text.textContent = projectState.name || 'Project Ready';
                this.showLoadingOverlay(false);
                this.updateFileTree(projectState.files);
                break;
            case 'error':
                dot.className = 'w-2 h-2 bg-red-500 rounded-full';
                text.textContent = 'Error';
                this.showLoadingOverlay(false);
                break;
        }
    }

    updateFileTree(files) {
        const fileTree = document.getElementById('file-tree');
        
        if (!files || files.length === 0) {
            fileTree.innerHTML = '<div class="text-gray-500 italic">No files</div>';
            return;
        }
        
        fileTree.innerHTML = '';
        
        files.forEach(file => {
            const fileElement = document.createElement('div');
            fileElement.className = 'file-item flex items-center space-x-2 py-1';
            
            const icon = this.getFileIcon(file);
            fileElement.innerHTML = `
                <i class="${icon} text-sm"></i>
                <span class="text-sm">${file}</span>
            `;
            
            fileTree.appendChild(fileElement);
        });
    }

    getFileIcon(filename) {
        const ext = filename.split('.').pop().toLowerCase();
        
        switch (ext) {
            case 'js':
            case 'jsx':
                return 'fab fa-js-square text-yellow-500';
            case 'ts':
            case 'tsx':
                return 'fab fa-js-square text-blue-500';
            case 'html':
                return 'fab fa-html5 text-orange-500';
            case 'css':
                return 'fab fa-css3-alt text-blue-500';
            case 'py':
                return 'fab fa-python text-green-500';
            case 'json':
                return 'fas fa-file-code text-gray-500';
            case 'md':
                return 'fab fa-markdown text-gray-700';
            default:
                return 'fas fa-file text-gray-500';
        }
    }

    showLoadingOverlay(show) {
        const overlay = document.getElementById('loading-overlay');
        overlay.classList.toggle('hidden', !show);
    }

    showNewProjectDialog() {
        // Simple prompt for now - can be enhanced with a proper modal
        const projectType = prompt('What type of project would you like to create?\n\nOptions:\n- react_app\n- vue_app\n- angular_app\n- nodejs_api\n- django_app\n- fullstack_react_node', 'react_app');
        
        if (projectType) {
            const requirements = prompt('Describe your project requirements:');
            if (requirements) {
                this.generateProject(projectType, requirements);
            }
        }
    }

    generateProject(projectType, requirements) {
        fetch('/api/project/generate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                project_type: projectType,
                requirements: requirements
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                this.showError(data.error);
            }
        })
        .catch(error => {
            this.showError('Failed to start project generation');
        });
    }

    switchTab(tabBtn) {
        // Remove active class from all tabs
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active', 'text-blue-600', 'border-blue-600');
            btn.classList.add('text-gray-600');
        });
        
        // Add active class to clicked tab
        tabBtn.classList.add('active', 'text-blue-600', 'border-blue-600');
        tabBtn.classList.remove('text-gray-600');
        
        // Update content based on tab
        const tabText = tabBtn.textContent.trim();
        this.updatePreviewContent(tabText);
    }

    updatePreviewContent(tabType) {
        const content = document.getElementById('preview-content');
        
        switch (tabType) {
            case 'Preview':
                content.innerHTML = this.getPreviewContent();
                break;
            case 'Code':
                content.innerHTML = this.getCodeContent();
                break;
            case 'Terminal':
                content.innerHTML = this.getTerminalContent();
                break;
        }
    }

    getPreviewContent() {
        if (this.currentProject && this.currentProject.status === 'ready') {
            return `
                <iframe class="preview-iframe" src="about:blank">
                </iframe>
            `;
        }
        return `
            <div class="h-full flex items-center justify-center text-gray-500">
                <div class="text-center">
                    <i class="fas fa-eye text-6xl mb-4 text-gray-300"></i>
                    <h3 class="text-xl font-semibold mb-2">No Preview Available</h3>
                    <p>Generate a project to see the live preview</p>
                </div>
            </div>
        `;
    }

    getCodeContent() {
        return `
            <div class="h-full bg-gray-900 text-green-400 p-4 font-mono text-sm">
                <div class="mb-2">// Code editor will be implemented here</div>
                <div class="text-gray-500">Select a file from the file tree to view its contents</div>
            </div>
        `;
    }

    getTerminalContent() {
        return `
            <div class="terminal h-full">
                <div class="terminal-prompt">$ </div>
                <div class="terminal-output">Terminal ready. Project commands will appear here.</div>
            </div>
        `;
    }

    toggleVoiceInput() {
        // Voice input functionality - placeholder for now
        alert('Voice input feature coming soon!');
    }

    showError(message) {
        // Simple error display - can be enhanced with proper notifications
        console.error(message);
        alert(`Error: ${message}`);
    }

    loadMessageHistory() {
        fetch('/api/messages')
            .then(response => response.json())
            .then(messages => {
                messages.forEach(message => {
                    this.addMessage(message);
                });
            })
            .catch(error => {
                console.error('Failed to load message history:', error);
            });
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.aiCodeAgent = new AICodeAgent();
});
