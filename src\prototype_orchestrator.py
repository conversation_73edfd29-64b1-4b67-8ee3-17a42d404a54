# AI Coding Agent - Prototype Orchestrator
"""
Main orchestrator that coordinates all four core capabilities in a unified workflow
"""

import json
import time
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime

from src.logger import get_logger
from src.database import DatabaseManager

# Import all core capabilities
from src.requirements_parser import RequirementParser
from src.dependency_manager import DependencyManager
from src.database_setup import ProjectDatabaseAnalyzer, DatabaseSelector, SchemaGenerator, MigrationManager, DatabaseConnectionManager
from src.error_monitor import ErrorMonitor, ErrorEvent
from src.root_cause_analyzer import RootCauseAnalyzer
from src.auto_fix_generator import AutoFixGenerator
from src.error_explainer import ErrorExplainer
from src.error_pattern_learner import <PERSON>rrorPatternLearner
from src.validators import CodeValidator
from src.models import LLMManager

@dataclass
class ProjectRequest:
    """Represents a user's project request"""
    request_id: str
    user_input: str
    project_type: str
    requirements: Dict[str, Any]
    context: Dict[str, Any]
    timestamp: datetime

@dataclass
class ProjectResult:
    """Result of project generation"""
    success: bool
    project_path: str
    generated_files: List[str]
    dependencies_installed: List[str]
    database_setup: Dict[str, Any]
    errors_detected: List[str]
    errors_fixed: List[str]
    execution_time: float
    explanation: str
    next_steps: List[str]

class PrototypeOrchestrator:
    """
    Main orchestrator that coordinates all four core capabilities:
    1. Natural Language Processing (Requirements Parser)
    2. Dependency Management
    3. Database Setup Automation
    4. Error Detection & Resolution
    """

    def __init__(self, projects_dir: str = "projects"):
        """Initialize the prototype orchestrator"""
        self.logger = get_logger(__name__)
        self.db = DatabaseManager()
        self.projects_dir = Path(projects_dir)
        self.projects_dir.mkdir(exist_ok=True)

        # Initialize all core components
        self.logger.info("Initializing core capabilities...")

        # Capability 1: Natural Language Processing
        self.requirements_parser = RequirementParser()

        # Capability 2: Dependency Management
        self.dependency_manager = DependencyManager()

        # Capability 3: Database Setup
        self.db_analyzer = ProjectDatabaseAnalyzer()
        self.db_selector = DatabaseSelector()
        self.schema_generator = SchemaGenerator()
        self.migration_manager = MigrationManager(str(self.projects_dir))
        self.connection_manager = DatabaseConnectionManager(str(self.projects_dir))

        # Capability 4: Error Detection & Resolution
        self.error_monitor = ErrorMonitor(str(self.projects_dir))
        self.root_cause_analyzer = RootCauseAnalyzer()
        self.auto_fix_generator = AutoFixGenerator()
        self.error_explainer = ErrorExplainer()
        self.pattern_learner = ErrorPatternLearner()

        # Supporting components
        self.validator = CodeValidator()
        self.llm_manager = LLMManager()

        # Start error monitoring
        self.error_monitor.start_monitoring()

        self.logger.info("PrototypeOrchestrator initialized successfully")

    def create_project_from_natural_language(self, user_input: str,
                                           user_context: Optional[Dict[str, Any]] = None) -> ProjectResult:
        """
        Main workflow: Create a complete project from natural language input

        Args:
            user_input: Natural language description of the project
            user_context: Optional user context (skill level, preferences, etc.)

        Returns:
            Complete project result with all generated files and setup
        """
        start_time = time.time()
        request_id = self._generate_request_id()

        self.logger.info(f"Starting project creation from natural language: {user_input[:100]}...")

        try:
            # Phase 1: Natural Language Processing
            self.logger.info("Phase 1: Parsing requirements from natural language")
            requirements_result = self._parse_requirements(user_input, user_context)

            # Phase 2: Project Planning and Code Generation
            self.logger.info("Phase 2: Generating project structure and code")
            generation_result = self._generate_project_code(requirements_result)

            # Phase 3: Dependency Management
            self.logger.info("Phase 3: Managing dependencies")
            dependency_result = self._manage_dependencies(generation_result)

            # Phase 4: Database Setup (if needed)
            self.logger.info("Phase 4: Setting up database")
            database_result = self._setup_database(requirements_result, generation_result)

            # Phase 5: Error Detection and Resolution
            self.logger.info("Phase 5: Detecting and resolving errors")
            error_resolution_result = self._detect_and_resolve_errors(generation_result)

            # Phase 6: Final Validation and Testing
            self.logger.info("Phase 6: Final validation")
            validation_result = self._validate_project(generation_result)

            # Compile final result
            execution_time = time.time() - start_time
            result = self._compile_project_result(
                request_id, requirements_result, generation_result,
                dependency_result, database_result, error_resolution_result,
                validation_result, execution_time
            )

            self.logger.info(f"Project creation completed in {execution_time:.2f} seconds")
            return result

        except Exception as e:
            self.logger.error(f"Error in project creation: {e}")
            return ProjectResult(
                success=False,
                project_path="",
                generated_files=[],
                dependencies_installed=[],
                database_setup={},
                errors_detected=[str(e)],
                errors_fixed=[],
                execution_time=time.time() - start_time,
                explanation=f"Project creation failed: {str(e)}",
                next_steps=["Review the error and try again"]
            )

    def _parse_requirements(self, user_input: str, user_context: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Phase 1: Parse natural language requirements"""
        try:
            # Parse the user input
            parsed_requirements = self.requirements_parser.parse_requirements(user_input)

            # For now, we'll proceed with the parsed requirements
            # In a real implementation, we'd generate clarifying questions

            return {
                'parsed_requirements': parsed_requirements,
                'clarifying_questions': [],
                'user_context': user_context or {},
                'project_type': getattr(parsed_requirements, 'project_type', 'web_app'),
                'technologies': getattr(parsed_requirements, 'technologies', []),
                'features': getattr(parsed_requirements, 'features', [])
            }

        except Exception as e:
            self.logger.error(f"Error parsing requirements: {e}")
            raise Exception(f"Failed to parse requirements: {e}")

    def _generate_project_code(self, requirements_result: Dict[str, Any]) -> Dict[str, Any]:
        """Phase 2: Generate project structure and code"""
        try:
            parsed_requirements = requirements_result['parsed_requirements']
            project_type = requirements_result['project_type']

            # Create project directory
            project_name = parsed_requirements.get('project_name', 'generated_project')
            project_path = self.projects_dir / project_name
            project_path.mkdir(exist_ok=True)

            # Generate code based on project type
            generated_files = []

            if project_type == 'web_app':
                generated_files = self._generate_web_app(project_path, parsed_requirements)
            elif project_type == 'api':
                generated_files = self._generate_api(project_path, parsed_requirements)
            elif project_type == 'cli_tool':
                generated_files = self._generate_cli_tool(project_path, parsed_requirements)
            else:
                # Default to simple Python script
                generated_files = self._generate_python_script(project_path, parsed_requirements)

            return {
                'project_path': str(project_path),
                'generated_files': generated_files,
                'project_type': project_type,
                'requirements': parsed_requirements
            }

        except Exception as e:
            self.logger.error(f"Error generating project code: {e}")
            raise Exception(f"Failed to generate project code: {e}")

    def _manage_dependencies(self, generation_result: Dict[str, Any]) -> Dict[str, Any]:
        """Phase 3: Manage project dependencies"""
        try:
            project_path = Path(generation_result['project_path'])
            project_type = generation_result['project_type']
            requirements = generation_result['requirements']

            # Analyze and install dependencies
            result = self.dependency_manager.analyze_and_install_dependencies(
                str(project_path), auto_install=True
            )

            dependencies = result.get('packages', [])
            installed_deps = [pkg.name for pkg in dependencies if result.get('installation_results', {}).get(pkg.name, False)]

            return {
                'dependencies_analyzed': dependencies,
                'dependencies_installed': installed_deps,
                'requirements_file_created': True
            }

        except Exception as e:
            self.logger.error(f"Error managing dependencies: {e}")
            return {
                'dependencies_analyzed': [],
                'dependencies_installed': [],
                'requirements_file_created': False,
                'error': str(e)
            }

    def _setup_database(self, requirements_result: Dict[str, Any], generation_result: Dict[str, Any]) -> Dict[str, Any]:
        """Phase 4: Setup database if needed"""
        try:
            parsed_requirements = requirements_result['parsed_requirements']
            project_path = Path(generation_result['project_path'])

            # Check if database is needed
            needs_database = self.db_analyzer.analyze_project_requirements(str(project_path))

            if not needs_database.entities and not needs_database.relationships:
                return {
                    'database_needed': False,
                    'database_type': None,
                    'schema_created': False,
                    'connection_configured': False
                }

            # Select appropriate database
            db_type = self.db_selector.select_database_type(needs_database)

            # Generate schema
            schema = self.schema_generator.generate_schema_from_requirements(
                needs_database, db_type
            )

            # Setup database connection
            connection_result = self.connection_manager.setup_database_connection(
                schema, {'database_type': db_type.value}
            )

            return {
                'database_needed': True,
                'database_type': db_type.value,
                'schema_created': True,
                'schema_files': [f"{schema.name}.sql"],
                'connection_configured': bool(connection_result),
                'connection_details': connection_result
            }

        except Exception as e:
            self.logger.error(f"Error setting up database: {e}")
            return {
                'database_needed': False,
                'database_type': None,
                'schema_created': False,
                'connection_configured': False,
                'error': str(e)
            }

    def _detect_and_resolve_errors(self, generation_result: Dict[str, Any]) -> Dict[str, Any]:
        """Phase 5: Detect and resolve errors"""
        try:
            project_path = Path(generation_result['project_path'])
            generated_files = generation_result['generated_files']

            errors_detected = []
            errors_fixed = []

            # Check each generated file for errors
            for file_path in generated_files:
                full_path = project_path / file_path
                if full_path.exists():
                    # Read file content
                    with open(full_path, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # Determine language and validate
                    language = self._get_language_from_file(file_path)
                    if language:
                        file_errors = self.error_monitor.check_code_for_errors(
                            content, language, str(full_path)
                        )
                        errors_detected.extend(file_errors)

                        # Try to fix errors
                        for error in file_errors:
                            try:
                                # Analyze root cause
                                root_cause = self.root_cause_analyzer.analyze_error(error)

                                # Generate fix suggestions
                                fixes = self.auto_fix_generator.generate_fix(error, root_cause, content)

                                # Apply the best fix if confidence is high
                                if fixes and fixes[0].confidence > 0.8:
                                    fix_result = self.auto_fix_generator.apply_fix(
                                        fixes[0], str(full_path), create_backup=True
                                    )
                                    if fix_result.success:
                                        errors_fixed.append(f"Fixed {error.error_type} in {file_path}")

                                        # Learn from the fix
                                        self.pattern_learner.learn_from_error(
                                            error, root_cause, True, fix_result.validation_passed
                                        )

                            except Exception as e:
                                self.logger.warning(f"Error fixing {error.error_type}: {e}")

            return {
                'errors_detected': [f"{e.error_type}: {e.message}" for e in errors_detected],
                'errors_fixed': errors_fixed,
                'total_errors': len(errors_detected),
                'total_fixed': len(errors_fixed)
            }

        except Exception as e:
            self.logger.error(f"Error in error detection and resolution: {e}")
            return {
                'errors_detected': [str(e)],
                'errors_fixed': [],
                'total_errors': 1,
                'total_fixed': 0
            }

    def _validate_project(self, generation_result: Dict[str, Any]) -> Dict[str, Any]:
        """Phase 6: Final project validation"""
        try:
            project_path = Path(generation_result['project_path'])
            generated_files = generation_result['generated_files']

            validation_results = {
                'files_validated': 0,
                'validation_passed': True,
                'issues_found': [],
                'recommendations': []
            }

            # Validate each generated file
            for file_path in generated_files:
                full_path = project_path / file_path
                if full_path.exists():
                    # Read file content
                    with open(full_path, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # Determine language and validate
                    language = self._get_language_from_file(file_path)
                    if language:
                        if language == 'python':
                            is_valid, issues, analysis = self.validator.validate_python_advanced(content, str(full_path))
                        elif language == 'javascript':
                            is_valid, issues, analysis = self.validator.validate_javascript_advanced(content, str(full_path))
                        elif language == 'html':
                            is_valid, issues, analysis = self.validator.validate_html_advanced(content, str(full_path))
                        else:
                            is_valid, issues = self.validator.validate_python(content)
                            analysis = {}

                        validation_results['files_validated'] += 1

                        if not is_valid:
                            validation_results['validation_passed'] = False

                        if issues:
                            validation_results['issues_found'].extend([f"{file_path}: {issue}" for issue in issues])

            # Add general recommendations
            if validation_results['validation_passed']:
                validation_results['recommendations'].extend([
                    "Consider adding unit tests",
                    "Set up continuous integration",
                    "Add documentation",
                    "Configure linting tools"
                ])

            return validation_results

        except Exception as e:
            self.logger.error(f"Error in project validation: {e}")
            return {
                'files_validated': 0,
                'validation_passed': False,
                'issues_found': [str(e)],
                'recommendations': []
            }

    def _compile_project_result(self, request_id: str, requirements_result: Dict[str, Any],
                              generation_result: Dict[str, Any], dependency_result: Dict[str, Any],
                              database_result: Dict[str, Any], error_resolution_result: Dict[str, Any],
                              validation_result: Dict[str, Any], execution_time: float) -> ProjectResult:
        """Compile all results into final project result"""

        # Generate explanation
        explanation = self._generate_project_explanation(
            requirements_result, generation_result, dependency_result,
            database_result, error_resolution_result, validation_result
        )

        # Generate next steps
        next_steps = self._generate_next_steps(
            generation_result, dependency_result, database_result,
            error_resolution_result, validation_result
        )

        return ProjectResult(
            success=validation_result['validation_passed'] and len(error_resolution_result['errors_detected']) == 0,
            project_path=generation_result['project_path'],
            generated_files=generation_result['generated_files'],
            dependencies_installed=dependency_result['dependencies_installed'],
            database_setup=database_result,
            errors_detected=error_resolution_result['errors_detected'],
            errors_fixed=error_resolution_result['errors_fixed'],
            execution_time=execution_time,
            explanation=explanation,
            next_steps=next_steps
        )

    def _generate_request_id(self) -> str:
        """Generate unique request ID"""
        import uuid
        return str(uuid.uuid4())

    def _get_language_from_file(self, file_path: str) -> Optional[str]:
        """Determine programming language from file extension"""
        extension = Path(file_path).suffix.lower()

        language_map = {
            '.py': 'python',
            '.js': 'javascript',
            '.ts': 'javascript',
            '.html': 'html',
            '.htm': 'html',
            '.css': 'css'
        }

        return language_map.get(extension)

    def _generate_project_explanation(self, requirements_result: Dict[str, Any],
                                    generation_result: Dict[str, Any], dependency_result: Dict[str, Any],
                                    database_result: Dict[str, Any], error_resolution_result: Dict[str, Any],
                                    validation_result: Dict[str, Any]) -> str:
        """Generate human-readable explanation of what was created"""

        project_type = generation_result['project_type']
        files_count = len(generation_result['generated_files'])
        deps_count = len(dependency_result['dependencies_installed'])
        errors_fixed = len(error_resolution_result['errors_fixed'])

        explanation = f"Successfully created a {project_type} project with {files_count} files. "

        if deps_count > 0:
            explanation += f"Installed {deps_count} dependencies automatically. "

        if database_result['database_needed']:
            explanation += f"Set up {database_result['database_type']} database with schema. "

        if errors_fixed > 0:
            explanation += f"Detected and fixed {errors_fixed} errors automatically. "

        if validation_result['validation_passed']:
            explanation += "All files passed validation checks."
        else:
            issues_count = len(validation_result['issues_found'])
            explanation += f"Found {issues_count} validation issues that may need attention."

        return explanation

    def _generate_next_steps(self, generation_result: Dict[str, Any], dependency_result: Dict[str, Any],
                           database_result: Dict[str, Any], error_resolution_result: Dict[str, Any],
                           validation_result: Dict[str, Any]) -> List[str]:
        """Generate recommended next steps for the user"""

        next_steps = []
        project_path = generation_result['project_path']

        # Basic next steps
        next_steps.append(f"Navigate to your project: cd {project_path}")

        # Project-specific steps
        project_type = generation_result['project_type']
        if project_type == 'web_app':
            next_steps.extend([
                "Run the application: python app.py",
                "Open your browser to http://localhost:5000"
            ])
        elif project_type == 'api':
            next_steps.extend([
                "Start the API server: python main.py",
                "Test the API endpoints using curl or Postman"
            ])
        else:
            next_steps.append("Run your application: python main.py")

        # Database steps
        if database_result['database_needed']:
            next_steps.append("Initialize the database: python init_db.py")

        # Error resolution steps
        if error_resolution_result['errors_detected']:
            remaining_errors = len(error_resolution_result['errors_detected']) - len(error_resolution_result['errors_fixed'])
            if remaining_errors > 0:
                next_steps.append(f"Review and fix {remaining_errors} remaining errors")

        # Validation steps
        if not validation_result['validation_passed']:
            next_steps.append("Address validation issues found during analysis")

        # General improvement steps
        next_steps.extend([
            "Add unit tests for your code",
            "Set up version control: git init",
            "Create a README.md file",
            "Consider adding error handling and logging"
        ])

        return next_steps

    def _generate_web_app(self, project_path: Path, requirements: Dict[str, Any]) -> List[str]:
        """Generate a web application"""
        generated_files = []

        # Create main application file
        app_content = '''from flask import Flask, render_template, request, jsonify
import os

app = Flask(__name__)

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/data')
def get_data():
    return jsonify({'message': 'Hello from your AI-generated app!'})

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
'''

        with open(project_path / 'app.py', 'w') as f:
            f.write(app_content)
        generated_files.append('app.py')

        # Create templates directory and HTML file
        templates_dir = project_path / 'templates'
        templates_dir.mkdir(exist_ok=True)

        html_content = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Generated Web App</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 800px; margin: 0 auto; }
        .header { text-align: center; color: #333; }
        .content { margin-top: 30px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="header">Welcome to Your AI-Generated Web App!</h1>
        <div class="content">
            <p>This application was automatically generated based on your requirements.</p>
            <button onclick="fetchData()">Test API</button>
            <div id="result"></div>
        </div>
    </div>

    <script>
        async function fetchData() {
            try {
                const response = await fetch('/api/data');
                const data = await response.json();
                document.getElementById('result').innerHTML = '<p>API Response: ' + data.message + '</p>';
            } catch (error) {
                document.getElementById('result').innerHTML = '<p>Error: ' + error.message + '</p>';
            }
        }
    </script>
</body>
</html>
'''

        with open(templates_dir / 'index.html', 'w') as f:
            f.write(html_content)
        generated_files.append('templates/index.html')

        # Create requirements.txt
        requirements_content = '''Flask==2.3.3
'''

        with open(project_path / 'requirements.txt', 'w') as f:
            f.write(requirements_content)
        generated_files.append('requirements.txt')

        return generated_files

    def _generate_api(self, project_path: Path, requirements: Dict[str, Any]) -> List[str]:
        """Generate an API application"""
        generated_files = []

        # Create main API file
        api_content = '''from flask import Flask, jsonify, request
from flask_cors import CORS

app = Flask(__name__)
CORS(app)

# Sample data
data_store = [
    {"id": 1, "name": "Item 1", "description": "First item"},
    {"id": 2, "name": "Item 2", "description": "Second item"}
]

@app.route('/api/items', methods=['GET'])
def get_items():
    return jsonify(data_store)

@app.route('/api/items/<int:item_id>', methods=['GET'])
def get_item(item_id):
    item = next((item for item in data_store if item['id'] == item_id), None)
    if item:
        return jsonify(item)
    return jsonify({'error': 'Item not found'}), 404

@app.route('/api/items', methods=['POST'])
def create_item():
    data = request.get_json()
    new_item = {
        'id': len(data_store) + 1,
        'name': data.get('name', ''),
        'description': data.get('description', '')
    }
    data_store.append(new_item)
    return jsonify(new_item), 201

@app.route('/health')
def health_check():
    return jsonify({'status': 'healthy'})

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
'''

        with open(project_path / 'main.py', 'w') as f:
            f.write(api_content)
        generated_files.append('main.py')

        # Create requirements.txt
        requirements_content = '''Flask==2.3.3
Flask-CORS==4.0.0
'''

        with open(project_path / 'requirements.txt', 'w') as f:
            f.write(requirements_content)
        generated_files.append('requirements.txt')

        return generated_files

    def _generate_cli_tool(self, project_path: Path, requirements: Dict[str, Any]) -> List[str]:
        """Generate a CLI tool application"""
        generated_files = []

        # Create main CLI file
        cli_content = '''#!/usr/bin/env python3
"""
AI-Generated CLI Tool
"""

import argparse
import sys
import json

def main():
    parser = argparse.ArgumentParser(description='AI-Generated CLI Tool')
    parser.add_argument('--version', action='version', version='1.0.0')
    parser.add_argument('--input', '-i', help='Input file or data')
    parser.add_argument('--output', '-o', help='Output file')
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')

    args = parser.parse_args()

    if args.verbose:
        print("Running in verbose mode...")

    # Sample functionality
    if args.input:
        print(f"Processing input: {args.input}")
        result = process_input(args.input)

        if args.output:
            with open(args.output, 'w') as f:
                json.dump(result, f, indent=2)
            print(f"Results saved to: {args.output}")
        else:
            print("Results:")
            print(json.dumps(result, indent=2))
    else:
        print("No input provided. Use --help for usage information.")

def process_input(input_data):
    """Process the input data"""
    return {
        "input": input_data,
        "processed": True,
        "message": "Successfully processed by AI-generated CLI tool"
    }

if __name__ == '__main__':
    main()
'''

        with open(project_path / 'main.py', 'w') as f:
            f.write(cli_content)
        generated_files.append('main.py')

        # Make it executable
        import os
        os.chmod(project_path / 'main.py', 0o755)

        return generated_files

    def _generate_python_script(self, project_path: Path, requirements: Dict[str, Any]) -> List[str]:
        """Generate a simple Python script"""
        generated_files = []

        # Create main script
        script_content = '''#!/usr/bin/env python3
"""
AI-Generated Python Script
"""

def main():
    """Main function"""
    print("Hello from your AI-generated Python script!")

    # Sample functionality
    data = {
        "message": "This script was automatically generated",
        "features": [
            "Automatic code generation",
            "Error detection and fixing",
            "Dependency management"
        ]
    }

    print("Script features:")
    for feature in data["features"]:
        print(f"- {feature}")

    # Example of simple processing
    numbers = [1, 2, 3, 4, 5]
    squared = [x**2 for x in numbers]
    print(f"Original numbers: {numbers}")
    print(f"Squared numbers: {squared}")

def helper_function(data):
    """Example helper function"""
    return f"Processed: {data}"

if __name__ == '__main__':
    main()
'''

        with open(project_path / 'main.py', 'w') as f:
            f.write(script_content)
        generated_files.append('main.py')

        return generated_files