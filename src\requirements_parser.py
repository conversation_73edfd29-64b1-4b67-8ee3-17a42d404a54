# AI Coding Agent - Requirements Parser
"""
Natural Language Processing module for understanding and parsing project requirements
"""

import re
import json
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from enum import Enum
import uuid
from datetime import datetime

from src.logger import get_logger
from src.models import LLMManager
from src.conversation import ConversationManager, Message
from src.exceptions import RequirementParsingError


class ProjectType(Enum):
    """Enumeration of supported project types"""
    WEB_APP = "web_app"
    API = "api"
    DESKTOP_APP = "desktop_app"
    MOBILE_APP = "mobile_app"
    CLI_TOOL = "cli_tool"
    LIBRARY = "library"
    GAME = "game"
    DATA_ANALYSIS = "data_analysis"
    MACHINE_LEARNING = "machine_learning"
    UNKNOWN = "unknown"


class TechnicalLevel(Enum):
    """User's technical expertise level"""
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    EXPERT = "expert"


@dataclass
class ProjectRequirement:
    """Represents a parsed project requirement"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    title: str = ""
    description: str = ""
    project_type: ProjectType = ProjectType.UNKNOWN
    features: List[str] = field(default_factory=list)
    technologies: List[str] = field(default_factory=list)
    constraints: List[str] = field(default_factory=list)
    user_stories: List[str] = field(default_factory=list)
    technical_requirements: Dict[str, Any] = field(default_factory=dict)
    estimated_complexity: str = "medium"  # low, medium, high
    confidence_score: float = 0.0  # 0.0 to 1.0
    clarifications_needed: List[str] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'project_type': self.project_type.value,
            'features': self.features,
            'technologies': self.technologies,
            'constraints': self.constraints,
            'user_stories': self.user_stories,
            'technical_requirements': self.technical_requirements,
            'estimated_complexity': self.estimated_complexity,
            'confidence_score': self.confidence_score,
            'clarifications_needed': self.clarifications_needed,
            'created_at': self.created_at.isoformat()
        }


@dataclass
class UserProfile:
    """Represents user's technical profile and preferences"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    technical_level: TechnicalLevel = TechnicalLevel.INTERMEDIATE
    preferred_technologies: List[str] = field(default_factory=list)
    communication_style: str = "friendly"  # friendly, formal, technical
    previous_projects: List[str] = field(default_factory=list)
    learning_goals: List[str] = field(default_factory=list)
    interaction_history: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)


class RequirementParser:
    """
    Advanced natural language processing system for understanding project requirements
    with conversational AI capabilities and context awareness
    """
    
    def __init__(self, llm_manager: Optional[LLMManager] = None, 
                 conversation_manager: Optional[ConversationManager] = None):
        """Initialize the requirement parser"""
        self.logger = get_logger(__name__)
        self.llm_manager = llm_manager or LLMManager()
        self.conversation_manager = conversation_manager or ConversationManager()
        
        # User profiles for personalization
        self.user_profiles: Dict[str, UserProfile] = {}
        
        # Pattern recognition for common project types
        self.project_patterns = self._initialize_project_patterns()
        
        # Technology keywords for detection
        self.tech_keywords = self._initialize_tech_keywords()
        
        self.logger.info("RequirementParser initialized with conversational AI capabilities")
    
    def parse_requirements(self, user_input: str, user_id: str = "default", 
                          conversation_id: Optional[str] = None) -> ProjectRequirement:
        """
        Parse natural language input into structured project requirements
        
        Args:
            user_input: Natural language description of the project
            user_id: Unique identifier for the user
            conversation_id: Optional conversation context
            
        Returns:
            ProjectRequirement object with parsed information
            
        Raises:
            RequirementParsingError: If parsing fails
        """
        try:
            self.logger.info(f"Parsing requirements for user {user_id}")
            
            # Get or create user profile
            user_profile = self._get_or_create_user_profile(user_id)
            
            # Get conversation context if available
            context = self._get_conversation_context(conversation_id) if conversation_id else ""
            
            # Initial analysis using pattern matching
            initial_analysis = self._analyze_input_patterns(user_input)
            
            # Enhanced analysis using LLM with context
            llm_analysis = self._analyze_with_llm(user_input, context, user_profile)
            
            # Merge analyses to create comprehensive requirements
            requirements = self._merge_analyses(initial_analysis, llm_analysis, user_input)
            
            # Identify clarifications needed
            requirements.clarifications_needed = self._identify_clarifications(requirements, user_profile)
            
            # Calculate confidence score
            requirements.confidence_score = self._calculate_confidence_score(requirements)
            
            # Update user profile based on interaction
            self._update_user_profile(user_profile, requirements)
            
            self.logger.info(f"Successfully parsed requirements with confidence: {requirements.confidence_score:.2f}")
            return requirements
            
        except Exception as e:
            self.logger.error(f"Failed to parse requirements: {str(e)}")
            raise RequirementParsingError(f"Failed to parse requirements: {str(e)}")

    def _initialize_project_patterns(self) -> Dict[ProjectType, List[str]]:
        """Initialize regex patterns for project type detection"""
        return {
            ProjectType.WEB_APP: [
                r'\b(web\s*app|website|web\s*application|web\s*portal|web\s*platform)\b',
                r'\b(html|css|javascript|react|vue|angular|django|flask|express)\b',
                r'\b(frontend|backend|full\s*stack|responsive|mobile\s*friendly)\b'
            ],
            ProjectType.API: [
                r'\b(api|rest|restful|graphql|endpoint|microservice)\b',
                r'\b(json|xml|http|https|webhook|integration)\b',
                r'\b(server|backend|service|middleware)\b'
            ],
            ProjectType.DESKTOP_APP: [
                r'\b(desktop|gui|window|application|software)\b',
                r'\b(tkinter|pyqt|electron|wpf|winforms|javafx)\b',
                r'\b(cross\s*platform|windows|mac|linux)\b'
            ],
            ProjectType.MOBILE_APP: [
                r'\b(mobile|app|android|ios|smartphone|tablet)\b',
                r'\b(react\s*native|flutter|ionic|xamarin|swift|kotlin)\b',
                r'\b(touch|gesture|notification|gps|camera)\b'
            ],
            ProjectType.CLI_TOOL: [
                r'\b(command\s*line|cli|terminal|console|script)\b',
                r'\b(automation|batch|shell|bash|powershell)\b',
                r'\b(tool|utility|helper|processor)\b'
            ],
            ProjectType.GAME: [
                r'\b(game|gaming|player|level|score|character)\b',
                r'\b(unity|unreal|pygame|graphics|animation)\b',
                r'\b(2d|3d|multiplayer|single\s*player)\b'
            ],
            ProjectType.DATA_ANALYSIS: [
                r'\b(data|analysis|analytics|visualization|chart|graph)\b',
                r'\b(pandas|numpy|matplotlib|plotly|jupyter|notebook)\b',
                r'\b(csv|excel|database|sql|statistics)\b'
            ],
            ProjectType.MACHINE_LEARNING: [
                r'\b(machine\s*learning|ml|ai|artificial\s*intelligence|neural\s*network)\b',
                r'\b(tensorflow|pytorch|scikit|model|training|prediction)\b',
                r'\b(classification|regression|clustering|deep\s*learning)\b'
            ]
        }

    def _initialize_tech_keywords(self) -> Dict[str, List[str]]:
        """Initialize technology keyword mappings"""
        return {
            'frontend': ['html', 'css', 'javascript', 'react', 'vue', 'angular', 'svelte', 'typescript'],
            'backend': ['python', 'flask', 'django', 'fastapi', 'node.js', 'express', 'java', 'spring'],
            'database': ['sqlite', 'postgresql', 'mysql', 'mongodb', 'redis', 'elasticsearch'],
            'mobile': ['react native', 'flutter', 'ionic', 'xamarin', 'swift', 'kotlin', 'java'],
            'desktop': ['electron', 'tkinter', 'pyqt', 'wpf', 'javafx', 'qt'],
            'data': ['pandas', 'numpy', 'matplotlib', 'plotly', 'jupyter', 'scipy'],
            'ml': ['tensorflow', 'pytorch', 'scikit-learn', 'keras', 'opencv'],
            'devops': ['docker', 'kubernetes', 'aws', 'azure', 'gcp', 'jenkins', 'github actions']
        }

    def _get_or_create_user_profile(self, user_id: str) -> UserProfile:
        """Get existing user profile or create a new one"""
        if user_id not in self.user_profiles:
            self.user_profiles[user_id] = UserProfile()
            self.logger.info(f"Created new user profile for {user_id}")
        return self.user_profiles[user_id]

    def _get_conversation_context(self, conversation_id: str) -> str:
        """Get conversation context for better understanding"""
        try:
            conversation = self.conversation_manager.get_conversation(conversation_id)
            if conversation:
                # Get recent messages for context
                recent_messages = conversation.get_context_messages(max_messages=5)
                context_parts = []
                for msg in recent_messages:
                    context_parts.append(f"{msg.role}: {msg.content[:200]}...")
                return "\n".join(context_parts)
        except Exception as e:
            self.logger.warning(f"Failed to get conversation context: {e}")
        return ""

    def _analyze_input_patterns(self, user_input: str) -> Dict[str, Any]:
        """Analyze input using pattern matching for quick insights"""
        analysis = {
            'project_type': ProjectType.UNKNOWN,
            'detected_technologies': [],
            'features': [],
            'complexity_indicators': []
        }

        input_lower = user_input.lower()

        # Detect project type
        max_matches = 0
        detected_type = ProjectType.UNKNOWN

        for project_type, patterns in self.project_patterns.items():
            matches = 0
            for pattern in patterns:
                if re.search(pattern, input_lower, re.IGNORECASE):
                    matches += 1

            if matches > max_matches:
                max_matches = matches
                detected_type = project_type

        analysis['project_type'] = detected_type

        # Detect technologies
        for category, keywords in self.tech_keywords.items():
            for keyword in keywords:
                if keyword.lower() in input_lower:
                    analysis['detected_technologies'].append(keyword)

        # Extract potential features (simple keyword extraction)
        feature_keywords = ['login', 'register', 'search', 'filter', 'sort', 'upload', 'download',
                           'share', 'comment', 'like', 'follow', 'notification', 'dashboard',
                           'admin', 'user management', 'payment', 'cart', 'checkout']

        for keyword in feature_keywords:
            if keyword in input_lower:
                analysis['features'].append(keyword)

        # Detect complexity indicators
        complexity_keywords = {
            'high': ['complex', 'advanced', 'enterprise', 'scalable', 'distributed', 'microservice'],
            'medium': ['moderate', 'standard', 'typical', 'normal'],
            'low': ['simple', 'basic', 'minimal', 'lightweight', 'quick', 'prototype']
        }

        for level, keywords in complexity_keywords.items():
            for keyword in keywords:
                if keyword in input_lower:
                    analysis['complexity_indicators'].append(level)

        return analysis

    def _analyze_with_llm(self, user_input: str, context: str, user_profile: UserProfile) -> Dict[str, Any]:
        """Use LLM for sophisticated requirement analysis"""
        try:
            # Build context-aware prompt for requirement analysis
            prompt = self._build_analysis_prompt(user_input, context, user_profile)

            # Use assistant model for general analysis
            response = self.llm_manager.generate_response_with_context(
                prompt=prompt,
                context=context,
                role="assistant"
            )

            # Parse LLM response into structured data
            return self._parse_llm_analysis(response)

        except Exception as e:
            self.logger.warning(f"LLM analysis failed, using fallback: {e}")
            return {
                'project_type': 'unknown',
                'features': [],
                'technologies': [],
                'user_stories': [],
                'technical_requirements': {},
                'estimated_complexity': 'medium'
            }

    def _build_analysis_prompt(self, user_input: str, context: str, user_profile: UserProfile) -> str:
        """Build sophisticated prompt for LLM analysis"""
        prompt = f"""You are an expert software architect and project analyst. Analyze the following project request and provide a detailed breakdown.

User's Technical Level: {user_profile.technical_level.value}
Communication Style: {user_profile.communication_style}
Previous Context: {context[:500] if context else "None"}

Project Request: "{user_input}"

Please analyze this request and provide a JSON response with the following structure:
{{
    "project_type": "web_app|api|desktop_app|mobile_app|cli_tool|library|game|data_analysis|machine_learning|unknown",
    "title": "A clear, concise project title",
    "description": "A detailed project description",
    "features": ["list", "of", "key", "features"],
    "technologies": ["suggested", "technologies", "and", "frameworks"],
    "user_stories": ["As a user, I want...", "As an admin, I need..."],
    "technical_requirements": {{
        "database": "required database type or none",
        "authentication": "required auth method or none",
        "deployment": "deployment requirements",
        "performance": "performance considerations",
        "security": "security requirements"
    }},
    "estimated_complexity": "low|medium|high",
    "constraints": ["any", "mentioned", "constraints", "or", "limitations"]
}}

Focus on understanding the user's intent and providing practical, actionable insights. Consider their technical level when suggesting technologies."""

        return prompt

    def _parse_llm_analysis(self, llm_response: str) -> Dict[str, Any]:
        """Parse LLM response into structured data"""
        try:
            # Try to extract JSON from the response
            json_match = re.search(r'\{.*\}', llm_response, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
                return json.loads(json_str)
            else:
                # Fallback: parse key information manually
                return self._manual_parse_response(llm_response)

        except json.JSONDecodeError as e:
            self.logger.warning(f"Failed to parse JSON from LLM response: {e}")
            return self._manual_parse_response(llm_response)

    def _manual_parse_response(self, response: str) -> Dict[str, Any]:
        """Manually parse LLM response when JSON parsing fails"""
        # Basic fallback parsing
        return {
            'project_type': 'unknown',
            'title': 'Parsed Project',
            'description': response[:200] + "..." if len(response) > 200 else response,
            'features': [],
            'technologies': [],
            'user_stories': [],
            'technical_requirements': {},
            'estimated_complexity': 'medium',
            'constraints': []
        }

    def _merge_analyses(self, pattern_analysis: Dict[str, Any],
                       llm_analysis: Dict[str, Any], user_input: str) -> ProjectRequirement:
        """Merge pattern matching and LLM analyses into comprehensive requirements"""

        # Determine project type (prefer LLM if confident, otherwise use pattern matching)
        project_type_str = llm_analysis.get('project_type', 'unknown')
        try:
            project_type = ProjectType(project_type_str)
        except ValueError:
            project_type = pattern_analysis.get('project_type', ProjectType.UNKNOWN)

        # Merge technologies
        technologies = list(set(
            pattern_analysis.get('detected_technologies', []) +
            llm_analysis.get('technologies', [])
        ))

        # Merge features
        features = list(set(
            pattern_analysis.get('features', []) +
            llm_analysis.get('features', [])
        ))

        # Create comprehensive requirements object
        requirements = ProjectRequirement(
            title=llm_analysis.get('title', 'New Project'),
            description=llm_analysis.get('description', user_input),
            project_type=project_type,
            features=features,
            technologies=technologies,
            constraints=llm_analysis.get('constraints', []),
            user_stories=llm_analysis.get('user_stories', []),
            technical_requirements=llm_analysis.get('technical_requirements', {}),
            estimated_complexity=llm_analysis.get('estimated_complexity', 'medium')
        )

        return requirements

    def _identify_clarifications(self, requirements: ProjectRequirement,
                               user_profile: UserProfile) -> List[str]:
        """Identify what clarifications are needed based on requirements and user profile"""
        clarifications = []

        # Check for missing critical information
        if not requirements.features:
            clarifications.append("What specific features would you like in your application?")

        if requirements.project_type == ProjectType.UNKNOWN:
            clarifications.append("What type of application are you looking to build?")

        if not requirements.technologies and user_profile.technical_level != TechnicalLevel.BEGINNER:
            clarifications.append("Do you have any preferred technologies or frameworks?")

        # Technical level specific clarifications
        if user_profile.technical_level == TechnicalLevel.BEGINNER:
            if not requirements.technical_requirements.get('deployment'):
                clarifications.append("How would you like to share or deploy your application?")

        if user_profile.technical_level in [TechnicalLevel.ADVANCED, TechnicalLevel.EXPERT]:
            if not requirements.technical_requirements.get('performance'):
                clarifications.append("Are there any specific performance requirements or constraints?")
            if not requirements.technical_requirements.get('security'):
                clarifications.append("What security considerations should we keep in mind?")

        # Project type specific clarifications
        if requirements.project_type == ProjectType.WEB_APP:
            if not any('user' in story.lower() for story in requirements.user_stories):
                clarifications.append("Who will be using this web application and what are their main goals?")

        return clarifications[:3]  # Limit to 3 most important clarifications

    def _calculate_confidence_score(self, requirements: ProjectRequirement) -> float:
        """Calculate confidence score based on completeness and clarity of requirements"""
        score = 0.0
        max_score = 10.0

        # Project type identified
        if requirements.project_type != ProjectType.UNKNOWN:
            score += 2.0

        # Has title and description
        if requirements.title and len(requirements.title) > 3:
            score += 1.0
        if requirements.description and len(requirements.description) > 10:
            score += 1.0

        # Has features
        if requirements.features:
            score += min(len(requirements.features) * 0.5, 2.0)

        # Has technologies
        if requirements.technologies:
            score += min(len(requirements.technologies) * 0.3, 1.5)

        # Has user stories
        if requirements.user_stories:
            score += min(len(requirements.user_stories) * 0.4, 1.5)

        # Has technical requirements
        if requirements.technical_requirements:
            score += min(len(requirements.technical_requirements) * 0.2, 1.0)

        return min(score / max_score, 1.0)

    def _update_user_profile(self, user_profile: UserProfile, requirements: ProjectRequirement):
        """Update user profile based on current interaction"""
        user_profile.updated_at = datetime.now()

        # Add technologies to preferred list if not already there
        for tech in requirements.technologies:
            if tech not in user_profile.preferred_technologies:
                user_profile.preferred_technologies.append(tech)

        # Add project to history
        project_summary = f"{requirements.project_type.value}: {requirements.title}"
        if project_summary not in user_profile.previous_projects:
            user_profile.previous_projects.append(project_summary)

        # Update interaction history
        interaction_key = datetime.now().strftime("%Y-%m-%d")
        if interaction_key not in user_profile.interaction_history:
            user_profile.interaction_history[interaction_key] = []

        user_profile.interaction_history[interaction_key].append({
            'project_type': requirements.project_type.value,
            'complexity': requirements.estimated_complexity,
            'confidence': requirements.confidence_score
        })

        self.logger.debug(f"Updated user profile for interaction on {interaction_key}")

    def get_user_profile(self, user_id: str) -> Optional[UserProfile]:
        """Get user profile by ID"""
        return self.user_profiles.get(user_id)

    def update_user_technical_level(self, user_id: str, technical_level: TechnicalLevel):
        """Update user's technical level"""
        if user_id in self.user_profiles:
            self.user_profiles[user_id].technical_level = technical_level
            self.user_profiles[user_id].updated_at = datetime.now()
            self.logger.info(f"Updated technical level for user {user_id} to {technical_level.value}")

    def get_project_suggestions(self, user_profile: UserProfile) -> List[str]:
        """Get project suggestions based on user profile and history"""
        suggestions = []

        # Based on technical level
        if user_profile.technical_level == TechnicalLevel.BEGINNER:
            suggestions.extend([
                "A simple todo list web application",
                "A personal portfolio website",
                "A basic calculator application",
                "A simple blog with posts and comments"
            ])
        elif user_profile.technical_level == TechnicalLevel.INTERMEDIATE:
            suggestions.extend([
                "A full-stack e-commerce application",
                "A social media dashboard",
                "A project management tool",
                "A weather application with API integration"
            ])
        elif user_profile.technical_level in [TechnicalLevel.ADVANCED, TechnicalLevel.EXPERT]:
            suggestions.extend([
                "A microservices-based application",
                "A real-time chat application with WebSockets",
                "A machine learning model deployment platform",
                "A distributed system with load balancing"
            ])

        # Based on preferred technologies
        if 'react' in user_profile.preferred_technologies:
            suggestions.append("A React-based single page application")
        if 'python' in user_profile.preferred_technologies:
            suggestions.append("A Python-based data analysis tool")
        if 'mobile' in [tech.lower() for tech in user_profile.preferred_technologies]:
            suggestions.append("A cross-platform mobile application")

        return suggestions[:5]  # Return top 5 suggestions
