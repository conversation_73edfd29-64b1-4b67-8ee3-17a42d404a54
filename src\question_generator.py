# AI Coding Agent - Dynamic Question Generator
"""
Intelligent question generation system for clarifying project requirements
"""

import random
from typing import List, Dict, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum

from .logger import get_logger
from .models import LLMManager
from .requirements_parser import ProjectRequirement, UserProfile, ProjectType, TechnicalLevel


class QuestionType(Enum):
    """Types of clarifying questions"""
    FEATURE_CLARIFICATION = "feature_clarification"
    TECHNICAL_PREFERENCE = "technical_preference"
    USER_EXPERIENCE = "user_experience"
    SCOPE_DEFINITION = "scope_definition"
    CONSTRAINT_IDENTIFICATION = "constraint_identification"
    PRIORITY_RANKING = "priority_ranking"


@dataclass
class Question:
    """Represents a dynamically generated question"""
    id: str
    text: str
    question_type: QuestionType
    context: str
    follow_up_hints: List[str]
    technical_level: TechnicalLevel
    priority: int  # 1-10, higher is more important
    metadata: Dict[str, Any]


class QuestionGenerator:
    """
    Intelligent question generation system that creates contextual, 
    non-scripted clarifying questions based on project requirements
    """
    
    def __init__(self, llm_manager: Optional[LLMManager] = None):
        """Initialize the question generator"""
        self.logger = get_logger(__name__)
        self.llm_manager = llm_manager or LLMManager()
        
        # Question templates organized by type and technical level
        self.question_frameworks = self._initialize_question_frameworks()
        
        # Context-aware question patterns
        self.context_patterns = self._initialize_context_patterns()
        
        self.logger.info("QuestionGenerator initialized with dynamic question capabilities")
    
    def generate_questions(self, requirements: ProjectRequirement, 
                          user_profile: UserProfile,
                          conversation_context: str = "",
                          max_questions: int = 3) -> List[Question]:
        """
        Generate intelligent, contextual questions based on requirements and user profile
        
        Args:
            requirements: Parsed project requirements
            user_profile: User's technical profile and preferences
            conversation_context: Previous conversation context
            max_questions: Maximum number of questions to generate
            
        Returns:
            List of dynamically generated questions
        """
        try:
            self.logger.info(f"Generating questions for {requirements.project_type.value} project")
            
            # Analyze what information is missing or unclear
            missing_info = self._analyze_missing_information(requirements)
            
            # Generate questions using multiple strategies
            questions = []
            
            # Strategy 1: LLM-generated contextual questions
            llm_questions = self._generate_llm_questions(requirements, user_profile, conversation_context)
            questions.extend(llm_questions)
            
            # Strategy 2: Pattern-based intelligent questions
            pattern_questions = self._generate_pattern_questions(requirements, user_profile, missing_info)
            questions.extend(pattern_questions)
            
            # Strategy 3: Technical level specific questions
            level_questions = self._generate_level_specific_questions(requirements, user_profile)
            questions.extend(level_questions)
            
            # Prioritize and filter questions
            prioritized_questions = self._prioritize_questions(questions, requirements, user_profile)
            
            # Remove duplicates and select best questions
            final_questions = self._select_best_questions(prioritized_questions, max_questions)
            
            self.logger.info(f"Generated {len(final_questions)} contextual questions")
            return final_questions
            
        except Exception as e:
            self.logger.error(f"Failed to generate questions: {str(e)}")
            # Fallback to basic questions
            return self._generate_fallback_questions(requirements, user_profile, max_questions)
    
    def _analyze_missing_information(self, requirements: ProjectRequirement) -> Dict[str, bool]:
        """Analyze what critical information is missing from requirements"""
        missing = {
            'features': len(requirements.features) == 0,
            'technologies': len(requirements.technologies) == 0,
            'user_stories': len(requirements.user_stories) == 0,
            'technical_requirements': len(requirements.technical_requirements) == 0,
            'constraints': len(requirements.constraints) == 0,
            'target_users': not any('user' in story.lower() for story in requirements.user_stories),
            'deployment': 'deployment' not in requirements.technical_requirements,
            'authentication': 'authentication' not in requirements.technical_requirements,
            'database': 'database' not in requirements.technical_requirements,
            'performance': 'performance' not in requirements.technical_requirements
        }
        
        return missing
    
    def _generate_llm_questions(self, requirements: ProjectRequirement, 
                               user_profile: UserProfile, 
                               conversation_context: str) -> List[Question]:
        """Generate questions using LLM for maximum contextuality"""
        try:
            prompt = self._build_question_generation_prompt(requirements, user_profile, conversation_context)
            
            response = self.llm_manager.generate_response_with_context(
                prompt=prompt,
                context=conversation_context,
                role="assistant"
            )
            
            return self._parse_llm_questions(response, user_profile.technical_level)
            
        except Exception as e:
            self.logger.warning(f"LLM question generation failed: {e}")
            return []
    
    def _build_question_generation_prompt(self, requirements: ProjectRequirement,
                                        user_profile: UserProfile,
                                        conversation_context: str) -> str:
        """Build sophisticated prompt for LLM question generation"""
        
        prompt = f"""You are an expert software consultant conducting a requirements gathering session. 
Based on the project information below, generate 2-3 intelligent, conversational clarifying questions.

Project Details:
- Type: {requirements.project_type.value}
- Title: {requirements.title}
- Description: {requirements.description}
- Features mentioned: {', '.join(requirements.features) if requirements.features else 'None specified'}
- Technologies mentioned: {', '.join(requirements.technologies) if requirements.technologies else 'None specified'}
- Confidence level: {requirements.confidence_score:.2f}

User Profile:
- Technical Level: {user_profile.technical_level.value}
- Communication Style: {user_profile.communication_style}
- Previous Projects: {', '.join(user_profile.previous_projects[-3:]) if user_profile.previous_projects else 'None'}

Conversation Context: {conversation_context[:300] if conversation_context else 'Initial conversation'}

Generate questions that are:
1. Natural and conversational (not robotic or templated)
2. Specific to this project and user
3. Help clarify the most important missing information
4. Appropriate for the user's technical level
5. Build on the conversation context

Format each question as:
Q: [Natural, conversational question]
Type: [feature_clarification|technical_preference|user_experience|scope_definition|constraint_identification]
Priority: [1-10]
Hint: [Brief follow-up hint or example]

Example good questions:
- "I love the idea! Quick question - who do you imagine using this most? That'll help me suggest the right features."
- "Sounds exciting! Are you thinking more like a simple tool for personal use, or something that could handle multiple users?"
- "Great concept! What's the main problem this will solve for people?"

Generate 2-3 questions now:"""
        
        return prompt
    
    def _parse_llm_questions(self, llm_response: str, technical_level: TechnicalLevel) -> List[Question]:
        """Parse LLM response into Question objects"""
        questions = []
        
        try:
            # Split response into question blocks
            blocks = llm_response.split('Q:')[1:]  # Skip first empty element
            
            for i, block in enumerate(blocks):
                lines = block.strip().split('\n')
                if not lines:
                    continue
                
                question_text = lines[0].strip()
                question_type = QuestionType.FEATURE_CLARIFICATION  # Default
                priority = 5  # Default
                hint = ""
                
                # Parse metadata from subsequent lines
                for line in lines[1:]:
                    line = line.strip()
                    if line.startswith('Type:'):
                        try:
                            type_str = line.split(':', 1)[1].strip()
                            question_type = QuestionType(type_str)
                        except (ValueError, IndexError):
                            pass
                    elif line.startswith('Priority:'):
                        try:
                            priority = int(line.split(':', 1)[1].strip())
                        except (ValueError, IndexError):
                            pass
                    elif line.startswith('Hint:'):
                        try:
                            hint = line.split(':', 1)[1].strip()
                        except IndexError:
                            pass
                
                if question_text:
                    question = Question(
                        id=f"llm_q_{i}",
                        text=question_text,
                        question_type=question_type,
                        context="LLM generated",
                        follow_up_hints=[hint] if hint else [],
                        technical_level=technical_level,
                        priority=priority,
                        metadata={"source": "llm", "block_index": i}
                    )
                    questions.append(question)
            
        except Exception as e:
            self.logger.warning(f"Failed to parse LLM questions: {e}")
        
        return questions
    
    def _generate_pattern_questions(self, requirements: ProjectRequirement,
                                  user_profile: UserProfile,
                                  missing_info: Dict[str, bool]) -> List[Question]:
        """Generate questions using intelligent pattern matching"""
        questions = []
        
        # Project type specific questions
        if requirements.project_type == ProjectType.WEB_APP:
            if missing_info['target_users']:
                questions.append(self._create_question(
                    "Who will be the main users of your web application? Understanding your audience helps me suggest the right features and design approach.",
                    QuestionType.USER_EXPERIENCE,
                    user_profile.technical_level,
                    priority=8,
                    hints=["Individual users", "Business teams", "Public audience", "Specific industry"]
                ))
            
            if missing_info['authentication']:
                questions.append(self._create_question(
                    "Will users need to create accounts and log in, or should it work without registration?",
                    QuestionType.TECHNICAL_PREFERENCE,
                    user_profile.technical_level,
                    priority=7,
                    hints=["User accounts with login", "Guest access only", "Social media login", "Not sure yet"]
                ))
        
        elif requirements.project_type == ProjectType.API:
            if missing_info['technical_requirements']:
                questions.append(self._create_question(
                    "What kind of data will your API work with, and how do you expect other applications to use it?",
                    QuestionType.SCOPE_DEFINITION,
                    user_profile.technical_level,
                    priority=9,
                    hints=["User data", "Product information", "Real-time updates", "File processing"]
                ))
        
        # Technical level specific questions
        if user_profile.technical_level == TechnicalLevel.BEGINNER:
            if missing_info['deployment']:
                questions.append(self._create_question(
                    "How would you like to share your finished project with others?",
                    QuestionType.CONSTRAINT_IDENTIFICATION,
                    user_profile.technical_level,
                    priority=6,
                    hints=["Just on my computer", "Share with friends", "Put it online", "Not sure yet"]
                ))
        
        elif user_profile.technical_level in [TechnicalLevel.ADVANCED, TechnicalLevel.EXPERT]:
            if missing_info['performance']:
                questions.append(self._create_question(
                    "Are there any specific performance requirements or scalability considerations I should keep in mind?",
                    QuestionType.CONSTRAINT_IDENTIFICATION,
                    user_profile.technical_level,
                    priority=7,
                    hints=["High traffic expected", "Real-time processing", "Large datasets", "Standard performance"]
                ))
        
        return questions

    def _create_question(self, text: str, question_type: QuestionType,
                        technical_level: TechnicalLevel, priority: int = 5,
                        hints: Optional[List[str]] = None) -> Question:
        """Helper method to create a Question object"""
        return Question(
            id=f"pattern_{hash(text) % 10000}",
            text=text,
            question_type=question_type,
            context="Pattern generated",
            follow_up_hints=hints or [],
            technical_level=technical_level,
            priority=priority,
            metadata={"source": "pattern"}
        )

    def _generate_level_specific_questions(self, requirements: ProjectRequirement,
                                         user_profile: UserProfile) -> List[Question]:
        """Generate questions specific to user's technical level"""
        questions = []

        if user_profile.technical_level == TechnicalLevel.BEGINNER:
            # Focus on simple, encouraging questions
            if not requirements.features:
                questions.append(self._create_question(
                    "What would you like people to be able to do with your application? Even simple ideas are great starting points!",
                    QuestionType.FEATURE_CLARIFICATION,
                    user_profile.technical_level,
                    priority=9,
                    hints=["Add/view items", "Search for things", "Save information", "Share with others"]
                ))

        elif user_profile.technical_level == TechnicalLevel.INTERMEDIATE:
            # Balance technical and functional questions
            if not requirements.technologies and requirements.project_type != ProjectType.UNKNOWN:
                questions.append(self._create_question(
                    "Do you have any preferred technologies or frameworks you'd like to use, or should I suggest the best options?",
                    QuestionType.TECHNICAL_PREFERENCE,
                    user_profile.technical_level,
                    priority=6,
                    hints=["I have preferences", "Suggest the best", "Use what I know", "Learn something new"]
                ))

        elif user_profile.technical_level in [TechnicalLevel.ADVANCED, TechnicalLevel.EXPERT]:
            # Focus on architecture and advanced considerations
            if not requirements.technical_requirements:
                questions.append(self._create_question(
                    "What are your thoughts on the technical architecture? Any specific patterns, databases, or deployment strategies you prefer?",
                    QuestionType.TECHNICAL_PREFERENCE,
                    user_profile.technical_level,
                    priority=8,
                    hints=["Microservices", "Monolithic", "Serverless", "Container-based"]
                ))

        return questions

    def _prioritize_questions(self, questions: List[Question],
                            requirements: ProjectRequirement,
                            user_profile: UserProfile) -> List[Question]:
        """Prioritize questions based on context and importance"""

        for question in questions:
            # Boost priority for questions that address critical missing info
            if question.question_type == QuestionType.FEATURE_CLARIFICATION and not requirements.features:
                question.priority += 3

            if question.question_type == QuestionType.USER_EXPERIENCE and not requirements.user_stories:
                question.priority += 2

            # Adjust for user's technical level
            if question.technical_level == user_profile.technical_level:
                question.priority += 1

            # Boost priority for project-type specific questions
            if requirements.project_type != ProjectType.UNKNOWN:
                if (requirements.project_type == ProjectType.WEB_APP and
                    question.question_type == QuestionType.USER_EXPERIENCE):
                    question.priority += 2
                elif (requirements.project_type == ProjectType.API and
                      question.question_type == QuestionType.TECHNICAL_PREFERENCE):
                    question.priority += 2

        # Sort by priority (highest first)
        return sorted(questions, key=lambda q: q.priority, reverse=True)

    def _select_best_questions(self, questions: List[Question], max_questions: int) -> List[Question]:
        """Select the best questions, avoiding duplicates and ensuring variety"""
        if not questions:
            return []

        selected = []
        used_types = set()

        # First pass: select highest priority questions of different types
        for question in questions:
            if len(selected) >= max_questions:
                break

            if question.question_type not in used_types:
                selected.append(question)
                used_types.add(question.question_type)

        # Second pass: fill remaining slots with highest priority questions
        for question in questions:
            if len(selected) >= max_questions:
                break

            if question not in selected:
                # Check for similar questions to avoid duplicates
                if not self._is_similar_question(question, selected):
                    selected.append(question)

        return selected[:max_questions]

    def _is_similar_question(self, question: Question, existing_questions: List[Question]) -> bool:
        """Check if a question is too similar to existing ones"""
        question_words = set(question.text.lower().split())

        for existing in existing_questions:
            existing_words = set(existing.text.lower().split())

            # Calculate word overlap
            overlap = len(question_words.intersection(existing_words))
            total_words = len(question_words.union(existing_words))

            if total_words > 0 and overlap / total_words > 0.4:  # 40% similarity threshold
                return True

        return False

    def _generate_fallback_questions(self, requirements: ProjectRequirement,
                                   user_profile: UserProfile,
                                   max_questions: int) -> List[Question]:
        """Generate basic fallback questions when other methods fail"""
        fallback_questions = [
            self._create_question(
                "What's the main goal you want to achieve with this project?",
                QuestionType.SCOPE_DEFINITION,
                user_profile.technical_level,
                priority=8,
                hints=["Solve a problem", "Learn new skills", "Build a tool", "Create something fun"]
            ),
            self._create_question(
                "Who do you imagine using this application?",
                QuestionType.USER_EXPERIENCE,
                user_profile.technical_level,
                priority=7,
                hints=["Just me", "My team", "Friends and family", "General public"]
            ),
            self._create_question(
                "What features are most important to you?",
                QuestionType.FEATURE_CLARIFICATION,
                user_profile.technical_level,
                priority=9,
                hints=["Core functionality", "User interface", "Data management", "Integration with other tools"]
            )
        ]

        return fallback_questions[:max_questions]

    def _initialize_question_frameworks(self) -> Dict[str, Any]:
        """Initialize question generation frameworks"""
        return {
            "conversation_starters": [
                "I love this idea! ",
                "That sounds really interesting! ",
                "Great concept! ",
                "I'm excited to help with this! ",
                "This could be really useful! "
            ],
            "clarification_phrases": [
                "Quick question - ",
                "I'm curious about ",
                "To help me understand better, ",
                "One thing I'd like to clarify - ",
                "This will help me suggest the right approach - "
            ],
            "technical_level_adaptations": {
                TechnicalLevel.BEGINNER: {
                    "tone": "encouraging and simple",
                    "avoid": ["architecture", "scalability", "microservices"],
                    "prefer": ["features", "users", "goals"]
                },
                TechnicalLevel.EXPERT: {
                    "tone": "technical and detailed",
                    "include": ["architecture", "patterns", "performance"],
                    "prefer": ["implementation", "constraints", "optimization"]
                }
            }
        }

    def _initialize_context_patterns(self) -> Dict[str, List[str]]:
        """Initialize context-aware question patterns"""
        return {
            "follow_up_indicators": [
                "also", "additionally", "furthermore", "another thing",
                "by the way", "speaking of", "related to that"
            ],
            "uncertainty_indicators": [
                "not sure", "maybe", "possibly", "might", "could be",
                "I think", "probably", "perhaps"
            ],
            "enthusiasm_indicators": [
                "excited", "love", "amazing", "awesome", "great",
                "fantastic", "perfect", "exactly"
            ]
        }
