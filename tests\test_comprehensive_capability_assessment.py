# AI Coding Agent - Comprehensive Capability Assessment
"""
Comprehensive validation of AI Coding Agent capabilities against user requirements:
1. Universal Project Generation
2. Design Customization Control
3. Functional Specification Compliance
4. Technology Stack Flexibility
"""

import sys
import os
import tempfile
from pathlib import Path
from typing import Dict, List, Any

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

def test_universal_project_generation():
    """Test capability to generate diverse project types"""
    print("🌐 Testing Universal Project Generation Capability...")

    # Test cases covering different project types
    test_cases = [
        {
            "request": "Create a static portfolio website with HTML, CSS, and JavaScript",
            "expected_type": "web_app",
            "expected_tech": ["html", "css", "javascript"],
            "complexity": "simple"
        },
        {
            "request": "Build a React e-commerce platform with shopping cart and payment integration",
            "expected_type": "web_app",
            "expected_tech": ["react", "javascript"],
            "complexity": "complex"
        },
        {
            "request": "Create a REST API for a blog management system with user authentication",
            "expected_type": "api",
            "expected_tech": ["python", "flask"],
            "complexity": "medium"
        },
        {
            "request": "Build a full-stack social media application with real-time messaging",
            "expected_type": "web_app",
            "expected_tech": ["react", "node.js", "websockets"],
            "complexity": "complex"
        },
        {
            "request": "Create a mobile-responsive content management system",
            "expected_type": "web_app",
            "expected_tech": ["html", "css", "javascript"],
            "complexity": "medium"
        }
    ]

    try:
        from requirements_parser import RequirementParser
        parser = RequirementParser()

        results = []
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n   Test {i}: {test_case['request'][:50]}...")

            try:
                # Parse the request
                result = parser.parse_requirements(test_case['request'])

                # Validate project type detection
                detected_type = result.project_type.value if hasattr(result.project_type, 'value') else str(result.project_type)
                type_match = detected_type == test_case['expected_type']

                # Validate technology detection
                detected_tech = [tech.lower() for tech in result.technologies]
                tech_overlap = any(tech in detected_tech for tech in test_case['expected_tech'])

                # Check if basic features are detected
                has_features = len(result.features) > 0

                test_result = {
                    'test_case': i,
                    'type_detection': type_match,
                    'tech_detection': tech_overlap,
                    'feature_extraction': has_features,
                    'overall_success': type_match and tech_overlap and has_features
                }

                results.append(test_result)

                status = "✅ PASS" if test_result['overall_success'] else "❌ FAIL"
                print(f"      {status} Type: {detected_type}, Tech: {detected_tech[:3]}, Features: {len(result.features)}")

            except Exception as e:
                print(f"      ❌ FAIL - Exception: {e}")
                results.append({'test_case': i, 'overall_success': False, 'error': str(e)})

        # Calculate success rate
        successful_tests = sum(1 for r in results if r.get('overall_success', False))
        success_rate = (successful_tests / len(test_cases)) * 100

        print(f"\n   📊 Universal Project Generation Results:")
        print(f"      Success Rate: {success_rate:.1f}% ({successful_tests}/{len(test_cases)})")

        if success_rate >= 80:
            print(f"      ✅ CAPABILITY VALIDATED - Can handle diverse project types")
            return True
        else:
            print(f"      ❌ CAPABILITY LIMITED - Struggles with project diversity")
            return False

    except Exception as e:
        print(f"   ❌ CAPABILITY TEST FAILED: {e}")
        return False

def test_design_customization_control():
    """Test capability to handle specific design requirements"""
    print("\n🎨 Testing Design Customization Control...")

    design_requests = [
        {
            "request": "Create a dark theme website with purple accents and modern card-based layout",
            "design_elements": ["dark theme", "purple", "card layout"],
            "complexity": "medium"
        },
        {
            "request": "Build a minimalist portfolio with clean typography and subtle animations",
            "design_elements": ["minimalist", "typography", "animations"],
            "complexity": "medium"
        },
        {
            "request": "Design a responsive e-commerce site with grid layout and hover effects",
            "design_elements": ["responsive", "grid", "hover effects"],
            "complexity": "complex"
        }
    ]

    try:
        from requirements_parser import RequirementParser
        parser = RequirementParser()

        design_detection_score = 0
        total_elements = 0

        for i, test_case in enumerate(design_requests, 1):
            print(f"\n   Test {i}: {test_case['request'][:50]}...")

            try:
                result = parser.parse_requirements(test_case['request'])

                # Check if design elements are captured in features or technical requirements
                captured_elements = 0
                all_text = ' '.join([
                    result.description.lower(),
                    ' '.join(result.features).lower(),
                    str(result.technical_requirements).lower()
                ])

                for element in test_case['design_elements']:
                    if element.lower() in all_text:
                        captured_elements += 1
                        print(f"      ✅ Detected: {element}")
                    else:
                        print(f"      ❌ Missed: {element}")

                design_detection_score += captured_elements
                total_elements += len(test_case['design_elements'])

            except Exception as e:
                print(f"      ❌ FAIL - Exception: {e}")

        detection_rate = (design_detection_score / total_elements) * 100 if total_elements > 0 else 0

        print(f"\n   📊 Design Customization Results:")
        print(f"      Design Element Detection: {detection_rate:.1f}% ({design_detection_score}/{total_elements})")

        if detection_rate >= 60:
            print(f"      ✅ CAPABILITY VALIDATED - Can capture design requirements")
            return True
        else:
            print(f"      ❌ CAPABILITY LIMITED - Struggles with design specification capture")
            return False

    except Exception as e:
        print(f"   ❌ DESIGN CAPABILITY TEST FAILED: {e}")
        return False

def test_functional_specification_compliance():
    """Test capability to handle specific functional requirements"""
    print("\n⚙️ Testing Functional Specification Compliance...")

    functional_requests = [
        {
            "request": "Create a user authentication system with email verification and password reset",
            "functions": ["authentication", "email verification", "password reset"],
            "complexity": "medium"
        },
        {
            "request": "Build a real-time chat application with message history and file sharing",
            "functions": ["real-time chat", "message history", "file sharing"],
            "complexity": "complex"
        },
        {
            "request": "Design a payment processing system with multiple payment methods and refunds",
            "functions": ["payment processing", "multiple payment methods", "refunds"],
            "complexity": "complex"
        }
    ]

    try:
        from requirements_parser import RequirementParser
        parser = RequirementParser()

        function_detection_score = 0
        total_functions = 0

        for i, test_case in enumerate(functional_requests, 1):
            print(f"\n   Test {i}: {test_case['request'][:50]}...")

            try:
                result = parser.parse_requirements(test_case['request'])

                # Check if functional requirements are captured
                captured_functions = 0
                all_text = ' '.join([
                    result.description.lower(),
                    ' '.join(result.features).lower(),
                    ' '.join(result.user_stories).lower(),
                    str(result.technical_requirements).lower()
                ])

                for function in test_case['functions']:
                    if any(word in all_text for word in function.lower().split()):
                        captured_functions += 1
                        print(f"      ✅ Detected: {function}")
                    else:
                        print(f"      ❌ Missed: {function}")

                function_detection_score += captured_functions
                total_functions += len(test_case['functions'])

            except Exception as e:
                print(f"      ❌ FAIL - Exception: {e}")

        detection_rate = (function_detection_score / total_functions) * 100 if total_functions > 0 else 0

        print(f"\n   📊 Functional Specification Results:")
        print(f"      Function Detection: {detection_rate:.1f}% ({function_detection_score}/{total_functions})")

        if detection_rate >= 70:
            print(f"      ✅ CAPABILITY VALIDATED - Can capture functional requirements")
            return True
        else:
            print(f"      ❌ CAPABILITY LIMITED - Struggles with functional specification capture")
            return False

    except Exception as e:
        print(f"   ❌ FUNCTIONAL CAPABILITY TEST FAILED: {e}")
        return False

def test_technology_stack_flexibility():
    """Test capability to handle different technology preferences"""
    print("\n🔧 Testing Technology Stack Flexibility...")

    tech_requests = [
        {
            "request": "Build a React application with TypeScript and Material-UI",
            "expected_tech": ["react", "typescript", "material-ui"],
            "category": "frontend"
        },
        {
            "request": "Create a Python Flask API with PostgreSQL database",
            "expected_tech": ["python", "flask", "postgresql"],
            "category": "backend"
        },
        {
            "request": "Build a Vue.js application with Node.js backend and MongoDB",
            "expected_tech": ["vue", "node.js", "mongodb"],
            "category": "full-stack"
        },
        {
            "request": "Create a mobile app using React Native with Firebase",
            "expected_tech": ["react native", "firebase"],
            "category": "mobile"
        }
    ]

    try:
        from requirements_parser import RequirementParser
        parser = RequirementParser()

        tech_detection_score = 0
        total_tech = 0

        for i, test_case in enumerate(tech_requests, 1):
            print(f"\n   Test {i}: {test_case['request'][:50]}...")

            try:
                result = parser.parse_requirements(test_case['request'])

                # Check technology detection
                detected_tech = [tech.lower() for tech in result.technologies]
                captured_tech = 0

                for tech in test_case['expected_tech']:
                    if any(tech.lower() in dt for dt in detected_tech) or tech.lower() in ' '.join(detected_tech):
                        captured_tech += 1
                        print(f"      ✅ Detected: {tech}")
                    else:
                        print(f"      ❌ Missed: {tech}")

                tech_detection_score += captured_tech
                total_tech += len(test_case['expected_tech'])

                print(f"      Technologies found: {detected_tech[:5]}")

            except Exception as e:
                print(f"      ❌ FAIL - Exception: {e}")

        detection_rate = (tech_detection_score / total_tech) * 100 if total_tech > 0 else 0

        print(f"\n   📊 Technology Stack Flexibility Results:")
        print(f"      Technology Detection: {detection_rate:.1f}% ({tech_detection_score}/{total_tech})")

        if detection_rate >= 75:
            print(f"      ✅ CAPABILITY VALIDATED - Can handle diverse technology stacks")
            return True
        else:
            print(f"      ❌ CAPABILITY LIMITED - Struggles with technology stack flexibility")
            return False

    except Exception as e:
        print(f"   ❌ TECHNOLOGY CAPABILITY TEST FAILED: {e}")
        return False

def test_current_generation_capabilities():
    """Test actual code generation capabilities"""
    print("\n🏗️ Testing Current Code Generation Capabilities...")

    try:
        from prototype_orchestrator import PrototypeOrchestrator

        with tempfile.TemporaryDirectory() as temp_dir:
            orchestrator = PrototypeOrchestrator(temp_dir)

            # Test simple web app generation
            test_request = "Create a simple todo list web application"

            try:
                result = orchestrator.create_project_from_natural_language(test_request)

                if result and result.success:
                    project_path = Path(result.project_path)

                    # Check if files were actually generated
                    generated_files = list(project_path.glob('**/*'))
                    generated_files = [f for f in generated_files if f.is_file()]

                    print(f"      ✅ Project generated successfully")
                    print(f"      ✅ Files created: {len(generated_files)}")
                    print(f"      ✅ Project path: {project_path}")

                    # Check for essential web app files
                    essential_files = ['app.py', 'requirements.txt']
                    found_essential = 0

                    for essential in essential_files:
                        if (project_path / essential).exists():
                            found_essential += 1
                            print(f"      ✅ Found: {essential}")
                        else:
                            print(f"      ❌ Missing: {essential}")

                    if found_essential >= len(essential_files) * 0.8:  # 80% of essential files
                        print(f"      ✅ GENERATION CAPABILITY VALIDATED")
                        return True
                    else:
                        print(f"      ❌ GENERATION CAPABILITY LIMITED - Missing essential files")
                        return False
                else:
                    print(f"      ❌ Project generation failed")
                    return False

            except Exception as e:
                print(f"      ❌ Generation test failed: {e}")
                return False

    except Exception as e:
        print(f"   ❌ GENERATION CAPABILITY TEST FAILED: {e}")
        return False

def run_comprehensive_capability_assessment():
    """Run comprehensive capability assessment"""
    print("=" * 100)
    print("🚀 COMPREHENSIVE AI CODING AGENT CAPABILITY ASSESSMENT")
    print("=" * 100)
    print("Validating capabilities against user requirements:")
    print("1. Universal Project Generation")
    print("2. Design Customization Control")
    print("3. Functional Specification Compliance")
    print("4. Technology Stack Flexibility")
    print("=" * 100)

    # Run all capability tests
    test_results = []

    tests = [
        ("Universal Project Generation", test_universal_project_generation),
        ("Design Customization Control", test_design_customization_control),
        ("Functional Specification Compliance", test_functional_specification_compliance),
        ("Technology Stack Flexibility", test_technology_stack_flexibility),
        ("Current Generation Capabilities", test_current_generation_capabilities)
    ]

    for test_name, test_func in tests:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            test_results.append((test_name, False))

    # Calculate overall capability score
    passed_tests = sum(1 for _, result in test_results if result)
    total_tests = len(test_results)
    capability_score = (passed_tests / total_tests) * 100

    # Print comprehensive assessment
    print("\n" + "=" * 100)
    print("📊 COMPREHENSIVE CAPABILITY ASSESSMENT RESULTS")
    print("=" * 100)

    for test_name, result in test_results:
        status = "✅ VALIDATED" if result else "❌ LIMITED"
        print(f"{status} {test_name}")

    print(f"\n🎯 Overall Capability Score: {capability_score:.1f}% ({passed_tests}/{total_tests})")

    # Detailed capability analysis
    print(f"\n📋 DETAILED CAPABILITY ANALYSIS:")

    if capability_score >= 80:
        print("✅ EXCELLENT - Ready for diverse project requirements")
        print("✅ Can handle most user requests with high confidence")
        print("✅ Suitable for Phase 4 GUI development")
        recommendation = "PROCEED"
    elif capability_score >= 60:
        print("⚠️ GOOD - Can handle many project types with some limitations")
        print("⚠️ May struggle with complex or highly specific requirements")
        print("⚠️ Consider capability improvements before Phase 4")
        recommendation = "PROCEED WITH CAUTION"
    else:
        print("❌ LIMITED - Significant capability gaps identified")
        print("❌ Cannot reliably handle diverse project requirements")
        print("❌ Requires substantial improvements before Phase 4")
        recommendation = "DO NOT PROCEED"

    print(f"\n🎯 RECOMMENDATION: {recommendation}")

    if recommendation == "PROCEED":
        print("✅ AI Coding Agent is ready for Phase 4 development")
        print("✅ Current capabilities meet user requirements")
        return True
    else:
        print("❌ AI Coding Agent needs capability improvements")
        print("❌ Address identified limitations before Phase 4")
        return False

if __name__ == "__main__":
    success = run_comprehensive_capability_assessment()
    if not success:
        sys.exit(1)