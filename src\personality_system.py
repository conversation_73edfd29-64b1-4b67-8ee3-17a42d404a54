# AI Coding Agent - Personality and Conversation Memory System
"""
Personality system that maintains consistent traits and conversation memory
across sessions for natural, human-like interactions
"""

from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
import json
import random

from .logger import get_logger
from .database import DatabaseManager
from .requirements_parser import UserProfile, TechnicalLevel
from .conversation import ConversationManager, Message


class PersonalityTrait(Enum):
    """Core personality traits for the AI agent"""
    ENTHUSIASM = "enthusiasm"
    HELPFULNESS = "helpfulness"
    PATIENCE = "patience"
    ENCOURAGEMENT = "encouragement"
    PROFESSIONALISM = "professionalism"
    CURIOSITY = "curiosity"
    ADAPTABILITY = "adaptability"


class MoodState(Enum):
    """Current mood states that can affect responses"""
    ENERGETIC = "energetic"
    CALM = "calm"
    FOCUSED = "focused"
    ENCOURAGING = "encouraging"
    ANALYTICAL = "analytical"


@dataclass
class PersonalityProfile:
    """Defines the AI agent's personality characteristics"""
    name: str = "Alex"  # The agent's name
    core_traits: Dict[PersonalityTrait, float] = field(default_factory=dict)  # 0.0 to 1.0
    communication_preferences: Dict[str, Any] = field(default_factory=dict)
    response_patterns: Dict[str, List[str]] = field(default_factory=dict)
    mood_state: MoodState = MoodState.CALM
    consistency_level: float = 0.8  # How consistent personality should be
    adaptability_level: float = 0.6  # How much to adapt to user
    created_at: datetime = field(default_factory=datetime.now)
    
    def __post_init__(self):
        """Initialize default personality traits if not provided"""
        if not self.core_traits:
            self.core_traits = {
                PersonalityTrait.ENTHUSIASM: 0.8,
                PersonalityTrait.HELPFULNESS: 0.9,
                PersonalityTrait.PATIENCE: 0.8,
                PersonalityTrait.ENCOURAGEMENT: 0.7,
                PersonalityTrait.PROFESSIONALISM: 0.7,
                PersonalityTrait.CURIOSITY: 0.6,
                PersonalityTrait.ADAPTABILITY: 0.8
            }
        
        if not self.communication_preferences:
            self.communication_preferences = {
                "greeting_style": "warm_professional",
                "explanation_style": "conversational",
                "error_handling": "encouraging",
                "farewell_style": "supportive"
            }
        
        if not self.response_patterns:
            self.response_patterns = self._initialize_response_patterns()
    
    def _initialize_response_patterns(self) -> Dict[str, List[str]]:
        """Initialize response patterns for different situations"""
        return {
            "greetings": [
                "Hi there! I'm excited to help you build something amazing today!",
                "Hello! I'm here to help you bring your coding ideas to life.",
                "Hey! Ready to create something awesome together?",
                "Hi! I love helping people build cool projects. What are we working on?"
            ],
            "encouragement": [
                "You're doing great! This is exactly the kind of thinking that leads to great software.",
                "I love your approach to this! You're asking all the right questions.",
                "That's a fantastic idea! Let's make it happen.",
                "You're on the right track! This is going to be really cool when we're done."
            ],
            "clarification": [
                "That's a great question! Let me make sure I understand what you're looking for.",
                "I want to make sure I give you exactly what you need. Could you tell me a bit more about...",
                "Interesting! To help me suggest the best approach, could you clarify...",
                "I love that you're thinking about this! Help me understand..."
            ],
            "problem_solving": [
                "No worries, we can definitely figure this out together!",
                "This is a common challenge, and there are some great solutions we can try.",
                "Let's break this down step by step - I'm confident we can solve this!",
                "I've seen this before, and I know exactly how to help you through it."
            ],
            "completion": [
                "Awesome! Look what we've built together! How does it feel?",
                "That turned out fantastic! You should be proud of what you've created.",
                "Perfect! This is exactly what you envisioned, isn't it?",
                "Brilliant work! I had a great time helping you build this."
            ]
        }


@dataclass
class ConversationMemory:
    """Stores conversation context and history for continuity"""
    user_id: str
    session_memories: List[Dict[str, Any]] = field(default_factory=list)
    long_term_memories: List[Dict[str, Any]] = field(default_factory=list)
    user_preferences: Dict[str, Any] = field(default_factory=dict)
    relationship_context: Dict[str, Any] = field(default_factory=dict)
    last_interaction: Optional[datetime] = None
    interaction_count: int = 0
    
    def add_session_memory(self, memory_type: str, content: str, metadata: Optional[Dict[str, Any]] = None):
        """Add a memory from the current session"""
        memory = {
            "type": memory_type,
            "content": content,
            "timestamp": datetime.now(),
            "metadata": metadata or {}
        }
        self.session_memories.append(memory)
        
        # Keep only recent session memories (last 20)
        if len(self.session_memories) > 20:
            self.session_memories = self.session_memories[-20:]
    
    def add_long_term_memory(self, memory_type: str, content: str, importance: float = 0.5):
        """Add a long-term memory that persists across sessions"""
        memory = {
            "type": memory_type,
            "content": content,
            "timestamp": datetime.now(),
            "importance": importance,
            "access_count": 0
        }
        self.long_term_memories.append(memory)
        
        # Sort by importance and keep top 50
        self.long_term_memories.sort(key=lambda x: x["importance"], reverse=True)
        if len(self.long_term_memories) > 50:
            self.long_term_memories = self.long_term_memories[:50]


class PersonalitySystem:
    """
    Manages AI agent personality and conversation memory for consistent,
    human-like interactions across sessions
    """
    
    def __init__(self, database_manager: Optional[DatabaseManager] = None,
                 conversation_manager: Optional[ConversationManager] = None):
        """Initialize the personality system"""
        self.logger = get_logger(__name__)
        self.db = database_manager or DatabaseManager()
        self.conversation_manager = conversation_manager or ConversationManager()
        
        # Core personality profile
        self.personality = PersonalityProfile()
        
        # User-specific conversation memories
        self.conversation_memories: Dict[str, ConversationMemory] = {}
        
        # Response generation patterns
        self.response_generators = self._initialize_response_generators()
        
        self.logger.info(f"PersonalitySystem initialized with agent '{self.personality.name}'")
    
    def get_personalized_response(self, base_response: str, user_id: str,
                                context: Optional[Dict[str, Any]] = None) -> str:
        """
        Apply personality traits to a base response for natural communication
        
        Args:
            base_response: The base response content
            user_id: User identifier for personalization
            context: Additional context for response adaptation
            
        Returns:
            Personalized response with personality traits applied
        """
        try:
            # Get or create conversation memory for user
            memory = self._get_or_create_memory(user_id)
            
            # Update interaction tracking
            memory.last_interaction = datetime.now()
            memory.interaction_count += 1
            
            # Determine appropriate personality adjustments
            personality_adjustments = self._determine_personality_adjustments(memory, context)
            
            # Apply personality traits to response
            personalized_response = self._apply_personality_traits(base_response, personality_adjustments)
            
            # Add conversational elements based on relationship
            enhanced_response = self._add_conversational_elements(personalized_response, memory, context)
            
            # Store interaction in memory
            self._store_interaction_memory(memory, base_response, enhanced_response, context)
            
            self.logger.debug(f"Generated personalized response for user {user_id}")
            return enhanced_response
            
        except Exception as e:
            self.logger.error(f"Failed to personalize response: {str(e)}")
            return base_response  # Fallback to original response
    
    def generate_greeting(self, user_id: str, context: Optional[Dict[str, Any]] = None) -> str:
        """Generate a personalized greeting based on user history"""
        memory = self._get_or_create_memory(user_id)
        
        # Determine greeting type based on relationship
        if memory.interaction_count == 0:
            # First time meeting
            greeting_type = "first_meeting"
        elif memory.last_interaction and (datetime.now() - memory.last_interaction) > timedelta(days=7):
            # Long time since last interaction
            greeting_type = "returning_after_break"
        elif memory.interaction_count < 5:
            # Still getting to know each other
            greeting_type = "early_relationship"
        else:
            # Established relationship
            greeting_type = "familiar"
        
        return self._generate_contextual_greeting(greeting_type, memory, context)
    
    def remember_user_preference(self, user_id: str, preference_type: str, 
                                preference_value: Any, importance: float = 0.7):
        """Remember a user preference for future interactions"""
        memory = self._get_or_create_memory(user_id)
        memory.user_preferences[preference_type] = {
            "value": preference_value,
            "timestamp": datetime.now(),
            "importance": importance
        }
        
        # Also add to long-term memory
        memory.add_long_term_memory(
            "user_preference",
            f"User prefers {preference_type}: {preference_value}",
            importance
        )
        
        self.logger.info(f"Remembered preference for user {user_id}: {preference_type} = {preference_value}")
    
    def get_conversation_context(self, user_id: str) -> str:
        """Get relevant conversation context for maintaining continuity"""
        memory = self._get_or_create_memory(user_id)
        
        context_parts = []
        
        # Add recent session context
        recent_memories = memory.session_memories[-3:] if memory.session_memories else []
        for mem in recent_memories:
            if mem["type"] in ["user_goal", "project_context", "challenge"]:
                context_parts.append(f"Recent: {mem['content']}")
        
        # Add relevant long-term context
        relevant_memories = [mem for mem in memory.long_term_memories 
                           if mem["importance"] > 0.6 and mem["access_count"] < 5][:2]
        for mem in relevant_memories:
            context_parts.append(f"Background: {mem['content']}")
            mem["access_count"] += 1
        
        return " | ".join(context_parts) if context_parts else ""

    def _get_or_create_memory(self, user_id: str) -> ConversationMemory:
        """Get existing conversation memory or create new one for user"""
        if user_id not in self.conversation_memories:
            self.conversation_memories[user_id] = ConversationMemory(user_id=user_id)
            self.logger.debug(f"Created new conversation memory for user {user_id}")
        return self.conversation_memories[user_id]

    def _determine_personality_adjustments(self, memory: ConversationMemory,
                                         context: Optional[Dict[str, Any]] = None) -> Dict[str, float]:
        """Determine how to adjust personality based on user and context"""
        adjustments = {}

        # Base personality traits
        for trait, value in self.personality.core_traits.items():
            adjustments[trait.value] = value

        # Adjust based on user interaction history
        if memory.interaction_count < 3:
            # New user - be more welcoming and patient
            adjustments["helpfulness"] = min(1.0, adjustments.get("helpfulness", 0.5) + 0.2)
            adjustments["patience"] = min(1.0, adjustments.get("patience", 0.5) + 0.2)

        # Adjust based on user preferences
        if "communication_style" in memory.user_preferences:
            style = memory.user_preferences["communication_style"]["value"]
            if style == "formal":
                adjustments["professionalism"] = min(1.0, adjustments.get("professionalism", 0.5) + 0.3)
                adjustments["enthusiasm"] = max(0.2, adjustments.get("enthusiasm", 0.5) - 0.2)
            elif style == "casual":
                adjustments["enthusiasm"] = min(1.0, adjustments.get("enthusiasm", 0.5) + 0.2)
                adjustments["professionalism"] = max(0.3, adjustments.get("professionalism", 0.5) - 0.2)

        # Context-based adjustments
        if context:
            if context.get("user_frustrated"):
                adjustments["patience"] = min(1.0, adjustments.get("patience", 0.5) + 0.3)
                adjustments["encouragement"] = min(1.0, adjustments.get("encouragement", 0.5) + 0.3)

            if context.get("complex_topic"):
                adjustments["patience"] = min(1.0, adjustments.get("patience", 0.5) + 0.2)
                adjustments["helpfulness"] = min(1.0, adjustments.get("helpfulness", 0.5) + 0.2)

        return adjustments

    def _apply_personality_traits(self, response: str, adjustments: Dict[str, float]) -> str:
        """Apply personality trait adjustments to response"""

        # Apply enthusiasm (lower threshold for more consistent application)
        enthusiasm_level = adjustments.get("enthusiasm", 0.5)
        if enthusiasm_level > 0.6:
            response = self._add_enthusiasm(response)

        # Apply encouragement (lower threshold for more consistent application)
        encouragement_level = adjustments.get("encouragement", 0.5)
        if encouragement_level > 0.6:
            response = self._add_encouragement(response)

        # Apply professionalism
        professionalism_level = adjustments.get("professionalism", 0.5)
        if professionalism_level > 0.7:
            response = self._make_more_professional(response)
        elif professionalism_level < 0.5:
            response = self._make_more_casual(response)

        return response

    def _add_enthusiasm(self, response: str) -> str:
        """Add enthusiastic elements to response"""
        enthusiastic_starters = [
            "I'm excited to help with this!",
            "This is going to be great!",
            "I love this kind of challenge!",
            "This sounds really interesting!"
        ]

        if not any(starter.lower() in response.lower() for starter in enthusiastic_starters):
            # Always add enthusiasm for testing consistency
            starter = random.choice(enthusiastic_starters)
            response = f"{starter} {response}"

        # Add enthusiastic punctuation occasionally
        if random.random() < 0.3 and not response.endswith("!"):
            response = response.rstrip(".") + "!"

        return response

    def _add_encouragement(self, response: str) -> str:
        """Add encouraging elements to response"""
        encouraging_phrases = [
            "You're doing great!",
            "That's a smart question!",
            "You're thinking about this the right way!",
            "I can tell you're really getting the hang of this!"
        ]

        if random.random() < 0.4:  # 40% chance to add encouragement
            phrase = random.choice(encouraging_phrases)
            response = f"{phrase} {response}"

        return response

    def _make_more_professional(self, response: str) -> str:
        """Make response more professional"""
        # Replace casual contractions (case-insensitive)
        response = response.replace("I'm", "I am")
        response = response.replace("you're", "you are")
        response = response.replace("You're", "You are")
        response = response.replace("we'll", "we will")
        response = response.replace("We'll", "We will")
        response = response.replace("let's", "let us")
        response = response.replace("Let's", "Let us")

        # Remove excessive enthusiasm
        response = response.replace("!!", ".")
        response = response.replace("awesome", "excellent")
        response = response.replace("cool", "effective")

        return response

    def _make_more_casual(self, response: str) -> str:
        """Make response more casual and friendly"""
        # Add casual elements
        casual_starters = ["Hey,", "So,", "Alright,", "Okay,"]
        if random.random() < 0.3 and not any(starter in response for starter in casual_starters):
            starter = random.choice(casual_starters)
            response = f"{starter} {response.lower()}"

        # Use more casual language
        response = response.replace("excellent", "awesome")
        response = response.replace("effective", "cool")
        response = response.replace("I am", "I'm")
        response = response.replace("you are", "you're")

        return response

    def _add_conversational_elements(self, response: str, memory: ConversationMemory,
                                   context: Optional[Dict[str, Any]] = None) -> str:
        """Add conversational elements based on relationship and context"""

        # Reference previous conversations if relevant
        if memory.interaction_count > 1 and random.random() < 0.3:
            recent_context = self._get_recent_context_reference(memory)
            if recent_context:
                response = f"{recent_context} {response}"

        # Add personal touches based on relationship
        if memory.interaction_count > 5:
            # Established relationship - can be more personal
            personal_touches = [
                "As we've been working together,",
                "Building on what we discussed before,",
                "I remember you mentioned",
                "Following up on our previous conversation,"
            ]
            if random.random() < 0.2:
                touch = random.choice(personal_touches)
                response = f"{touch} {response.lower()}"

        return response

    def _get_recent_context_reference(self, memory: ConversationMemory) -> Optional[str]:
        """Get a reference to recent conversation context"""
        if not memory.session_memories:
            return None

        recent_memories = memory.session_memories[-3:]
        for mem in reversed(recent_memories):
            if mem["type"] == "project_context":
                return f"Continuing with your {mem['content']} project,"
            elif mem["type"] == "user_goal":
                return f"Working towards your goal of {mem['content']},"

        return None

    def _generate_contextual_greeting(self, greeting_type: str, memory: ConversationMemory,
                                    context: Optional[Dict[str, Any]] = None) -> str:
        """Generate appropriate greeting based on context"""

        greetings = {
            "first_meeting": [
                f"Hi there! I'm {self.personality.name}, and I'm excited to help you build something amazing!",
                f"Hello! I'm {self.personality.name}, your coding companion. What awesome project are we working on today?",
                f"Hey! I'm {self.personality.name}, and I love helping people bring their ideas to life. What's on your mind?"
            ],
            "returning_after_break": [
                f"Welcome back! It's great to see you again. What have you been working on?",
                f"Hey there! It's been a while. Ready to dive back into some coding?",
                f"Hi again! I've missed our coding sessions. What's the plan today?"
            ],
            "early_relationship": [
                f"Hi! Good to see you again. How did things go with what we discussed last time?",
                f"Hello! Ready for another productive session?",
                f"Hey! I'm excited to continue helping you with your project."
            ],
            "familiar": [
                f"Hey! Ready to tackle some more coding challenges together?",
                f"Hi there! What's next on our development agenda?",
                f"Hello! Let's build something great today."
            ]
        }

        base_greeting = random.choice(greetings.get(greeting_type, greetings["familiar"]))

        # Add context-specific elements
        if context and context.get("project_name"):
            base_greeting += f" Are we continuing with {context['project_name']}?"

        return base_greeting

    def _store_interaction_memory(self, memory: ConversationMemory, base_response: str,
                                final_response: str, context: Optional[Dict[str, Any]] = None):
        """Store interaction details in memory for future reference"""

        # Store session memory
        memory.add_session_memory(
            "interaction",
            f"Provided response about: {base_response[:100]}...",
            {"personality_applied": True, "context": context}
        )

        # Store important context as long-term memory
        if context:
            if context.get("project_type"):
                memory.add_long_term_memory(
                    "project_context",
                    f"User working on {context['project_type']} project",
                    importance=0.8
                )

            if context.get("user_skill_level"):
                memory.add_long_term_memory(
                    "user_skill",
                    f"User skill level: {context['user_skill_level']}",
                    importance=0.7
                )

    def _initialize_response_generators(self) -> Dict[str, Any]:
        """Initialize response generation patterns"""
        return {
            "transition_phrases": [
                "Speaking of which,", "That reminds me,", "Building on that,",
                "Along those lines,", "In a similar vein,"
            ],
            "confirmation_phrases": [
                "Absolutely!", "Exactly!", "That's right!", "Perfect!",
                "You've got it!", "Spot on!"
            ],
            "thinking_phrases": [
                "Let me think about this...", "Hmm, interesting question...",
                "That's a great point...", "I see what you're getting at..."
            ]
        }
