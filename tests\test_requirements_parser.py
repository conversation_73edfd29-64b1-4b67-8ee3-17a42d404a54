# AI Coding Agent - Requirements Parser Tests
"""
Tests for the RequirementParser module
"""

import pytest
import json
from unittest.mock import Mock, patch
from datetime import datetime

from src.requirements_parser import (
    RequirementParser, ProjectRequirement, UserProfile, 
    ProjectType, TechnicalLevel
)
from src.exceptions import RequirementParsingError


class TestRequirementParser:
    """Test cases for RequirementParser"""
    
    def setup_method(self):
        """Set up test fixtures"""
        # Mock LLM manager to avoid actual LLM calls in tests
        self.mock_llm_manager = Mock()
        self.mock_conversation_manager = Mock()
        
        self.parser = RequirementParser(
            llm_manager=self.mock_llm_manager,
            conversation_manager=self.mock_conversation_manager
        )
    
    def test_initialization(self):
        """Test RequirementParser initialization"""
        assert self.parser is not None
        assert self.parser.llm_manager is not None
        assert self.parser.conversation_manager is not None
        assert isinstance(self.parser.user_profiles, dict)
        assert isinstance(self.parser.project_patterns, dict)
        assert isinstance(self.parser.tech_keywords, dict)
    
    def test_project_type_detection_web_app(self):
        """Test detection of web application projects"""
        test_inputs = [
            "I want to create a web application for managing tasks",
            "Build a website with user login and dashboard",
            "Create a responsive web app using React",
            "I need a web portal for my business"
        ]
        
        for user_input in test_inputs:
            analysis = self.parser._analyze_input_patterns(user_input)
            assert analysis['project_type'] == ProjectType.WEB_APP
    
    def test_project_type_detection_api(self):
        """Test detection of API projects"""
        test_inputs = [
            "Create a REST API for user management",
            "I need a GraphQL endpoint for my mobile app",
            "Build a microservice with JSON responses",
            "Create an API that handles HTTP requests"
        ]
        
        for user_input in test_inputs:
            analysis = self.parser._analyze_input_patterns(user_input)
            assert analysis['project_type'] == ProjectType.API
    
    def test_technology_detection(self):
        """Test detection of technologies in user input"""
        user_input = "Create a React web app with Python Flask backend and PostgreSQL database"
        analysis = self.parser._analyze_input_patterns(user_input)
        
        detected_techs = analysis['detected_technologies']
        assert 'react' in detected_techs
        assert 'python' in detected_techs
        assert 'flask' in detected_techs
        assert 'postgresql' in detected_techs
    
    def test_feature_detection(self):
        """Test detection of features in user input"""
        user_input = "I want a web app with user login, search functionality, and file upload"
        analysis = self.parser._analyze_input_patterns(user_input)
        
        features = analysis['features']
        assert 'login' in features
        assert 'search' in features
        assert 'upload' in features
    
    def test_complexity_detection(self):
        """Test detection of complexity indicators"""
        test_cases = [
            ("Create a simple todo app", ['low']),
            ("Build a complex enterprise system", ['high']),
            ("I need a basic calculator", ['low']),
            ("Create an advanced scalable platform", ['high'])
        ]
        
        for user_input, expected_complexity in test_cases:
            analysis = self.parser._analyze_input_patterns(user_input)
            complexity_indicators = analysis['complexity_indicators']
            assert any(indicator in complexity_indicators for indicator in expected_complexity)
    
    def test_user_profile_creation(self):
        """Test user profile creation and management"""
        user_id = "test_user_123"
        
        # First call should create new profile
        profile1 = self.parser._get_or_create_user_profile(user_id)
        assert isinstance(profile1, UserProfile)
        assert profile1.technical_level == TechnicalLevel.INTERMEDIATE  # Default
        
        # Second call should return same profile
        profile2 = self.parser._get_or_create_user_profile(user_id)
        assert profile1.id == profile2.id
    
    def test_llm_analysis_with_mock(self):
        """Test LLM analysis with mocked response"""
        # Mock LLM response
        mock_response = json.dumps({
            "project_type": "web_app",
            "title": "Task Management System",
            "description": "A web application for managing daily tasks",
            "features": ["user authentication", "task creation", "task editing"],
            "technologies": ["react", "node.js", "mongodb"],
            "user_stories": ["As a user, I want to create tasks"],
            "technical_requirements": {
                "database": "mongodb",
                "authentication": "jwt"
            },
            "estimated_complexity": "medium",
            "constraints": []
        })
        
        self.mock_llm_manager.generate_response_with_context.return_value = mock_response
        
        user_profile = UserProfile()
        result = self.parser._analyze_with_llm("Create a task management app", "", user_profile)
        
        assert result['project_type'] == 'web_app'
        assert result['title'] == 'Task Management System'
        assert 'user authentication' in result['features']
        assert 'react' in result['technologies']
    
    def test_confidence_score_calculation(self):
        """Test confidence score calculation"""
        # High confidence requirement
        high_conf_req = ProjectRequirement(
            title="Complete Project",
            description="A detailed description of the project",
            project_type=ProjectType.WEB_APP,
            features=["login", "dashboard", "reports"],
            technologies=["react", "python", "postgresql"],
            user_stories=["As a user, I want to login"],
            technical_requirements={"database": "postgresql", "auth": "jwt"}
        )
        
        score = self.parser._calculate_confidence_score(high_conf_req)
        assert score > 0.7  # Should be high confidence
        
        # Low confidence requirement
        low_conf_req = ProjectRequirement()
        score = self.parser._calculate_confidence_score(low_conf_req)
        assert score < 0.3  # Should be low confidence
    
    def test_clarifications_identification(self):
        """Test identification of needed clarifications"""
        # Incomplete requirements should generate clarifications
        incomplete_req = ProjectRequirement(
            project_type=ProjectType.UNKNOWN,
            features=[],
            technologies=[]
        )
        
        user_profile = UserProfile(technical_level=TechnicalLevel.BEGINNER)
        clarifications = self.parser._identify_clarifications(incomplete_req, user_profile)
        
        assert len(clarifications) > 0
        assert any("features" in clarification.lower() for clarification in clarifications)
        assert any("type" in clarification.lower() for clarification in clarifications)
    
    def test_parse_requirements_integration(self):
        """Test full requirements parsing integration"""
        # Mock LLM response for integration test
        mock_response = json.dumps({
            "project_type": "web_app",
            "title": "Blog Platform",
            "description": "A blogging platform with user management",
            "features": ["user registration", "post creation", "comments"],
            "technologies": ["python", "flask", "sqlite"],
            "user_stories": ["As a blogger, I want to create posts"],
            "technical_requirements": {"database": "sqlite"},
            "estimated_complexity": "medium",
            "constraints": []
        })
        
        self.mock_llm_manager.generate_response_with_context.return_value = mock_response
        
        user_input = "I want to create a blog website where users can write posts"
        result = self.parser.parse_requirements(user_input, "test_user")
        
        assert isinstance(result, ProjectRequirement)
        assert result.project_type == ProjectType.WEB_APP
        assert result.title == "Blog Platform"
        assert len(result.features) > 0
        assert result.confidence_score > 0.0
    
    def test_project_suggestions(self):
        """Test project suggestions based on user profile"""
        # Beginner user
        beginner_profile = UserProfile(technical_level=TechnicalLevel.BEGINNER)
        suggestions = self.parser.get_project_suggestions(beginner_profile)
        
        assert len(suggestions) > 0
        assert any("simple" in suggestion.lower() or "basic" in suggestion.lower() 
                  for suggestion in suggestions)
        
        # Expert user
        expert_profile = UserProfile(technical_level=TechnicalLevel.EXPERT)
        suggestions = self.parser.get_project_suggestions(expert_profile)
        
        assert len(suggestions) > 0
        assert any("microservices" in suggestion.lower() or "distributed" in suggestion.lower() 
                  for suggestion in suggestions)
    
    def test_error_handling(self):
        """Test error handling in requirements parsing"""
        # Mock LLM to raise an exception
        self.mock_llm_manager.generate_response_with_context.side_effect = Exception("LLM Error")
        
        # Should not raise exception, should handle gracefully
        result = self.parser.parse_requirements("Create an app", "test_user")
        assert isinstance(result, ProjectRequirement)
        # Should have lower confidence due to LLM failure
        assert result.confidence_score < 1.0


class TestProjectRequirement:
    """Test cases for ProjectRequirement data class"""
    
    def test_project_requirement_creation(self):
        """Test ProjectRequirement creation and serialization"""
        req = ProjectRequirement(
            title="Test Project",
            description="A test project",
            project_type=ProjectType.WEB_APP,
            features=["feature1", "feature2"],
            technologies=["python", "react"]
        )
        
        assert req.title == "Test Project"
        assert req.project_type == ProjectType.WEB_APP
        assert len(req.features) == 2
        
        # Test serialization
        req_dict = req.to_dict()
        assert isinstance(req_dict, dict)
        assert req_dict['title'] == "Test Project"
        assert req_dict['project_type'] == "web_app"


class TestUserProfile:
    """Test cases for UserProfile data class"""
    
    def test_user_profile_creation(self):
        """Test UserProfile creation with defaults"""
        profile = UserProfile()
        
        assert profile.technical_level == TechnicalLevel.INTERMEDIATE
        assert profile.communication_style == "friendly"
        assert isinstance(profile.preferred_technologies, list)
        assert isinstance(profile.interaction_history, dict)
        assert isinstance(profile.created_at, datetime)
