#!/usr/bin/env python3
"""
Test script for enhanced project generators
"""

import sys
import os
import tempfile
from pathlib import Path

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from enhanced_project_generators import EnhancedProjectGenerator

def test_react_project_generation():
    """Test React project generation"""
    print("🧪 Testing React Project Generation...")
    
    generator = EnhancedProjectGenerator()
    
    with tempfile.TemporaryDirectory() as temp_dir:
        result = generator.generate_project(
            project_type="react_app",
            project_name="test-react-app",
            project_path=temp_dir,
            requirements={"name": "Test React App", "description": "A test React application"}
        )
        
        if result['success']:
            print(f"   ✅ React project generated successfully")
            print(f"   📁 Project path: {result['project_path']}")
            print(f"   📄 Generated {len(result['generated_files'])} files")
            print(f"   🎯 Features: {', '.join(result['features'])}")
            return True
        else:
            print(f"   ❌ React project generation failed: {result.get('error', 'Unknown error')}")
            return False

def test_nodejs_project_generation():
    """Test Node.js project generation"""
    print("🧪 Testing Node.js Project Generation...")
    
    generator = EnhancedProjectGenerator()
    
    with tempfile.TemporaryDirectory() as temp_dir:
        result = generator.generate_project(
            project_type="nodejs_api",
            project_name="test-nodejs-api",
            project_path=temp_dir,
            requirements={"name": "Test Node.js API", "description": "A test Node.js API"}
        )
        
        if result['success']:
            print(f"   ✅ Node.js project generated successfully")
            print(f"   📁 Project path: {result['project_path']}")
            print(f"   📄 Generated {len(result['generated_files'])} files")
            print(f"   🎯 Features: {', '.join(result['features'])}")
            return True
        else:
            print(f"   ❌ Node.js project generation failed: {result.get('error', 'Unknown error')}")
            return False

def test_fullstack_project_generation():
    """Test Full-stack project generation"""
    print("🧪 Testing Full-stack Project Generation...")
    
    generator = EnhancedProjectGenerator()
    
    with tempfile.TemporaryDirectory() as temp_dir:
        result = generator.generate_project(
            project_type="fullstack_react_node",
            project_name="test-fullstack-app",
            project_path=temp_dir,
            requirements={"name": "Test Full-stack App", "description": "A test full-stack application"}
        )
        
        if result['success']:
            print(f"   ✅ Full-stack project generated successfully")
            print(f"   📁 Project path: {result['project_path']}")
            print(f"   📄 Generated {len(result['generated_files'])} files")
            print(f"   🎯 Features: {', '.join(result['features'])}")
            return True
        else:
            print(f"   ❌ Full-stack project generation failed: {result.get('error', 'Unknown error')}")
            return False

def main():
    """Main test function"""
    print("🚀 Testing Enhanced Project Generators")
    print("=" * 50)
    
    tests = [
        test_react_project_generation,
        test_nodejs_project_generation,
        test_fullstack_project_generation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"   ❌ Test failed with exception: {e}")
            print()
    
    print("=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Enhanced project generators are working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
