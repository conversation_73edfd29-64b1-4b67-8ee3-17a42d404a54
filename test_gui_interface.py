#!/usr/bin/env python3
"""
Test script for the modern GUI interface
"""

import sys
import os
import time
import threading
from pathlib import Path

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_gui_interface():
    """Test the GUI interface initialization"""
    print("🧪 Testing Modern GUI Interface...")
    
    try:
        from gui_interface import ModernGUIInterface
        
        # Create interface instance
        gui = ModernGUIInterface(host="localhost", port=5000)
        
        print("   ✅ GUI Interface initialized successfully")
        print("   🌐 Interface available at: http://localhost:5000")
        print("   📱 Features:")
        print("      - Dual-pane layout (Communication + Project View)")
        print("      - Real-time WebSocket communication")
        print("      - Resizable panels")
        print("      - File explorer")
        print("      - Live project preview")
        print("      - Modern responsive design")
        
        # Test in a separate thread to avoid blocking
        def run_server():
            try:
                gui.run(debug=False)
            except Exception as e:
                print(f"   ⚠️  Server error: {e}")
        
        server_thread = threading.Thread(target=run_server, daemon=True)
        server_thread.start()
        
        # Give server time to start
        time.sleep(2)
        
        print("\n   🚀 GUI Interface is running!")
        print("   📋 To test:")
        print("      1. Open http://localhost:5000 in your browser")
        print("      2. Try sending a message in the chat")
        print("      3. Click 'New Project' to generate a project")
        print("      4. Observe the dual-pane interface")
        
        # Keep running for demo
        try:
            print("\n   ⏳ Press Ctrl+C to stop the server...")
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n   🛑 Stopping GUI Interface...")
            return True
            
    except ImportError as e:
        print(f"   ❌ Import error: {e}")
        print("   💡 Make sure Flask and Flask-SocketIO are installed:")
        print("      pip install flask flask-socketio flask-cors")
        return False
    except Exception as e:
        print(f"   ❌ GUI Interface test failed: {e}")
        return False

def check_dependencies():
    """Check if required dependencies are installed"""
    print("🔍 Checking Dependencies...")
    
    required_packages = [
        'flask',
        'flask_socketio',
        'flask_cors'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"   ✅ {package}")
        except ImportError:
            print(f"   ❌ {package} (missing)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n   📦 Install missing packages:")
        print(f"      pip install {' '.join(missing_packages)}")
        return False
    
    print("   🎉 All dependencies are installed!")
    return True

def check_ui_files():
    """Check if UI files exist"""
    print("📁 Checking UI Files...")
    
    required_files = [
        'ui/templates/index.html',
        'ui/static/css/main.css',
        'ui/static/js/main.js'
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path} (missing)")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n   ⚠️  Missing UI files. They should have been created automatically.")
        return False
    
    print("   🎉 All UI files are present!")
    return True

def main():
    """Main test function"""
    print("🚀 Testing Modern GUI Interface")
    print("=" * 50)
    
    # Check dependencies first
    if not check_dependencies():
        print("\n❌ Dependency check failed. Please install missing packages.")
        return False
    
    print()
    
    # Check UI files
    if not check_ui_files():
        print("\n❌ UI files check failed.")
        return False
    
    print()
    
    # Test GUI interface
    success = test_gui_interface()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 GUI Interface test completed successfully!")
        print("💡 The modern dual-pane interface is ready for use.")
    else:
        print("⚠️  GUI Interface test encountered issues.")
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n🛑 Test interrupted by user")
        sys.exit(0)
