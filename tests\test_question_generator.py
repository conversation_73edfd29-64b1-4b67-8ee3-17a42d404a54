# AI Coding Agent - Question Generator Tests
"""
Tests for the QuestionGenerator module
"""

import pytest
from unittest.mock import Mo<PERSON>, patch

from src.question_generator import (
    QuestionGenerator, Question, QuestionType
)
from src.requirements_parser import (
    ProjectRequirement, UserProfile, ProjectType, TechnicalLevel
)


class TestQuestionGenerator:
    """Test cases for QuestionGenerator"""
    
    def setup_method(self):
        """Set up test fixtures"""
        # Mock LLM manager to avoid actual LLM calls in tests
        self.mock_llm_manager = Mock()
        self.generator = QuestionGenerator(llm_manager=self.mock_llm_manager)
    
    def test_initialization(self):
        """Test QuestionGenerator initialization"""
        assert self.generator is not None
        assert self.generator.llm_manager is not None
        assert isinstance(self.generator.question_frameworks, dict)
        assert isinstance(self.generator.context_patterns, dict)
    
    def test_analyze_missing_information(self):
        """Test analysis of missing information in requirements"""
        # Complete requirements
        complete_req = ProjectRequirement(
            title="Complete Project",
            features=["login", "dashboard"],
            technologies=["python", "react"],
            user_stories=["As a user, I want to login"],
            technical_requirements={"database": "postgresql", "authentication": "jwt"},
            constraints=["mobile-friendly"]
        )
        
        missing_info = self.generator._analyze_missing_information(complete_req)
        assert not missing_info['features']
        assert not missing_info['technologies']
        assert not missing_info['user_stories']
        
        # Incomplete requirements
        incomplete_req = ProjectRequirement()
        missing_info = self.generator._analyze_missing_information(incomplete_req)
        assert missing_info['features']
        assert missing_info['technologies']
        assert missing_info['user_stories']
    
    def test_pattern_question_generation_web_app(self):
        """Test pattern-based question generation for web apps"""
        requirements = ProjectRequirement(
            project_type=ProjectType.WEB_APP,
            features=[],  # Missing features
            user_stories=[]  # Missing user stories
        )
        
        user_profile = UserProfile(technical_level=TechnicalLevel.INTERMEDIATE)
        missing_info = self.generator._analyze_missing_information(requirements)
        
        questions = self.generator._generate_pattern_questions(requirements, user_profile, missing_info)
        
        assert len(questions) > 0
        # Should have questions about users and authentication for web apps
        question_texts = [q.text.lower() for q in questions]
        assert any('user' in text for text in question_texts)
    
    def test_pattern_question_generation_api(self):
        """Test pattern-based question generation for APIs"""
        requirements = ProjectRequirement(
            project_type=ProjectType.API,
            technical_requirements={}  # Missing technical requirements
        )
        
        user_profile = UserProfile(technical_level=TechnicalLevel.ADVANCED)
        missing_info = self.generator._analyze_missing_information(requirements)
        
        questions = self.generator._generate_pattern_questions(requirements, user_profile, missing_info)
        
        assert len(questions) > 0
        # Should have questions about data and API usage
        question_texts = [q.text.lower() for q in questions]
        assert any('data' in text or 'api' in text for text in question_texts)
    
    def test_level_specific_questions_beginner(self):
        """Test questions specific to beginner users"""
        requirements = ProjectRequirement(features=[])
        user_profile = UserProfile(technical_level=TechnicalLevel.BEGINNER)
        
        questions = self.generator._generate_level_specific_questions(requirements, user_profile)
        
        assert len(questions) > 0
        # Beginner questions should be encouraging and simple
        for question in questions:
            assert question.technical_level == TechnicalLevel.BEGINNER
            # Should avoid complex technical terms
            assert 'architecture' not in question.text.lower()
            assert 'microservices' not in question.text.lower()
    
    def test_level_specific_questions_expert(self):
        """Test questions specific to expert users"""
        requirements = ProjectRequirement(technical_requirements={})
        user_profile = UserProfile(technical_level=TechnicalLevel.EXPERT)
        
        questions = self.generator._generate_level_specific_questions(requirements, user_profile)
        
        assert len(questions) > 0
        # Expert questions should be technical and detailed
        for question in questions:
            assert question.technical_level == TechnicalLevel.EXPERT
        
        # Should include technical terms
        question_texts = [q.text.lower() for q in questions]
        assert any('architecture' in text or 'technical' in text for text in question_texts)
    
    def test_question_prioritization(self):
        """Test question prioritization logic"""
        # Create test questions with different priorities
        questions = [
            Question(
                id="q1",
                text="Low priority question",
                question_type=QuestionType.CONSTRAINT_IDENTIFICATION,
                context="test",
                follow_up_hints=[],
                technical_level=TechnicalLevel.INTERMEDIATE,
                priority=3,
                metadata={}
            ),
            Question(
                id="q2",
                text="High priority question",
                question_type=QuestionType.FEATURE_CLARIFICATION,
                context="test",
                follow_up_hints=[],
                technical_level=TechnicalLevel.INTERMEDIATE,
                priority=8,
                metadata={}
            )
        ]
        
        requirements = ProjectRequirement(features=[])  # Missing features
        user_profile = UserProfile(technical_level=TechnicalLevel.INTERMEDIATE)
        
        prioritized = self.generator._prioritize_questions(questions, requirements, user_profile)
        
        # Should be sorted by priority (highest first)
        assert prioritized[0].priority >= prioritized[1].priority
        # Feature clarification should get priority boost due to missing features
        feature_question = next(q for q in prioritized if q.question_type == QuestionType.FEATURE_CLARIFICATION)
        assert feature_question.priority > 8  # Should be boosted
    
    def test_question_selection_variety(self):
        """Test that question selection ensures variety"""
        # Create multiple questions of the same type
        questions = [
            Question(
                id=f"q{i}",
                text=f"Question {i}",
                question_type=QuestionType.FEATURE_CLARIFICATION,
                context="test",
                follow_up_hints=[],
                technical_level=TechnicalLevel.INTERMEDIATE,
                priority=5 + i,
                metadata={}
            ) for i in range(5)
        ]
        
        # Add one question of different type
        questions.append(Question(
            id="q_different",
            text="Different type question",
            question_type=QuestionType.USER_EXPERIENCE,
            context="test",
            follow_up_hints=[],
            technical_level=TechnicalLevel.INTERMEDIATE,
            priority=1,  # Lower priority
            metadata={}
        ))
        
        selected = self.generator._select_best_questions(questions, 3)
        
        assert len(selected) <= 3
        # Should include the different type question despite lower priority
        question_types = [q.question_type for q in selected]
        assert QuestionType.USER_EXPERIENCE in question_types
    
    def test_similar_question_detection(self):
        """Test detection of similar questions"""
        question1 = Question(
            id="q1",
            text="What features do you want in your application?",
            question_type=QuestionType.FEATURE_CLARIFICATION,
            context="test",
            follow_up_hints=[],
            technical_level=TechnicalLevel.INTERMEDIATE,
            priority=5,
            metadata={}
        )
        
        question2 = Question(
            id="q2",
            text="What features would you like in your app?",
            question_type=QuestionType.FEATURE_CLARIFICATION,
            context="test",
            follow_up_hints=[],
            technical_level=TechnicalLevel.INTERMEDIATE,
            priority=5,
            metadata={}
        )
        
        question3 = Question(
            id="q3",
            text="Who will be using this system?",
            question_type=QuestionType.USER_EXPERIENCE,
            context="test",
            follow_up_hints=[],
            technical_level=TechnicalLevel.INTERMEDIATE,
            priority=5,
            metadata={}
        )
        
        # Similar questions should be detected
        assert self.generator._is_similar_question(question2, [question1])
        
        # Different questions should not be detected as similar
        assert not self.generator._is_similar_question(question3, [question1])
    
    def test_llm_question_parsing(self):
        """Test parsing of LLM-generated questions"""
        llm_response = """
Q: What's the main goal you want to achieve with this project?
Type: scope_definition
Priority: 8
Hint: Think about the problem you're solving

Q: Who do you imagine using this application?
Type: user_experience
Priority: 7
Hint: Consider your target audience
"""
        
        questions = self.generator._parse_llm_questions(llm_response, TechnicalLevel.INTERMEDIATE)
        
        assert len(questions) == 2
        assert questions[0].text == "What's the main goal you want to achieve with this project?"
        assert questions[0].question_type == QuestionType.SCOPE_DEFINITION
        assert questions[0].priority == 8
        assert "Think about the problem you're solving" in questions[0].follow_up_hints
    
    def test_fallback_questions(self):
        """Test fallback question generation"""
        requirements = ProjectRequirement()
        user_profile = UserProfile()
        
        questions = self.generator._generate_fallback_questions(requirements, user_profile, 3)
        
        assert len(questions) <= 3
        assert all(isinstance(q, Question) for q in questions)
        # Should cover basic question types
        question_types = [q.question_type for q in questions]
        assert QuestionType.SCOPE_DEFINITION in question_types
    
    def test_generate_questions_integration(self):
        """Test full question generation integration"""
        # Mock LLM response
        mock_llm_response = """
Q: What's the main purpose of your web application?
Type: scope_definition
Priority: 8
Hint: Think about the core problem it solves
"""
        
        self.mock_llm_manager.generate_response_with_context.return_value = mock_llm_response
        
        requirements = ProjectRequirement(
            project_type=ProjectType.WEB_APP,
            features=[],
            user_stories=[]
        )
        
        user_profile = UserProfile(technical_level=TechnicalLevel.INTERMEDIATE)
        
        questions = self.generator.generate_questions(requirements, user_profile, max_questions=3)
        
        assert len(questions) > 0
        assert len(questions) <= 3
        assert all(isinstance(q, Question) for q in questions)
        
        # Should include both LLM and pattern-generated questions
        sources = [q.metadata.get('source') for q in questions]
        assert 'llm' in sources or 'pattern' in sources
    
    def test_error_handling_in_generation(self):
        """Test error handling when LLM fails"""
        # Mock LLM to raise an exception
        self.mock_llm_manager.generate_response_with_context.side_effect = Exception("LLM Error")
        
        requirements = ProjectRequirement(project_type=ProjectType.WEB_APP)
        user_profile = UserProfile()
        
        # Should not raise exception, should return fallback questions
        questions = self.generator.generate_questions(requirements, user_profile)
        
        assert len(questions) > 0
        assert all(isinstance(q, Question) for q in questions)


class TestQuestion:
    """Test cases for Question data class"""
    
    def test_question_creation(self):
        """Test Question creation"""
        question = Question(
            id="test_q",
            text="Test question?",
            question_type=QuestionType.FEATURE_CLARIFICATION,
            context="test context",
            follow_up_hints=["hint1", "hint2"],
            technical_level=TechnicalLevel.INTERMEDIATE,
            priority=7,
            metadata={"source": "test"}
        )
        
        assert question.id == "test_q"
        assert question.text == "Test question?"
        assert question.question_type == QuestionType.FEATURE_CLARIFICATION
        assert question.priority == 7
        assert len(question.follow_up_hints) == 2
