# AI Coding Agent - Personality System Tests
"""
Tests for the PersonalitySystem module
"""

import pytest
from unittest.mock import <PERSON><PERSON>
from datetime import datetime, timedelta

from src.personality_system import (
    PersonalitySystem, PersonalityProfile, ConversationMemory,
    PersonalityTrait, MoodState
)


class TestPersonalitySystem:
    """Test cases for PersonalitySystem"""
    
    def setup_method(self):
        """Set up test fixtures"""
        # Mock database and conversation managers
        self.mock_db = Mock()
        self.mock_conversation_manager = Mock()
        
        self.system = PersonalitySystem(
            database_manager=self.mock_db,
            conversation_manager=self.mock_conversation_manager
        )
    
    def test_initialization(self):
        """Test PersonalitySystem initialization"""
        assert self.system is not None
        assert isinstance(self.system.personality, PersonalityProfile)
        assert self.system.personality.name == "Alex"
        assert isinstance(self.system.conversation_memories, dict)
        assert isinstance(self.system.response_generators, dict)
    
    def test_get_or_create_memory_new_user(self):
        """Test creating new conversation memory for new user"""
        user_id = "test_user_123"
        
        memory = self.system._get_or_create_memory(user_id)
        
        assert isinstance(memory, ConversationMemory)
        assert memory.user_id == user_id
        assert memory.interaction_count == 0
        assert user_id in self.system.conversation_memories
    
    def test_get_or_create_memory_existing_user(self):
        """Test retrieving existing conversation memory"""
        user_id = "test_user_123"
        
        # Create memory first time
        memory1 = self.system._get_or_create_memory(user_id)
        memory1.interaction_count = 5
        
        # Get memory second time
        memory2 = self.system._get_or_create_memory(user_id)
        
        assert memory1 is memory2
        assert memory2.interaction_count == 5
    
    def test_determine_personality_adjustments_new_user(self):
        """Test personality adjustments for new user"""
        memory = ConversationMemory(user_id="test", interaction_count=1)
        
        adjustments = self.system._determine_personality_adjustments(memory)
        
        # New users should get increased helpfulness and patience
        assert adjustments["helpfulness"] > self.system.personality.core_traits[PersonalityTrait.HELPFULNESS]
        assert adjustments["patience"] > self.system.personality.core_traits[PersonalityTrait.PATIENCE]
    
    def test_determine_personality_adjustments_formal_preference(self):
        """Test personality adjustments for formal communication preference"""
        memory = ConversationMemory(user_id="test", interaction_count=5)
        memory.user_preferences["communication_style"] = {
            "value": "formal",
            "timestamp": datetime.now(),
            "importance": 0.8
        }
        
        adjustments = self.system._determine_personality_adjustments(memory)
        
        # Formal preference should increase professionalism and decrease enthusiasm
        assert adjustments["professionalism"] > self.system.personality.core_traits[PersonalityTrait.PROFESSIONALISM]
        assert adjustments["enthusiasm"] < self.system.personality.core_traits[PersonalityTrait.ENTHUSIASM]
    
    def test_determine_personality_adjustments_frustrated_context(self):
        """Test personality adjustments when user is frustrated"""
        memory = ConversationMemory(user_id="test", interaction_count=3)
        context = {"user_frustrated": True}
        
        adjustments = self.system._determine_personality_adjustments(memory, context)
        
        # Frustrated user should get increased patience and encouragement
        assert adjustments["patience"] > self.system.personality.core_traits[PersonalityTrait.PATIENCE]
        assert adjustments["encouragement"] > self.system.personality.core_traits[PersonalityTrait.ENCOURAGEMENT]
    
    def test_add_enthusiasm(self):
        """Test adding enthusiasm to response"""
        response = "This is a good solution."
        enhanced = self.system._add_enthusiasm(response)
        
        # Should either add enthusiastic starter or change punctuation
        assert enhanced != response
        assert ("excited" in enhanced.lower() or "great" in enhanced.lower() or 
                "love" in enhanced.lower() or enhanced.endswith("!"))
    
    def test_add_encouragement(self):
        """Test adding encouragement to response"""
        response = "Here's how to solve this problem."
        
        # Run multiple times since it's probabilistic
        enhanced_responses = [self.system._add_encouragement(response) for _ in range(10)]
        
        # At least some should be enhanced with encouragement
        enhanced_count = sum(1 for resp in enhanced_responses if resp != response)
        assert enhanced_count > 0
        
        # Check that encouragement was actually added
        encouraging_phrases = ["great", "smart", "right way", "getting the hang"]
        enhanced_with_encouragement = [resp for resp in enhanced_responses 
                                     if any(phrase in resp.lower() for phrase in encouraging_phrases)]
        assert len(enhanced_with_encouragement) > 0
    
    def test_make_more_professional(self):
        """Test making response more professional"""
        response = "I'm excited! You're awesome and this is really cool!!"
        professional = self.system._make_more_professional(response)
        
        assert "I am" in professional
        assert "you are" in professional
        assert "!!" not in professional
        assert "excellent" in professional or "effective" in professional
    
    def test_make_more_casual(self):
        """Test making response more casual"""
        response = "I am pleased to inform you that this is an excellent solution."
        casual = self.system._make_more_casual(response)
        
        # Should have casual contractions and language
        assert ("I'm" in casual or "you're" in casual or 
                "awesome" in casual or "cool" in casual)
    
    def test_generate_greeting_first_meeting(self):
        """Test greeting generation for first meeting"""
        user_id = "new_user"
        
        greeting = self.system.generate_greeting(user_id)
        
        assert "Alex" in greeting  # Should include agent name
        assert any(word in greeting.lower() for word in ["hi", "hello", "hey"])
        assert any(word in greeting.lower() for word in ["excited", "help", "amazing"])
    
    def test_generate_greeting_returning_user(self):
        """Test greeting generation for returning user"""
        user_id = "returning_user"
        
        # Create memory with past interaction
        memory = self.system._get_or_create_memory(user_id)
        memory.interaction_count = 3
        memory.last_interaction = datetime.now() - timedelta(days=10)  # Long break
        
        greeting = self.system.generate_greeting(user_id)
        
        assert any(word in greeting.lower() for word in ["welcome", "back", "again"])
    
    def test_generate_greeting_familiar_user(self):
        """Test greeting generation for familiar user"""
        user_id = "familiar_user"
        
        # Create memory with many interactions
        memory = self.system._get_or_create_memory(user_id)
        memory.interaction_count = 10
        memory.last_interaction = datetime.now() - timedelta(hours=1)  # Recent
        
        greeting = self.system.generate_greeting(user_id)
        
        assert any(word in greeting.lower() for word in ["ready", "tackle", "build"])
    
    def test_remember_user_preference(self):
        """Test remembering user preferences"""
        user_id = "test_user"
        
        self.system.remember_user_preference(user_id, "language", "Python", importance=0.8)
        
        memory = self.system._get_or_create_memory(user_id)
        assert "language" in memory.user_preferences
        assert memory.user_preferences["language"]["value"] == "Python"
        assert memory.user_preferences["language"]["importance"] == 0.8
        
        # Should also be in long-term memory
        assert len(memory.long_term_memories) > 0
        assert any("Python" in mem["content"] for mem in memory.long_term_memories)
    
    def test_get_conversation_context(self):
        """Test getting conversation context"""
        user_id = "test_user"
        memory = self.system._get_or_create_memory(user_id)
        
        # Add some session memories
        memory.add_session_memory("user_goal", "build a web application")
        memory.add_session_memory("project_context", "e-commerce site")
        
        # Add long-term memory
        memory.add_long_term_memory("user_skill", "intermediate Python developer", importance=0.8)
        
        context = self.system.get_conversation_context(user_id)
        
        assert "web application" in context or "e-commerce" in context
        assert len(context) > 0
    
    def test_get_personalized_response(self):
        """Test full personalized response generation"""
        user_id = "test_user"
        base_response = "Here's how to create an API endpoint."
        context = {"project_type": "web_app", "user_skill_level": "beginner"}
        
        personalized = self.system.get_personalized_response(base_response, user_id, context)
        
        # Should be different from base response (personalized)
        assert personalized != base_response
        assert len(personalized) >= len(base_response)
        
        # Should update interaction count
        memory = self.system._get_or_create_memory(user_id)
        assert memory.interaction_count == 1
        assert memory.last_interaction is not None
    
    def test_get_personalized_response_error_handling(self):
        """Test error handling in personalized response generation"""
        user_id = "test_user"
        base_response = "Test response"
        
        # Force an error by making a method fail
        original_method = self.system._apply_personality_traits
        self.system._apply_personality_traits = Mock(side_effect=Exception("Test error"))
        
        # Should return original response on error
        result = self.system.get_personalized_response(base_response, user_id)
        assert result == base_response
        
        # Restore original method
        self.system._apply_personality_traits = original_method
    
    def test_conversation_memory_session_limit(self):
        """Test that session memories are limited"""
        memory = ConversationMemory(user_id="test")
        
        # Add more than the limit (20)
        for i in range(25):
            memory.add_session_memory("test", f"Memory {i}")
        
        # Should only keep the last 20
        assert len(memory.session_memories) == 20
        assert memory.session_memories[0]["content"] == "Memory 5"  # First kept memory
        assert memory.session_memories[-1]["content"] == "Memory 24"  # Last memory
    
    def test_conversation_memory_long_term_limit(self):
        """Test that long-term memories are limited and sorted by importance"""
        memory = ConversationMemory(user_id="test")
        
        # Add memories with different importance levels
        for i in range(60):
            importance = 0.1 + (i % 10) * 0.1  # Varying importance 0.1 to 1.0
            memory.add_long_term_memory("test", f"Memory {i}", importance)
        
        # Should only keep top 50 by importance
        assert len(memory.long_term_memories) == 50
        
        # Should be sorted by importance (highest first)
        importances = [mem["importance"] for mem in memory.long_term_memories]
        assert importances == sorted(importances, reverse=True)


class TestPersonalityProfile:
    """Test cases for PersonalityProfile"""
    
    def test_personality_profile_creation(self):
        """Test PersonalityProfile creation with defaults"""
        profile = PersonalityProfile()
        
        assert profile.name == "Alex"
        assert len(profile.core_traits) > 0
        assert PersonalityTrait.HELPFULNESS in profile.core_traits
        assert PersonalityTrait.ENTHUSIASM in profile.core_traits
        assert len(profile.communication_preferences) > 0
        assert len(profile.response_patterns) > 0
    
    def test_personality_profile_custom_traits(self):
        """Test PersonalityProfile with custom traits"""
        custom_traits = {
            PersonalityTrait.ENTHUSIASM: 0.9,
            PersonalityTrait.PROFESSIONALISM: 0.3
        }
        
        profile = PersonalityProfile(core_traits=custom_traits)
        
        assert profile.core_traits[PersonalityTrait.ENTHUSIASM] == 0.9
        assert profile.core_traits[PersonalityTrait.PROFESSIONALISM] == 0.3


class TestConversationMemory:
    """Test cases for ConversationMemory"""
    
    def test_conversation_memory_creation(self):
        """Test ConversationMemory creation"""
        memory = ConversationMemory(user_id="test_user")
        
        assert memory.user_id == "test_user"
        assert memory.interaction_count == 0
        assert len(memory.session_memories) == 0
        assert len(memory.long_term_memories) == 0
        assert memory.last_interaction is None
    
    def test_add_session_memory(self):
        """Test adding session memory"""
        memory = ConversationMemory(user_id="test")
        
        memory.add_session_memory("test_type", "test content", {"key": "value"})
        
        assert len(memory.session_memories) == 1
        assert memory.session_memories[0]["type"] == "test_type"
        assert memory.session_memories[0]["content"] == "test content"
        assert memory.session_memories[0]["metadata"]["key"] == "value"
        assert "timestamp" in memory.session_memories[0]
    
    def test_add_long_term_memory(self):
        """Test adding long-term memory"""
        memory = ConversationMemory(user_id="test")
        
        memory.add_long_term_memory("important_fact", "User loves Python", importance=0.9)
        
        assert len(memory.long_term_memories) == 1
        assert memory.long_term_memories[0]["type"] == "important_fact"
        assert memory.long_term_memories[0]["content"] == "User loves Python"
        assert memory.long_term_memories[0]["importance"] == 0.9
        assert memory.long_term_memories[0]["access_count"] == 0
