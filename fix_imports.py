#!/usr/bin/env python3
"""
Script to fix relative imports in the src directory
"""

import os
import re
from pathlib import Path

def fix_relative_imports(file_path):
    """Fix relative imports in a Python file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Pattern to match imports from local modules (without src. prefix)
        # This will match imports like "from logger import" but not "from pathlib import"
        local_modules = [
            'logger', 'config', 'exceptions', 'models', 'database', 'conversation',
            'validators', 'monitoring', 'security', 'requirements_parser',
            'question_generator', 'explanation_system', 'personality_system',
            'dependency_manager', 'database_setup', 'error_monitor',
            'root_cause_analyzer', 'auto_fix_generator', 'error_explainer',
            'error_pattern_learner', 'prototype_orchestrator', 'agent'
        ]

        changes_made = False

        for module in local_modules:
            # Pattern to match imports from local modules
            pattern = rf'from {module} import'
            replacement = f'from src.{module} import'

            if pattern in content and replacement not in content:
                content = content.replace(pattern, replacement)
                changes_made = True

        # Check if any changes were made
        if changes_made:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"Fixed imports in: {file_path}")
            return True
        return False
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False

def main():
    """Main function to fix all relative imports in src directory"""
    src_dir = Path("src")
    
    if not src_dir.exists():
        print("src directory not found!")
        return
    
    fixed_files = 0
    total_files = 0
    
    # Process all Python files in src directory
    for py_file in src_dir.glob("*.py"):
        if py_file.name == "__init__.py":
            continue
            
        total_files += 1
        if fix_relative_imports(py_file):
            fixed_files += 1
    
    print(f"\nProcessed {total_files} files, fixed imports in {fixed_files} files")

if __name__ == "__main__":
    main()
