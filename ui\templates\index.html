<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Coding Agent - Modern Interface</title>
    
    <!-- CSS Framework and Custom Styles -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/main.css') }}" rel="stylesheet">
    
    <!-- Socket.IO for real-time communication -->
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
</head>
<body class="bg-gray-100 h-screen overflow-hidden">
    <!-- Main Container -->
    <div id="app" class="flex flex-col h-screen">
        <!-- Header -->
        <header class="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <i class="fas fa-robot text-blue-600 text-2xl"></i>
                        <h1 class="text-xl font-bold text-gray-800">AI Coding Agent</h1>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div id="connection-status" class="flex items-center space-x-1">
                            <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                            <span class="text-sm text-gray-600">Connected</span>
                        </div>
                    </div>
                </div>
                
                <div class="flex items-center space-x-4">
                    <button id="new-project-btn" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-plus mr-2"></i>New Project
                    </button>
                    <button id="settings-btn" class="text-gray-600 hover:text-gray-800 p-2">
                        <i class="fas fa-cog"></i>
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content Area -->
        <div class="flex flex-1 overflow-hidden">
            <!-- Left Panel - Communication -->
            <div id="communication-panel" class="w-1/2 bg-white border-r border-gray-200 flex flex-col">
                <!-- Chat Header -->
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-800">
                        <i class="fas fa-comments mr-2 text-blue-600"></i>
                        Conversation
                    </h2>
                </div>
                
                <!-- Messages Area -->
                <div id="messages-container" class="flex-1 overflow-y-auto p-6 space-y-4">
                    <!-- Welcome message -->
                    <div class="message assistant-message">
                        <div class="flex items-start space-x-3">
                            <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-robot text-white text-sm"></i>
                            </div>
                            <div class="flex-1">
                                <div class="bg-gray-100 rounded-lg p-3">
                                    <p class="text-gray-800">
                                        👋 Hello! I'm your AI Coding Agent. I can help you create web applications, APIs, and mobile apps using modern frameworks like React, Vue, Angular, Node.js, and more.
                                    </p>
                                    <p class="text-gray-800 mt-2">
                                        What would you like to build today?
                                    </p>
                                </div>
                                <span class="text-xs text-gray-500 mt-1 block">Just now</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Message Input -->
                <div class="border-t border-gray-200 p-6">
                    <div class="flex space-x-4">
                        <div class="flex-1">
                            <textarea 
                                id="message-input" 
                                placeholder="Describe what you want to build..."
                                class="w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                rows="3"
                            ></textarea>
                        </div>
                        <div class="flex flex-col space-y-2">
                            <button id="send-btn" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                            <button id="voice-btn" class="bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-3 rounded-lg transition-colors">
                                <i class="fas fa-microphone"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Resize Handle -->
            <div id="resize-handle" class="w-1 bg-gray-300 hover:bg-blue-500 cursor-col-resize transition-colors"></div>

            <!-- Right Panel - Live Project View -->
            <div id="project-panel" class="flex-1 bg-gray-50 flex flex-col">
                <!-- Project Header -->
                <div class="px-6 py-4 bg-white border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h2 class="text-lg font-semibold text-gray-800">
                            <i class="fas fa-code mr-2 text-green-600"></i>
                            Live Project View
                        </h2>
                        <div class="flex items-center space-x-2">
                            <div id="project-status" class="flex items-center space-x-2">
                                <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
                                <span class="text-sm text-gray-600">No Project</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Project Content -->
                <div class="flex-1 flex">
                    <!-- File Explorer -->
                    <div id="file-explorer" class="w-64 bg-white border-r border-gray-200 overflow-y-auto">
                        <div class="p-4">
                            <h3 class="text-sm font-semibold text-gray-700 mb-3">
                                <i class="fas fa-folder mr-2"></i>Files
                            </h3>
                            <div id="file-tree" class="text-sm">
                                <div class="text-gray-500 italic">No project loaded</div>
                            </div>
                        </div>
                    </div>

                    <!-- Main Preview Area -->
                    <div class="flex-1 flex flex-col">
                        <!-- Tabs -->
                        <div id="preview-tabs" class="bg-white border-b border-gray-200 px-4">
                            <div class="flex space-x-1">
                                <button class="tab-btn active px-4 py-2 text-sm font-medium text-blue-600 border-b-2 border-blue-600">
                                    <i class="fas fa-eye mr-2"></i>Preview
                                </button>
                                <button class="tab-btn px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-800">
                                    <i class="fas fa-code mr-2"></i>Code
                                </button>
                                <button class="tab-btn px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-800">
                                    <i class="fas fa-terminal mr-2"></i>Terminal
                                </button>
                            </div>
                        </div>

                        <!-- Preview Content -->
                        <div id="preview-content" class="flex-1 bg-white">
                            <div class="h-full flex items-center justify-center text-gray-500">
                                <div class="text-center">
                                    <i class="fas fa-rocket text-6xl mb-4 text-gray-300"></i>
                                    <h3 class="text-xl font-semibold mb-2">Ready to Build</h3>
                                    <p>Start a conversation to generate your first project</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden z-50">
        <div class="bg-white rounded-lg p-8 max-w-sm w-full mx-4">
            <div class="text-center">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <h3 class="text-lg font-semibold mb-2">Generating Project</h3>
                <p class="text-gray-600">Please wait while I create your application...</p>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>
