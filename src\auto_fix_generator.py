# AI Coding Agent - Automatic Fix Generation
"""
AI-powered system for generating and applying automatic fixes for common errors
"""

import re
import ast
import json
import tempfile
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime

from src.logger import get_logger
from src.error_monitor import ErrorEvent
from src.root_cause_analyzer import RootCause
from src.models import LLMManager
from src.validators import CodeValidator

@dataclass
class FixSuggestion:
    """Represents an automatic fix suggestion"""
    fix_id: str
    error_id: str
    fix_type: str  # 'syntax', 'import', 'logic', 'style'
    confidence: float  # 0.0 to 1.0
    description: str
    original_code: str
    fixed_code: str
    explanation: str
    validation_result: bool
    side_effects: List[str]
    backup_required: bool

@dataclass
class FixResult:
    """Result of applying a fix"""
    success: bool
    fix_id: str
    applied_changes: List[str]
    validation_passed: bool
    error_message: Optional[str] = None
    backup_path: Optional[str] = None

class AutoFixGenerator:
    """
    AI-powered system that generates and applies automatic fixes for errors
    """

    def __init__(self):
        """Initialize auto fix generator"""
        self.logger = get_logger(__name__)
        self.llm_manager = LLMManager()
        self.validator = CodeValidator()

        # Fix templates for common errors
        self.fix_templates = {
            'python': {
                'SyntaxError': {
                    'missing_colon': {
                        'pattern': r'(if|elif|else|for|while|def|class|try|except|finally|with)\s+[^:]*$',
                        'fix': lambda match: match.group(0) + ':',
                        'confidence': 0.9
                    },
                    'missing_parentheses': {
                        'pattern': r'print\s+([^(].*)',
                        'fix': lambda match: f'print({match.group(1)})',
                        'confidence': 0.85
                    },
                    'indentation_error': {
                        'pattern': r'^(\s*)(.*)',
                        'fix': self._fix_indentation,
                        'confidence': 0.7
                    }
                },
                'ImportError': {
                    'missing_import': {
                        'pattern': r"No module named '([^']+)'",
                        'fix': self._generate_import_fix,
                        'confidence': 0.8
                    }
                },
                'NameError': {
                    'undefined_variable': {
                        'pattern': r"name '([^']+)' is not defined",
                        'fix': self._generate_variable_fix,
                        'confidence': 0.6
                    }
                }
            },
            'javascript': {
                'SyntaxError': {
                    'missing_semicolon': {
                        'pattern': r'([^;])\s*\n',
                        'fix': lambda match: match.group(1) + ';\n',
                        'confidence': 0.7
                    },
                    'missing_var_declaration': {
                        'pattern': r'^(\s*)([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=',
                        'fix': lambda match: f'{match.group(1)}var {match.group(2)} =',
                        'confidence': 0.6
                    }
                }
            }
        }

        # Common package mappings for import fixes
        self.package_mappings = {
            'requests': 'requests',
            'numpy': 'numpy',
            'pandas': 'pandas',
            'matplotlib': 'matplotlib',
            'flask': 'flask',
            'django': 'django',
            'sqlite3': None,  # Built-in module
            'json': None,  # Built-in module
            'os': None,  # Built-in module
            'sys': None,  # Built-in module
        }

        self.logger.info("AutoFixGenerator initialized")

    def generate_fix(self, error: ErrorEvent, root_cause: RootCause,
                    code_context: Optional[str] = None) -> List[FixSuggestion]:
        """
        Generate automatic fix suggestions for an error

        Args:
            error: Error event to fix
            root_cause: Root cause analysis result
            code_context: Optional code context around the error

        Returns:
            List of fix suggestions
        """
        self.logger.info(f"Generating fixes for error: {error.error_type}")

        fixes = []

        # Try template-based fixes first
        template_fixes = self._generate_template_fixes(error, root_cause, code_context)
        fixes.extend(template_fixes)

        # Try AI-powered fixes for complex issues
        if not fixes or max(f.confidence for f in fixes) < 0.8:
            ai_fixes = self._generate_ai_fixes(error, root_cause, code_context)
            fixes.extend(ai_fixes)

        # Sort by confidence
        fixes.sort(key=lambda f: f.confidence, reverse=True)

        self.logger.info(f"Generated {len(fixes)} fix suggestions")
        return fixes[:3]  # Return top 3 fixes

    def apply_fix(self, fix: FixSuggestion, file_path: str,
                  create_backup: bool = True) -> FixResult:
        """
        Apply a fix to a file

        Args:
            fix: Fix suggestion to apply
            file_path: Path to the file to fix
            create_backup: Whether to create a backup before applying

        Returns:
            Result of applying the fix
        """
        self.logger.info(f"Applying fix {fix.fix_id} to {file_path}")

        try:
            path = Path(file_path)
            if not path.exists():
                return FixResult(
                    success=False,
                    fix_id=fix.fix_id,
                    applied_changes=[],
                    validation_passed=False,
                    error_message=f"File not found: {file_path}"
                )

            # Create backup if requested
            backup_path = None
            if create_backup:
                backup_path = self._create_backup(path)

            # Read original content
            with open(path, 'r', encoding='utf-8') as f:
                original_content = f.read()

            # Apply the fix
            fixed_content = self._apply_fix_to_content(original_content, fix)

            # Validate the fix
            validation_passed = self._validate_fix(fixed_content, path.suffix)

            if validation_passed or fix.confidence > 0.9:
                # Write the fixed content
                with open(path, 'w', encoding='utf-8') as f:
                    f.write(fixed_content)

                return FixResult(
                    success=True,
                    fix_id=fix.fix_id,
                    applied_changes=[f"Applied {fix.fix_type} fix"],
                    validation_passed=validation_passed,
                    backup_path=str(backup_path) if backup_path else None
                )
            else:
                return FixResult(
                    success=False,
                    fix_id=fix.fix_id,
                    applied_changes=[],
                    validation_passed=False,
                    error_message="Fix validation failed",
                    backup_path=str(backup_path) if backup_path else None
                )

        except Exception as e:
            self.logger.error(f"Error applying fix: {e}")
            return FixResult(
                success=False,
                fix_id=fix.fix_id,
                applied_changes=[],
                validation_passed=False,
                error_message=str(e)
            )

    def _generate_template_fixes(self, error: ErrorEvent, root_cause: RootCause,
                               code_context: Optional[str]) -> List[FixSuggestion]:
        """Generate fixes using predefined templates"""
        fixes = []

        # Determine language
        language = self._get_language_from_error(error)
        if language not in self.fix_templates:
            return fixes

        error_templates = self.fix_templates[language].get(error.error_type, {})

        for fix_name, template in error_templates.items():
            try:
                # Check if pattern matches
                pattern = template['pattern']
                if isinstance(template['fix'], str):
                    # Simple string replacement
                    if re.search(pattern, error.message):
                        fix = self._create_fix_from_template(error, template, fix_name, code_context)
                        if fix:
                            fixes.append(fix)
                else:
                    # Function-based fix
                    match = re.search(pattern, error.message)
                    if match:
                        fix = self._create_fix_from_function(error, template, fix_name, match, code_context)
                        if fix:
                            fixes.append(fix)

            except Exception as e:
                self.logger.warning(f"Error applying template {fix_name}: {e}")

        return fixes

    def _generate_ai_fixes(self, error: ErrorEvent, root_cause: RootCause,
                          code_context: Optional[str]) -> List[FixSuggestion]:
        """Generate fixes using AI/LLM"""
        fixes = []

        try:
            # Prepare prompt for LLM
            prompt = self._build_fix_prompt(error, root_cause, code_context)

            # Use debugger model for fix generation
            llm = self.llm_manager.get_model('debugger')
            if not llm:
                self.logger.warning("Debugger model not available for AI fixes")
                return fixes

            # Generate fix using LLM
            response = llm(prompt)

            # Parse the response and create fix suggestion
            fix = self._parse_ai_fix_response(error, response)
            if fix:
                fixes.append(fix)

        except Exception as e:
            self.logger.error(f"Error generating AI fix: {e}")

        return fixes

    def _get_language_from_error(self, error: ErrorEvent) -> str:
        """Determine programming language from error"""
        if error.file_path:
            path = Path(error.file_path)
            extension = path.suffix.lower()

            language_map = {
                '.py': 'python',
                '.js': 'javascript',
                '.html': 'html',
                '.css': 'css'
            }

            return language_map.get(extension, 'unknown')

        # Infer from error type
        if any(keyword in error.error_type for keyword in ['Python', 'Import', 'Name', 'Type']):
            return 'python'
        elif any(keyword in error.error_type for keyword in ['JavaScript', 'Reference']):
            return 'javascript'

        return 'unknown'

    def _create_fix_from_template(self, error: ErrorEvent, template: Dict[str, Any],
                                fix_name: str, code_context: Optional[str]) -> Optional[FixSuggestion]:
        """Create fix suggestion from template"""
        try:
            import uuid

            # For simple template fixes, we need the actual code to fix
            if not code_context:
                return None

            fixed_code = re.sub(template['pattern'], template['fix'], code_context)

            return FixSuggestion(
                fix_id=str(uuid.uuid4()),
                error_id=error.id,
                fix_type='template',
                confidence=template['confidence'],
                description=f"Template fix: {fix_name}",
                original_code=code_context,
                fixed_code=fixed_code,
                explanation=f"Applied {fix_name} template fix",
                validation_result=False,  # Will be validated later
                side_effects=[],
                backup_required=True
            )

        except Exception as e:
            self.logger.error(f"Error creating template fix: {e}")
            return None

    def _create_fix_from_function(self, error: ErrorEvent, template: Dict[str, Any],
                                fix_name: str, match: re.Match,
                                code_context: Optional[str]) -> Optional[FixSuggestion]:
        """Create fix suggestion from function-based template"""
        try:
            import uuid

            # Call the fix function
            fix_function = template['fix']
            if callable(fix_function):
                if fix_name == 'missing_import':
                    fixed_code = self._generate_import_fix(match, code_context)
                elif fix_name == 'undefined_variable':
                    fixed_code = self._generate_variable_fix(match, code_context)
                elif fix_name == 'indentation_error':
                    fixed_code = self._fix_indentation(match, code_context)
                else:
                    fixed_code = fix_function(match)

                if fixed_code and fixed_code != code_context:
                    return FixSuggestion(
                        fix_id=str(uuid.uuid4()),
                        error_id=error.id,
                        fix_type='function',
                        confidence=template['confidence'],
                        description=f"Function fix: {fix_name}",
                        original_code=code_context or "",
                        fixed_code=fixed_code,
                        explanation=f"Applied {fix_name} function fix",
                        validation_result=False,
                        side_effects=[],
                        backup_required=True
                    )

        except Exception as e:
            self.logger.error(f"Error creating function fix: {e}")

        return None

    def _generate_import_fix(self, match: re.Match, code_context: Optional[str]) -> str:
        """Generate fix for missing import"""
        if not code_context:
            return ""

        module_name = match.group(1) if match.groups() else ""
        if not module_name:
            return code_context

        # Check if it's a known package
        if module_name in self.package_mappings:
            package = self.package_mappings[module_name]
            if package is None:  # Built-in module
                import_line = f"import {module_name}"
            else:
                import_line = f"import {package}"

            # Add import at the top of the file
            lines = code_context.split('\n')

            # Find the right place to insert import (after existing imports)
            insert_index = 0
            for i, line in enumerate(lines):
                if line.strip().startswith('import ') or line.strip().startswith('from '):
                    insert_index = i + 1
                elif line.strip() and not line.strip().startswith('#'):
                    break

            lines.insert(insert_index, import_line)
            return '\n'.join(lines)

        return code_context

    def _generate_variable_fix(self, match: re.Match, code_context: Optional[str]) -> str:
        """Generate fix for undefined variable"""
        if not code_context:
            return ""

        var_name = match.group(1) if match.groups() else ""
        if not var_name:
            return code_context

        # Simple fix: add variable definition
        lines = code_context.split('\n')

        # Find the line with the error and add definition before it
        for i, line in enumerate(lines):
            if var_name in line and '=' not in line.split(var_name)[0]:
                # Add variable definition before this line
                indent = len(line) - len(line.lstrip())
                definition = ' ' * indent + f"{var_name} = None  # TODO: Define this variable"
                lines.insert(i, definition)
                break

        return '\n'.join(lines)

    def _fix_indentation(self, match: re.Match, code_context: Optional[str]) -> str:
        """Fix indentation errors"""
        if not code_context:
            return ""

        lines = code_context.split('\n')
        fixed_lines = []

        # Simple indentation fix - ensure consistent 4-space indentation
        for line in lines:
            if line.strip():  # Non-empty line
                # Count leading spaces
                leading_spaces = len(line) - len(line.lstrip())
                # Convert to 4-space indentation
                indent_level = leading_spaces // 4
                fixed_line = '    ' * indent_level + line.lstrip()
                fixed_lines.append(fixed_line)
            else:
                fixed_lines.append(line)

        return '\n'.join(fixed_lines)

    def _build_fix_prompt(self, error: ErrorEvent, root_cause: RootCause,
                         code_context: Optional[str]) -> str:
        """Build prompt for LLM fix generation"""
        prompt = f"""You are an expert code debugger. Please fix the following error:

Error Type: {error.error_type}
Error Message: {error.message}
Root Cause: {root_cause.primary_cause}
Confidence: {root_cause.confidence}

"""

        if code_context:
            prompt += f"""Code Context:
```
{code_context}
```

"""

        prompt += """Please provide a fixed version of the code. Focus on:
1. Fixing the specific error mentioned
2. Maintaining code functionality
3. Following best practices
4. Providing a brief explanation of the fix

Return only the corrected code without additional commentary."""

        return prompt

    def _parse_ai_fix_response(self, error: ErrorEvent, response: str) -> Optional[FixSuggestion]:
        """Parse AI response and create fix suggestion"""
        try:
            import uuid

            # Extract code from response (assuming it's in code blocks)
            code_match = re.search(r'```(?:python|javascript|html)?\n(.*?)\n```', response, re.DOTALL)
            if code_match:
                fixed_code = code_match.group(1)
            else:
                # If no code blocks, use the entire response as fixed code
                fixed_code = response.strip()

            # Extract explanation
            explanation_match = re.search(r'(?:Explanation|Fix):\s*(.*?)(?:\n\n|\n```|$)', response, re.DOTALL)
            explanation = explanation_match.group(1).strip() if explanation_match else "AI-generated fix"

            return FixSuggestion(
                fix_id=str(uuid.uuid4()),
                error_id=error.id,
                fix_type='ai',
                confidence=0.7,  # Default confidence for AI fixes
                description="AI-generated fix",
                original_code="",  # Will be filled when applying
                fixed_code=fixed_code,
                explanation=explanation,
                validation_result=False,
                side_effects=["AI-generated code may need review"],
                backup_required=True
            )

        except Exception as e:
            self.logger.error(f"Error parsing AI fix response: {e}")
            return None

    def _create_backup(self, file_path: Path) -> Path:
        """Create backup of file before applying fix"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = file_path.with_suffix(f"{file_path.suffix}.backup_{timestamp}")

        import shutil
        shutil.copy2(file_path, backup_path)

        self.logger.info(f"Created backup: {backup_path}")
        return backup_path

    def _apply_fix_to_content(self, content: str, fix: FixSuggestion) -> str:
        """Apply fix to content"""
        if fix.fix_type == 'ai' and fix.fixed_code:
            # For AI fixes, replace entire content
            return fix.fixed_code
        elif fix.original_code and fix.fixed_code:
            # For template fixes, replace specific parts
            return content.replace(fix.original_code, fix.fixed_code)
        else:
            # Fallback: return original content
            return content

    def _validate_fix(self, content: str, file_extension: str) -> bool:
        """Validate the fixed content"""
        try:
            if file_extension == '.py':
                is_valid, errors = self.validator.validate_python(content)
                return is_valid
            elif file_extension == '.js':
                is_valid, errors = self.validator.validate_javascript(content)
                return is_valid
            elif file_extension in ['.html', '.htm']:
                is_valid, errors = self.validator.validate_html(content)
                return is_valid
            else:
                # For unknown file types, assume valid
                return True

        except Exception as e:
            self.logger.error(f"Error validating fix: {e}")
            return False