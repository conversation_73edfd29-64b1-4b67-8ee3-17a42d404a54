# AI Coding Agent - Error Detection & Monitoring
"""
Continuous error monitoring system for real-time error detection and analysis
"""

import time
import threading
import traceback
import psutil
import subprocess
import json
import re
import uuid
from pathlib import Path
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass, asdict
from datetime import datetime, timed<PERSON>ta
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

from .logger import get_logger
from .database import DatabaseManager
from .exceptions import ErrorMonitoringError

@dataclass
class ErrorEvent:
    """Represents a detected error event"""
    id: str
    timestamp: datetime
    error_type: str
    severity: str  # 'low', 'medium', 'high', 'critical'
    source: str  # 'syntax', 'runtime', 'system', 'security'
    message: str
    details: Dict[str, Any]
    file_path: Optional[str] = None
    line_number: Optional[int] = None
    stack_trace: Optional[str] = None
    suggested_fix: Optional[str] = None

@dataclass
class SystemHealth:
    """System health metrics"""
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    active_processes: int
    timestamp: datetime

class FileChangeHandler(FileSystemEventHandler):
    """Handles file system events for monitoring"""

    def __init__(self, error_monitor):
        self.error_monitor = error_monitor
        self.logger = get_logger(__name__)

    def on_modified(self, event):
        """Handle file modification events"""
        if not event.is_directory and event.src_path.endswith(('.py', '.js', '.html', '.css')):
            self.error_monitor._check_file_for_errors(event.src_path)

class ErrorMonitor:
    """
    Continuous error monitoring system that detects and tracks errors in real-time
    """

    def __init__(self, projects_dir: str = "projects", monitoring_interval: float = 5.0):
        """
        Initialize error monitor

        Args:
            projects_dir: Directory to monitor for file changes
            monitoring_interval: Interval in seconds for system health checks
        """
        self.projects_dir = Path(projects_dir)
        self.monitoring_interval = monitoring_interval
        self.logger = get_logger(__name__)
        self.db = DatabaseManager()

        # Monitoring state
        self.is_monitoring = False
        self.monitor_thread = None
        self.file_observer = None

        # Error tracking
        self.error_events: List[ErrorEvent] = []
        self.error_callbacks: List[Callable[[ErrorEvent], None]] = []
        self.system_health_history: List[SystemHealth] = []

        # Error patterns for detection
        self.error_patterns = {
            'python': {
                'syntax': [
                    r'SyntaxError:',
                    r'IndentationError:',
                    r'TabError:'
                ],
                'runtime': [
                    r'NameError:',
                    r'TypeError:',
                    r'ValueError:',
                    r'AttributeError:',
                    r'KeyError:',
                    r'IndexError:'
                ],
                'import': [
                    r'ImportError:',
                    r'ModuleNotFoundError:'
                ]
            },
            'javascript': {
                'syntax': [
                    r'SyntaxError:',
                    r'Unexpected token',
                    r'Unexpected end of input'
                ],
                'runtime': [
                    r'ReferenceError:',
                    r'TypeError:',
                    r'RangeError:',
                    r'is not defined'
                ]
            }
        }

        self.logger.info("ErrorMonitor initialized")

    def start_monitoring(self) -> None:
        """Start continuous error monitoring"""
        if self.is_monitoring:
            self.logger.warning("Error monitoring is already running")
            return

        self.is_monitoring = True

        # Start system health monitoring thread
        self.monitor_thread = threading.Thread(target=self._monitor_system_health, daemon=True)
        self.monitor_thread.start()

        # Start file system monitoring
        self._start_file_monitoring()

        self.logger.info("Error monitoring started")

    def stop_monitoring(self) -> None:
        """Stop error monitoring"""
        self.is_monitoring = False

        if self.file_observer:
            self.file_observer.stop()
            self.file_observer.join()

        if self.monitor_thread:
            self.monitor_thread.join(timeout=5.0)

        self.logger.info("Error monitoring stopped")

    def add_error_callback(self, callback: Callable[[ErrorEvent], None]) -> None:
        """Add callback function to be called when errors are detected"""
        self.error_callbacks.append(callback)

    def get_recent_errors(self, hours: int = 24) -> List[ErrorEvent]:
        """Get errors from the last N hours"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        return [error for error in self.error_events if error.timestamp >= cutoff_time]

    def get_error_statistics(self) -> Dict[str, Any]:
        """Get error statistics and trends"""
        recent_errors = self.get_recent_errors(24)

        stats = {
            'total_errors_24h': len(recent_errors),
            'errors_by_type': {},
            'errors_by_severity': {},
            'errors_by_source': {},
            'most_common_files': {},
            'error_trend': self._calculate_error_trend()
        }

        for error in recent_errors:
            # Count by type
            stats['errors_by_type'][error.error_type] = stats['errors_by_type'].get(error.error_type, 0) + 1

            # Count by severity
            stats['errors_by_severity'][error.severity] = stats['errors_by_severity'].get(error.severity, 0) + 1

            # Count by source
            stats['errors_by_source'][error.source] = stats['errors_by_source'].get(error.source, 0) + 1

            # Count by file
            if error.file_path:
                stats['most_common_files'][error.file_path] = stats['most_common_files'].get(error.file_path, 0) + 1

        return stats

    def check_code_for_errors(self, code: str, language: str, file_path: Optional[str] = None) -> List[ErrorEvent]:
        """
        Check code for errors and return detected issues

        Args:
            code: Code to check
            language: Programming language
            file_path: Optional file path for context

        Returns:
            List of detected error events
        """
        detected_errors = []

        try:
            if language == 'python':
                detected_errors.extend(self._check_python_errors(code, file_path))
            elif language == 'javascript':
                detected_errors.extend(self._check_javascript_errors(code, file_path))
            elif language == 'html':
                detected_errors.extend(self._check_html_errors(code, file_path))

            # Add errors to tracking
            for error in detected_errors:
                self._record_error(error)

        except Exception as e:
            self.logger.error(f"Error during code checking: {e}")
            error_event = ErrorEvent(
                id=self._generate_error_id(),
                timestamp=datetime.now(),
                error_type="monitoring_error",
                severity="medium",
                source="system",
                message=f"Error during code checking: {str(e)}",
                details={"exception": str(e)},
                file_path=file_path
            )
            detected_errors.append(error_event)

        return detected_errors

    def get_system_health(self) -> SystemHealth:
        """Get current system health metrics"""
        try:
            cpu_usage = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')

            health = SystemHealth(
                cpu_usage=cpu_usage,
                memory_usage=memory.percent,
                disk_usage=disk.percent,
                active_processes=len(psutil.pids()),
                timestamp=datetime.now()
            )

            return health

        except Exception as e:
            self.logger.error(f"Error getting system health: {e}")
            return SystemHealth(
                cpu_usage=0.0,
                memory_usage=0.0,
                disk_usage=0.0,
                active_processes=0,
                timestamp=datetime.now()
            )

    def _start_file_monitoring(self) -> None:
        """Start file system monitoring"""
        if not self.projects_dir.exists():
            self.projects_dir.mkdir(parents=True, exist_ok=True)

        event_handler = FileChangeHandler(self)
        self.file_observer = Observer()
        self.file_observer.schedule(event_handler, str(self.projects_dir), recursive=True)
        self.file_observer.start()

        self.logger.info(f"File monitoring started for {self.projects_dir}")

    def _monitor_system_health(self) -> None:
        """Monitor system health in background thread"""
        while self.is_monitoring:
            try:
                health = self.get_system_health()
                self.system_health_history.append(health)

                # Keep only last 24 hours of health data
                cutoff_time = datetime.now() - timedelta(hours=24)
                self.system_health_history = [
                    h for h in self.system_health_history if h.timestamp >= cutoff_time
                ]

                # Check for system health issues
                self._check_system_health_issues(health)

                time.sleep(self.monitoring_interval)

            except Exception as e:
                self.logger.error(f"Error in system health monitoring: {e}")
                time.sleep(self.monitoring_interval)

    def _check_file_for_errors(self, file_path: str) -> None:
        """Check a specific file for errors"""
        try:
            path = Path(file_path)
            if not path.exists():
                return

            with open(path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Determine language from file extension
            language = self._get_language_from_extension(path.suffix)
            if language:
                self.check_code_for_errors(content, language, str(path))

        except Exception as e:
            self.logger.error(f"Error checking file {file_path}: {e}")

    def _get_language_from_extension(self, extension: str) -> Optional[str]:
        """Get programming language from file extension"""
        extension_map = {
            '.py': 'python',
            '.js': 'javascript',
            '.html': 'html',
            '.htm': 'html',
            '.css': 'css'
        }
        return extension_map.get(extension.lower())

    def _calculate_error_trend(self) -> Dict[str, Any]:
        """Calculate error trend over time"""
        now = datetime.now()
        hours_24 = now - timedelta(hours=24)
        hours_12 = now - timedelta(hours=12)
        hours_6 = now - timedelta(hours=6)
        hours_1 = now - timedelta(hours=1)

        errors_24h = len([e for e in self.error_events if e.timestamp >= hours_24])
        errors_12h = len([e for e in self.error_events if e.timestamp >= hours_12])
        errors_6h = len([e for e in self.error_events if e.timestamp >= hours_6])
        errors_1h = len([e for e in self.error_events if e.timestamp >= hours_1])

        return {
            'errors_24h': errors_24h,
            'errors_12h': errors_12h,
            'errors_6h': errors_6h,
            'errors_1h': errors_1h,
            'trend': 'increasing' if errors_1h > errors_6h / 6 else 'stable'
        }

    def _check_python_errors(self, code: str, file_path: Optional[str] = None) -> List[ErrorEvent]:
        """Check Python code for errors"""
        errors = []

        # Syntax check
        try:
            import ast
            ast.parse(code)
        except SyntaxError as e:
            error = ErrorEvent(
                id=self._generate_error_id(),
                timestamp=datetime.now(),
                error_type="SyntaxError",
                severity="high",
                source="syntax",
                message=str(e),
                details={"line": e.lineno, "offset": e.offset},
                file_path=file_path,
                line_number=e.lineno
            )
            errors.append(error)

        # Pattern-based error detection
        for category, patterns in self.error_patterns['python'].items():
            for pattern in patterns:
                matches = re.finditer(pattern, code, re.MULTILINE)
                for match in matches:
                    line_num = code[:match.start()].count('\n') + 1
                    error = ErrorEvent(
                        id=self._generate_error_id(),
                        timestamp=datetime.now(),
                        error_type=pattern.replace(':', '').replace('\\', ''),
                        severity="medium",
                        source=category,
                        message=f"Detected {pattern} pattern",
                        details={"pattern": pattern, "match": match.group()},
                        file_path=file_path,
                        line_number=line_num
                    )
                    errors.append(error)

        return errors

    def _check_javascript_errors(self, code: str, file_path: Optional[str] = None) -> List[ErrorEvent]:
        """Check JavaScript code for errors"""
        errors = []

        # Basic syntax checks
        if code.count('(') != code.count(')'):
            error = ErrorEvent(
                id=self._generate_error_id(),
                timestamp=datetime.now(),
                error_type="SyntaxError",
                severity="high",
                source="syntax",
                message="Mismatched parentheses",
                details={"open_parens": code.count('('), "close_parens": code.count(')')},
                file_path=file_path
            )
            errors.append(error)

        if code.count('{') != code.count('}'):
            error = ErrorEvent(
                id=self._generate_error_id(),
                timestamp=datetime.now(),
                error_type="SyntaxError",
                severity="high",
                source="syntax",
                message="Mismatched braces",
                details={"open_braces": code.count('{'), "close_braces": code.count('}')},
                file_path=file_path
            )
            errors.append(error)

        # Pattern-based error detection
        for category, patterns in self.error_patterns['javascript'].items():
            for pattern in patterns:
                matches = re.finditer(pattern, code, re.MULTILINE)
                for match in matches:
                    line_num = code[:match.start()].count('\n') + 1
                    error = ErrorEvent(
                        id=self._generate_error_id(),
                        timestamp=datetime.now(),
                        error_type=pattern.replace(':', '').replace('\\', ''),
                        severity="medium",
                        source=category,
                        message=f"Detected {pattern} pattern",
                        details={"pattern": pattern, "match": match.group()},
                        file_path=file_path,
                        line_number=line_num
                    )
                    errors.append(error)

        return errors

    def _check_html_errors(self, code: str, file_path: Optional[str] = None) -> List[ErrorEvent]:
        """Check HTML code for errors"""
        errors = []

        # Basic HTML structure check
        if not code.strip().startswith('<'):
            error = ErrorEvent(
                id=self._generate_error_id(),
                timestamp=datetime.now(),
                error_type="StructureError",
                severity="medium",
                source="syntax",
                message="HTML should start with a tag",
                details={"first_char": code.strip()[:10] if code.strip() else ""},
                file_path=file_path,
                line_number=1
            )
            errors.append(error)

        # Check for unclosed tags (basic)
        import re
        open_tags = re.findall(r'<([a-zA-Z][^>]*?)>', code)
        close_tags = re.findall(r'</([a-zA-Z][^>]*?)>', code)

        # Simple tag balance check (not perfect but catches obvious issues)
        tag_counts = {}
        for tag in open_tags:
            tag_name = tag.split()[0].lower()
            if tag_name not in ['br', 'hr', 'img', 'input', 'meta', 'link']:  # Self-closing tags
                tag_counts[tag_name] = tag_counts.get(tag_name, 0) + 1

        for tag in close_tags:
            tag_name = tag.split()[0].lower()
            if tag_name in tag_counts:
                tag_counts[tag_name] -= 1

        for tag_name, count in tag_counts.items():
            if count != 0:
                error = ErrorEvent(
                    id=self._generate_error_id(),
                    timestamp=datetime.now(),
                    error_type="UnclosedTag",
                    severity="medium",
                    source="syntax",
                    message=f"Unbalanced tag: {tag_name}",
                    details={"tag": tag_name, "imbalance": count},
                    file_path=file_path
                )
                errors.append(error)

        return errors

    def _record_error(self, error: ErrorEvent) -> None:
        """Record error event and notify callbacks"""
        self.error_events.append(error)

        # Keep only last 1000 errors in memory
        if len(self.error_events) > 1000:
            self.error_events = self.error_events[-1000:]

        # Notify callbacks
        for callback in self.error_callbacks:
            try:
                callback(error)
            except Exception as e:
                self.logger.error(f"Error in error callback: {e}")

        # Log the error
        self.logger.warning(f"Error detected: {error.error_type} - {error.message}")

    def _generate_error_id(self) -> str:
        """Generate unique error ID"""
        return str(uuid.uuid4())

    def _check_system_health_issues(self, health: SystemHealth) -> None:
        """Check system health for issues and create error events if needed"""
        # CPU usage threshold
        if health.cpu_usage > 90:
            error = ErrorEvent(
                id=self._generate_error_id(),
                timestamp=health.timestamp,
                error_type="HighCPUUsage",
                severity="high",
                source="system",
                message=f"High CPU usage: {health.cpu_usage:.1f}%",
                details={"cpu_usage": health.cpu_usage}
            )
            self._record_error(error)

        # Memory usage threshold
        if health.memory_usage > 85:
            error = ErrorEvent(
                id=self._generate_error_id(),
                timestamp=health.timestamp,
                error_type="HighMemoryUsage",
                severity="high",
                source="system",
                message=f"High memory usage: {health.memory_usage:.1f}%",
                details={"memory_usage": health.memory_usage}
            )
            self._record_error(error)

        # Disk usage threshold
        if health.disk_usage > 90:
            error = ErrorEvent(
                id=self._generate_error_id(),
                timestamp=health.timestamp,
                error_type="HighDiskUsage",
                severity="critical",
                source="system",
                message=f"High disk usage: {health.disk_usage:.1f}%",
                details={"disk_usage": health.disk_usage}
            )
            self._record_error(error)