# AI Coding Agent - Custom Exceptions
"""
Custom exception classes for the coding agent
"""

from typing import Optional, Dict, Any

class CodingAgentError(Exception):
    """Base exception class for coding agent errors"""
    
    def __init__(self, message: str, error_code: Optional[str] = None, details: Optional[Dict[str, Any]] = None):
        self.message = message
        self.error_code = error_code or "GENERAL_ERROR"
        self.details = details or {}
        super().__init__(self.message)
    
    def to_dict(self) -> dict:
        """Convert exception to dictionary for API responses"""
        return {
            "error": True,
            "error_code": self.error_code,
            "message": self.message,
            "details": self.details
        }

class LLMConnectionError(CodingAgentError):
    """Raised when LLM connection fails"""
    
    def __init__(self, message: str = "Failed to connect to LLM service", **kwargs):
        super().__init__(message, error_code="LLM_CONNECTION_ERROR", **kwargs)

class LLMResponseError(CodingAgentError):
    """Raised when LLM returns invalid response"""
    
    def __init__(self, message: str = "Invalid response from LLM", **kwargs):
        super().__init__(message, error_code="LLM_RESPONSE_ERROR", **kwargs)

class CodeValidationError(CodingAgentError):
    """Raised when generated code fails validation"""
    
    def __init__(self, message: str = "Generated code failed validation", **kwargs):
        super().__init__(message, error_code="CODE_VALIDATION_ERROR", **kwargs)

class ProjectError(CodingAgentError):
    """Raised when project operations fail"""
    
    def __init__(self, message: str = "Project operation failed", **kwargs):
        super().__init__(message, error_code="PROJECT_ERROR", **kwargs)

class DatabaseError(CodingAgentError):
    """Raised when database operations fail"""
    
    def __init__(self, message: str = "Database operation failed", **kwargs):
        super().__init__(message, error_code="DATABASE_ERROR", **kwargs)

class ConfigurationError(CodingAgentError):
    """Raised when configuration is invalid"""
    
    def __init__(self, message: str = "Invalid configuration", **kwargs):
        super().__init__(message, error_code="CONFIGURATION_ERROR", **kwargs)

class SecurityError(CodingAgentError):
    """Raised when security validation fails"""

    def __init__(self, message: str = "Security validation failed", **kwargs):
        super().__init__(message, error_code="SECURITY_ERROR", **kwargs)

class RequirementParsingError(CodingAgentError):
    """Raised when requirement parsing fails"""

    def __init__(self, message: str = "Failed to parse requirements", **kwargs):
        super().__init__(message, error_code="REQUIREMENT_PARSING_ERROR", **kwargs)

class ErrorMonitoringError(CodingAgentError):
    """Raised when error monitoring operations fail"""

    def __init__(self, message: str = "Error monitoring operation failed", **kwargs):
        super().__init__(message, error_code="ERROR_MONITORING_ERROR", **kwargs)

class DependencyAnalysisError(CodingAgentError):
    """Raised when dependency analysis fails"""

    def __init__(self, message: str = "Dependency analysis failed", **kwargs):
        super().__init__(message, error_code="DEPENDENCY_ANALYSIS_ERROR", **kwargs)
