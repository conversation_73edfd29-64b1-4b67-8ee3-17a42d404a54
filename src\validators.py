# AI Coding Agent - Code Validation
"""
Code validation and security checking for generated code
"""

import ast
import tempfile
import subprocess
import re
from pathlib import Path
from typing import Tuple, List, Dict, Any, Optional

from .logger import get_logger
from .exceptions import CodeValidationError, SecurityError

class CodeValidator:
    """
    Validates generated code for syntax, security, and best practices
    """
    
    def __init__(self):
        """Initialize code validator"""
        self.temp_dir = Path(tempfile.gettempdir()) / "coding_agent_validation"
        self.temp_dir.mkdir(exist_ok=True)
        self.logger = get_logger(__name__)

        # Security patterns to detect dangerous code
        self.dangerous_patterns = [
            r'import\s+os',
            r'import\s+subprocess',
            r'import\s+sys',
            r'eval\s*\(',
            r'exec\s*\(',
            r'__import__\s*\(',
            r'open\s*\(',
            r'file\s*\(',
            r'input\s*\(',
            r'raw_input\s*\(',
        ]
        
    def validate_python(self, code: str) -> Tuple[bool, List[str]]:
        """
        Comprehensive Python code validation for syntax, security, and best practices

        Args:
            code: Python code to validate

        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        errors = []
        warnings = []

        # 1. Syntax validation
        try:
            parsed = ast.parse(code)
            self.logger.debug("Python syntax validation passed")
        except SyntaxError as e:
            error_msg = f"Syntax Error: {e}"
            errors.append(error_msg)
            self.logger.warning(f"Syntax validation failed: {error_msg}")
            return False, errors

        # 2. Security validation
        security_errors = self._check_security_patterns(code)
        errors.extend(security_errors)

        # 3. Best practices check
        best_practice_warnings = self._check_best_practices(code)
        warnings.extend(best_practice_warnings)

        is_valid = len(errors) == 0
        all_issues = errors + warnings

        if is_valid:
            self.logger.info(f"Python code validation passed with {len(warnings)} warnings")
        else:
            self.logger.warning(f"Python code validation failed with {len(errors)} errors")

        return is_valid, all_issues
    
    def validate_html(self, html_code: str) -> Tuple[bool, List[str]]:
        """
        Basic HTML validation
        
        Args:
            html_code: HTML code to validate
            
        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        errors = []
        
        # Basic HTML structure check
        if not html_code.strip().startswith('<'):
            errors.append("HTML should start with a tag")
            
        self.logger.info("HTML validation completed")
        return len(errors) == 0, errors

    def _check_security_patterns(self, code: str) -> List[str]:
        """Check for dangerous patterns using regex"""
        import re
        errors = []

        for pattern in self.dangerous_patterns:
            if re.search(pattern, code, re.IGNORECASE):
                errors.append(f"Security risk: Detected dangerous pattern '{pattern}'")

        return errors

    def _check_best_practices(self, code: str) -> List[str]:
        """Check for Python best practices"""
        warnings = []
        lines = code.split('\n')

        for i, line in enumerate(lines, 1):
            # Check line length
            if len(line) > 120:
                warnings.append(f"Line {i}: Line too long ({len(line)} > 120 characters)")

            # Check for print statements (should use logging)
            if 'print(' in line and not line.strip().startswith('#'):
                warnings.append(f"Line {i}: Consider using logging instead of print()")

        return warnings

    def validate_javascript(self, code: str) -> Tuple[bool, List[str]]:
        """
        Basic JavaScript validation

        Args:
            code: JavaScript code to validate

        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        errors = []
        warnings = []

        # Basic security checks
        dangerous_js_patterns = [
            r'eval\s*\(',
            r'document\.write\s*\(',
            r'innerHTML\s*=',
            r'outerHTML\s*=',
        ]

        import re
        for pattern in dangerous_js_patterns:
            if re.search(pattern, code, re.IGNORECASE):
                errors.append(f"Security risk: Detected dangerous JavaScript pattern '{pattern}'")

        # Basic syntax checks
        if code.count('(') != code.count(')'):
            errors.append("Mismatched parentheses")
        if code.count('{') != code.count('}'):
            errors.append("Mismatched braces")
        if code.count('[') != code.count(']'):
            errors.append("Mismatched brackets")

        is_valid = len(errors) == 0
        self.logger.info(f"JavaScript validation completed: {'passed' if is_valid else 'failed'}")
        return is_valid, errors + warnings

    def validate_python_advanced(self, code: str, file_path: Optional[str] = None) -> Tuple[bool, List[str], Dict[str, Any]]:
        """
        Advanced Python validation with AST analysis and security scanning

        Args:
            code: Python code to validate
            file_path: Optional file path for context

        Returns:
            Tuple of (is_valid, list_of_issues, analysis_details)
        """
        issues = []
        analysis = {
            'complexity_score': 0,
            'security_issues': [],
            'performance_warnings': [],
            'style_violations': [],
            'imports': [],
            'functions': [],
            'classes': []
        }

        try:
            # Parse AST for advanced analysis
            tree = ast.parse(code)

            # Analyze AST
            self._analyze_ast(tree, analysis, issues)

            # Security analysis
            security_issues = self._advanced_security_check(code, tree)
            analysis['security_issues'] = security_issues
            issues.extend(security_issues)

            # Performance analysis
            perf_warnings = self._performance_analysis(tree)
            analysis['performance_warnings'] = perf_warnings
            issues.extend(perf_warnings)

            # Style analysis
            style_issues = self._style_analysis(code)
            analysis['style_violations'] = style_issues
            issues.extend(style_issues)

            # Run external tools if available
            external_issues = self._run_external_python_tools(code, file_path)
            issues.extend(external_issues)

        except SyntaxError as e:
            issues.append(f"Syntax Error: {e}")
            return False, issues, analysis
        except Exception as e:
            self.logger.error(f"Error in advanced Python validation: {e}")
            issues.append(f"Validation error: {e}")

        is_valid = len([issue for issue in issues if 'Error:' in issue]) == 0
        self.logger.info(f"Advanced Python validation: {'passed' if is_valid else 'failed'} with {len(issues)} issues")

        return is_valid, issues, analysis

    def validate_javascript_advanced(self, code: str, file_path: Optional[str] = None) -> Tuple[bool, List[str], Dict[str, Any]]:
        """
        Advanced JavaScript validation with ESLint integration and security scanning

        Args:
            code: JavaScript code to validate
            file_path: Optional file path for context

        Returns:
            Tuple of (is_valid, list_of_issues, analysis_details)
        """
        issues = []
        analysis = {
            'security_issues': [],
            'performance_warnings': [],
            'style_violations': [],
            'functions': [],
            'variables': []
        }

        # Basic validation first
        basic_valid, basic_issues = self.validate_javascript(code)
        issues.extend(basic_issues)

        # Advanced security analysis
        security_issues = self._advanced_js_security_check(code)
        analysis['security_issues'] = security_issues
        issues.extend(security_issues)

        # Performance analysis
        perf_warnings = self._js_performance_analysis(code)
        analysis['performance_warnings'] = perf_warnings
        issues.extend(perf_warnings)

        # Style analysis
        style_issues = self._js_style_analysis(code)
        analysis['style_violations'] = style_issues
        issues.extend(style_issues)

        # Try to run ESLint if available
        eslint_issues = self._run_eslint(code, file_path)
        issues.extend(eslint_issues)

        is_valid = len([issue for issue in issues if 'Error:' in issue or 'Security risk:' in issue]) == 0
        self.logger.info(f"Advanced JavaScript validation: {'passed' if is_valid else 'failed'} with {len(issues)} issues")

        return is_valid, issues, analysis

    def validate_html_advanced(self, html_code: str, file_path: Optional[str] = None) -> Tuple[bool, List[str], Dict[str, Any]]:
        """
        Advanced HTML validation with W3C compliance and accessibility checks

        Args:
            html_code: HTML code to validate
            file_path: Optional file path for context

        Returns:
            Tuple of (is_valid, list_of_issues, analysis_details)
        """
        issues = []
        analysis = {
            'accessibility_issues': [],
            'seo_warnings': [],
            'performance_warnings': [],
            'structure_issues': [],
            'tags': [],
            'attributes': []
        }

        # Basic validation first
        basic_valid, basic_issues = self.validate_html(html_code)
        issues.extend(basic_issues)

        # Advanced HTML analysis
        self._analyze_html_structure(html_code, analysis, issues)

        # Accessibility analysis
        accessibility_issues = self._accessibility_analysis(html_code)
        analysis['accessibility_issues'] = accessibility_issues
        issues.extend(accessibility_issues)

        # SEO analysis
        seo_warnings = self._seo_analysis(html_code)
        analysis['seo_warnings'] = seo_warnings
        issues.extend(seo_warnings)

        # Performance analysis
        perf_warnings = self._html_performance_analysis(html_code)
        analysis['performance_warnings'] = perf_warnings
        issues.extend(perf_warnings)

        is_valid = len([issue for issue in issues if 'Error:' in issue]) == 0
        self.logger.info(f"Advanced HTML validation: {'passed' if is_valid else 'failed'} with {len(issues)} issues")

        return is_valid, issues, analysis

    def _analyze_ast(self, tree: ast.AST, analysis: Dict[str, Any], issues: List[str]) -> None:
        """Analyze Python AST for complexity and structure"""
        class ASTAnalyzer(ast.NodeVisitor):
            def __init__(self):
                self.complexity = 0
                self.imports = []
                self.functions = []
                self.classes = []
                self.nested_depth = 0
                self.max_depth = 0

            def visit_Import(self, node):
                for alias in node.names:
                    self.imports.append(alias.name)
                self.generic_visit(node)

            def visit_ImportFrom(self, node):
                module = node.module or ''
                for alias in node.names:
                    self.imports.append(f"{module}.{alias.name}")
                self.generic_visit(node)

            def visit_FunctionDef(self, node):
                self.functions.append({
                    'name': node.name,
                    'line': node.lineno,
                    'args': len(node.args.args),
                    'decorators': len(node.decorator_list)
                })
                self.nested_depth += 1
                self.max_depth = max(self.max_depth, self.nested_depth)
                self.complexity += 1
                self.generic_visit(node)
                self.nested_depth -= 1

            def visit_ClassDef(self, node):
                self.classes.append({
                    'name': node.name,
                    'line': node.lineno,
                    'bases': len(node.bases),
                    'methods': len([n for n in node.body if isinstance(n, ast.FunctionDef)])
                })
                self.complexity += 1
                self.generic_visit(node)

            def visit_If(self, node):
                self.complexity += 1
                self.generic_visit(node)

            def visit_For(self, node):
                self.complexity += 1
                self.generic_visit(node)

            def visit_While(self, node):
                self.complexity += 1
                self.generic_visit(node)

            def visit_Try(self, node):
                self.complexity += 1
                self.generic_visit(node)

        analyzer = ASTAnalyzer()
        analyzer.visit(tree)

        analysis['complexity_score'] = analyzer.complexity
        analysis['imports'] = analyzer.imports
        analysis['functions'] = analyzer.functions
        analysis['classes'] = analyzer.classes

        # Add complexity warnings
        if analyzer.complexity > 20:
            issues.append(f"Warning: High complexity score ({analyzer.complexity}). Consider refactoring.")

        if analyzer.max_depth > 4:
            issues.append(f"Warning: Deep nesting detected (depth: {analyzer.max_depth}). Consider refactoring.")

    def _advanced_security_check(self, code: str, tree: ast.AST) -> List[str]:
        """Advanced security analysis for Python code"""
        security_issues = []

        # Check for dangerous imports
        dangerous_imports = [
            'subprocess', 'os.system', 'eval', 'exec', 'compile',
            'pickle', '__import__', 'importlib'
        ]

        class SecurityAnalyzer(ast.NodeVisitor):
            def __init__(self):
                self.issues = []

            def visit_Import(self, node):
                for alias in node.names:
                    if alias.name in dangerous_imports:
                        self.issues.append(f"Security: Potentially dangerous import '{alias.name}' at line {node.lineno}")
                self.generic_visit(node)

            def visit_Call(self, node):
                if isinstance(node.func, ast.Name):
                    if node.func.id in ['eval', 'exec', 'compile']:
                        self.issues.append(f"Security: Dangerous function '{node.func.id}' at line {node.lineno}")
                elif isinstance(node.func, ast.Attribute):
                    if node.func.attr in ['system', 'popen', 'spawn']:
                        self.issues.append(f"Security: Dangerous method '{node.func.attr}' at line {node.lineno}")
                self.generic_visit(node)

        analyzer = SecurityAnalyzer()
        analyzer.visit(tree)
        security_issues.extend(analyzer.issues)

        # Additional pattern-based security checks
        dangerous_patterns = [
            (r'input\s*\(.*\)', "Security: Using input() can be dangerous"),
            (r'open\s*\([^)]*["\']w["\']', "Warning: File write operations detected"),
            (r'urllib\.request\.urlopen', "Security: URL opening detected - validate URLs"),
        ]

        for pattern, message in dangerous_patterns:
            if re.search(pattern, code):
                security_issues.append(message)

        return security_issues

    def _performance_analysis(self, tree: ast.AST) -> List[str]:
        """Analyze code for performance issues"""
        warnings = []

        class PerformanceAnalyzer(ast.NodeVisitor):
            def __init__(self):
                self.warnings = []
                self.loop_depth = 0

            def visit_For(self, node):
                self.loop_depth += 1
                if self.loop_depth > 3:
                    self.warnings.append(f"Performance: Deep nested loops at line {node.lineno}")

                # Check for list concatenation in loops
                for child in ast.walk(node):
                    if isinstance(child, ast.AugAssign) and isinstance(child.op, ast.Add):
                        if isinstance(child.target, ast.Name):
                            self.warnings.append(f"Performance: List concatenation in loop at line {child.lineno} - consider using list comprehension")

                self.generic_visit(node)
                self.loop_depth -= 1

            def visit_ListComp(self, node):
                # Check for nested list comprehensions
                nested_comps = [n for n in ast.walk(node) if isinstance(n, (ast.ListComp, ast.DictComp, ast.SetComp))]
                if len(nested_comps) > 2:
                    self.warnings.append(f"Performance: Complex nested comprehension at line {node.lineno}")
                self.generic_visit(node)

        analyzer = PerformanceAnalyzer()
        analyzer.visit(tree)

        return analyzer.warnings

    def _style_analysis(self, code: str) -> List[str]:
        """Analyze code style issues"""
        style_issues = []
        lines = code.split('\n')

        for i, line in enumerate(lines, 1):
            # Line length check
            if len(line) > 120:
                style_issues.append(f"Style: Line {i} too long ({len(line)} > 120 characters)")

            # Indentation check (should be 4 spaces)
            if line.strip() and line.startswith(' '):
                leading_spaces = len(line) - len(line.lstrip())
                if leading_spaces % 4 != 0:
                    style_issues.append(f"Style: Line {i} has inconsistent indentation")

            # Check for trailing whitespace
            if line.endswith(' ') or line.endswith('\t'):
                style_issues.append(f"Style: Line {i} has trailing whitespace")

            # Check for multiple blank lines
            if i > 1 and not line.strip() and not lines[i-2].strip():
                style_issues.append(f"Style: Line {i} has multiple consecutive blank lines")

        return style_issues

    def _run_external_python_tools(self, code: str, file_path: Optional[str]) -> List[str]:
        """Run external Python validation tools"""
        issues = []

        if not file_path:
            return issues

        # Try to run pyflakes
        try:
            result = subprocess.run(
                ['pyflakes', file_path],
                capture_output=True,
                text=True,
                timeout=10
            )
            if result.returncode != 0 and result.stdout:
                for line in result.stdout.strip().split('\n'):
                    if line.strip():
                        issues.append(f"Pyflakes: {line}")
        except (subprocess.TimeoutExpired, FileNotFoundError):
            pass  # Tool not available or timeout

        return issues

    def _advanced_js_security_check(self, code: str) -> List[str]:
        """Advanced JavaScript security analysis"""
        security_issues = []

        # XSS vulnerabilities
        xss_patterns = [
            (r'innerHTML\s*=\s*[^;]+\+', "Security: Potential XSS via innerHTML concatenation"),
            (r'document\.write\s*\([^)]*\+', "Security: Potential XSS via document.write concatenation"),
            (r'eval\s*\([^)]*\+', "Security: Potential code injection via eval"),
            (r'setTimeout\s*\([^)]*\+', "Security: Potential code injection via setTimeout"),
            (r'setInterval\s*\([^)]*\+', "Security: Potential code injection via setInterval"),
        ]

        for pattern, message in xss_patterns:
            if re.search(pattern, code):
                security_issues.append(message)

        # Check for hardcoded credentials
        credential_patterns = [
            (r'password\s*[:=]\s*["\'][^"\']+["\']', "Security: Hardcoded password detected"),
            (r'api[_-]?key\s*[:=]\s*["\'][^"\']+["\']', "Security: Hardcoded API key detected"),
            (r'secret\s*[:=]\s*["\'][^"\']+["\']', "Security: Hardcoded secret detected"),
        ]

        for pattern, message in credential_patterns:
            if re.search(pattern, code, re.IGNORECASE):
                security_issues.append(message)

        return security_issues

    def _js_performance_analysis(self, code: str) -> List[str]:
        """Analyze JavaScript for performance issues"""
        warnings = []

        # Check for performance anti-patterns
        perf_patterns = [
            (r'for\s*\([^)]*\.length[^)]*\)', "Performance: Avoid accessing .length in loop condition"),
            (r'document\.getElementById\s*\([^)]*\)\s*\.', "Performance: Cache DOM queries"),
            (r'new\s+RegExp\s*\(', "Performance: Use regex literals instead of RegExp constructor"),
            (r'\+\s*["\'].*["\']', "Performance: Use template literals instead of string concatenation"),
        ]

        for pattern, message in perf_patterns:
            if re.search(pattern, code):
                warnings.append(message)

        return warnings

    def _js_style_analysis(self, code: str) -> List[str]:
        """Analyze JavaScript style issues"""
        style_issues = []
        lines = code.split('\n')

        for i, line in enumerate(lines, 1):
            # Check for var usage (should use let/const)
            if re.search(r'\bvar\s+', line):
                style_issues.append(f"Style: Line {i} uses 'var' - consider 'let' or 'const'")

            # Check for == usage (should use ===)
            if re.search(r'[^=!]==[^=]', line):
                style_issues.append(f"Style: Line {i} uses '==' - consider '==='")

            # Check for missing semicolons
            if line.strip() and not line.strip().endswith((';', '{', '}', ':', ',')):
                if not line.strip().startswith(('if', 'for', 'while', 'function', 'class', '//')):
                    style_issues.append(f"Style: Line {i} missing semicolon")

        return style_issues

    def _run_eslint(self, code: str, file_path: Optional[str]) -> List[str]:
        """Run ESLint if available"""
        issues = []

        if not file_path:
            return issues

        try:
            result = subprocess.run(
                ['eslint', '--format', 'compact', file_path],
                capture_output=True,
                text=True,
                timeout=15
            )
            if result.stdout:
                for line in result.stdout.strip().split('\n'):
                    if line.strip() and 'error' in line.lower():
                        issues.append(f"ESLint: {line}")
        except (subprocess.TimeoutExpired, FileNotFoundError):
            pass  # ESLint not available or timeout

        return issues

    def _analyze_html_structure(self, html_code: str, analysis: Dict[str, Any], issues: List[str]) -> None:
        """Analyze HTML structure and extract information"""
        import re

        # Extract tags
        tags = re.findall(r'<(\w+)', html_code)
        analysis['tags'] = list(set(tags))

        # Check for proper HTML5 structure
        if not re.search(r'<!DOCTYPE\s+html>', html_code, re.IGNORECASE):
            issues.append("Warning: Missing HTML5 DOCTYPE declaration")

        if '<html' not in html_code.lower():
            issues.append("Warning: Missing <html> tag")

        if '<head' not in html_code.lower():
            issues.append("Warning: Missing <head> section")

        if '<body' not in html_code.lower():
            issues.append("Warning: Missing <body> section")

        # Check for unclosed tags
        self._check_html_tag_balance(html_code, issues)

    def _check_html_tag_balance(self, html_code: str, issues: List[str]) -> None:
        """Check for balanced HTML tags"""
        import re

        # Self-closing tags that don't need closing
        self_closing = {'br', 'hr', 'img', 'input', 'meta', 'link', 'area', 'base', 'col', 'embed', 'source', 'track', 'wbr'}

        # Find all opening and closing tags
        opening_tags = re.findall(r'<(\w+)(?:\s[^>]*)?>(?!</)', html_code, re.IGNORECASE)
        closing_tags = re.findall(r'</(\w+)>', html_code, re.IGNORECASE)

        # Count tags
        tag_counts = {}
        for tag in opening_tags:
            tag = tag.lower()
            if tag not in self_closing:
                tag_counts[tag] = tag_counts.get(tag, 0) + 1

        for tag in closing_tags:
            tag = tag.lower()
            if tag in tag_counts:
                tag_counts[tag] -= 1

        # Report unbalanced tags
        for tag, count in tag_counts.items():
            if count > 0:
                issues.append(f"Warning: {count} unclosed <{tag}> tag(s)")
            elif count < 0:
                issues.append(f"Warning: {abs(count)} extra closing </{tag}> tag(s)")

    def _accessibility_analysis(self, html_code: str) -> List[str]:
        """Analyze HTML for accessibility issues"""
        issues = []

        # Check for images without alt text
        img_pattern = r'<img(?![^>]*alt\s*=)[^>]*>'
        if re.search(img_pattern, html_code, re.IGNORECASE):
            issues.append("Accessibility: Images missing alt attributes")

        # Check for form inputs without labels
        input_pattern = r'<input(?![^>]*id\s*=)[^>]*>'
        if re.search(input_pattern, html_code, re.IGNORECASE):
            issues.append("Accessibility: Form inputs should have associated labels")

        # Check for missing heading hierarchy
        headings = re.findall(r'<h([1-6])', html_code, re.IGNORECASE)
        if headings:
            heading_levels = [int(h) for h in headings]
            if heading_levels and min(heading_levels) > 1:
                issues.append("Accessibility: Page should start with h1 heading")

        # Check for missing lang attribute
        if not re.search(r'<html[^>]*lang\s*=', html_code, re.IGNORECASE):
            issues.append("Accessibility: HTML element missing lang attribute")

        return issues

    def _seo_analysis(self, html_code: str) -> List[str]:
        """Analyze HTML for SEO issues"""
        warnings = []

        # Check for title tag
        if not re.search(r'<title[^>]*>.*</title>', html_code, re.IGNORECASE | re.DOTALL):
            warnings.append("SEO: Missing or empty title tag")

        # Check for meta description
        if not re.search(r'<meta[^>]*name\s*=\s*["\']description["\'][^>]*>', html_code, re.IGNORECASE):
            warnings.append("SEO: Missing meta description")

        # Check for multiple h1 tags
        h1_count = len(re.findall(r'<h1[^>]*>', html_code, re.IGNORECASE))
        if h1_count > 1:
            warnings.append(f"SEO: Multiple h1 tags found ({h1_count}) - should have only one")
        elif h1_count == 0:
            warnings.append("SEO: Missing h1 tag")

        # Check for meta viewport
        if not re.search(r'<meta[^>]*name\s*=\s*["\']viewport["\'][^>]*>', html_code, re.IGNORECASE):
            warnings.append("SEO: Missing viewport meta tag for mobile optimization")

        return warnings

    def _html_performance_analysis(self, html_code: str) -> List[str]:
        """Analyze HTML for performance issues"""
        warnings = []

        # Check for inline styles
        if re.search(r'style\s*=\s*["\'][^"\']+["\']', html_code, re.IGNORECASE):
            warnings.append("Performance: Inline styles detected - consider external CSS")

        # Check for inline scripts
        if re.search(r'<script[^>]*>(?!</script>)', html_code, re.IGNORECASE):
            warnings.append("Performance: Inline scripts detected - consider external JS files")

        # Check for large number of external resources
        css_links = len(re.findall(r'<link[^>]*rel\s*=\s*["\']stylesheet["\'][^>]*>', html_code, re.IGNORECASE))
        js_scripts = len(re.findall(r'<script[^>]*src\s*=', html_code, re.IGNORECASE))

        if css_links > 5:
            warnings.append(f"Performance: Many CSS files ({css_links}) - consider bundling")

        if js_scripts > 5:
            warnings.append(f"Performance: Many JS files ({js_scripts}) - consider bundling")

        # Check for missing async/defer on scripts
        sync_scripts = re.findall(r'<script[^>]*src[^>]*>(?![^<]*(?:async|defer))', html_code, re.IGNORECASE)
        if sync_scripts:
            warnings.append("Performance: Scripts without async/defer may block rendering")

        return warnings
