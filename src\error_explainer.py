# AI Coding Agent - Error Explanation System
"""
System for translating technical errors into clear, actionable explanations
"""

import re
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime

from .logger import get_logger
from .error_monitor import ErrorEvent
from .root_cause_analyzer import <PERSON><PERSON>aus<PERSON>
from .models import LLMManager

@dataclass
class ErrorExplanation:
    """User-friendly error explanation"""
    error_id: str
    simple_explanation: str
    technical_details: str
    what_happened: str
    why_it_happened: str
    how_to_fix: List[str]
    prevention_tips: List[str]
    related_concepts: List[str]
    difficulty_level: str  # 'beginner', 'intermediate', 'advanced'
    estimated_fix_time: str

class ErrorExplainer:
    """
    System that translates technical errors into clear, actionable explanations
    """

    def __init__(self):
        """Initialize error explainer"""
        self.logger = get_logger(__name__)
        self.llm_manager = LLMManager()

        # Pre-defined explanations for common errors
        self.explanation_templates = {
            'python': {
                'SyntaxError': {
                    'simple': "There's a problem with how your code is written - Python can't understand it.",
                    'what_happened': "Python tried to read your code but found something that doesn't follow the language rules.",
                    'common_causes': [
                        "Missing colons (:) after if, for, while, def, or class statements",
                        "Mismatched parentheses, brackets, or quotes",
                        "Incorrect indentation (spaces or tabs)",
                        "Typos in keywords or operators"
                    ],
                    'how_to_fix': [
                        "Check the line mentioned in the error message",
                        "Look for missing colons, parentheses, or quotes",
                        "Make sure your indentation is consistent",
                        "Use a code editor with syntax highlighting"
                    ],
                    'prevention': [
                        "Use an IDE with real-time syntax checking",
                        "Write code slowly and check each line",
                        "Use consistent indentation (4 spaces recommended)"
                    ],
                    'difficulty': 'beginner',
                    'fix_time': '2-5 minutes'
                },
                'NameError': {
                    'simple': "You're trying to use a variable or function that Python doesn't know about.",
                    'what_happened': "Python encountered a name (variable, function, etc.) that hasn't been defined yet.",
                    'common_causes': [
                        "Using a variable before defining it",
                        "Typo in variable or function name",
                        "Variable is out of scope",
                        "Forgot to import a module"
                    ],
                    'how_to_fix': [
                        "Define the variable before using it",
                        "Check spelling of variable names",
                        "Make sure the variable is in the right scope",
                        "Import required modules"
                    ],
                    'prevention': [
                        "Define variables before using them",
                        "Use descriptive variable names",
                        "Be careful with variable scope"
                    ],
                    'difficulty': 'beginner',
                    'fix_time': '1-3 minutes'
                },
                'ImportError': {
                    'simple': "Python can't find a module or package you're trying to use.",
                    'what_happened': "Python tried to import a module but couldn't find it on your system.",
                    'common_causes': [
                        "Package not installed",
                        "Typo in module name",
                        "Virtual environment not activated",
                        "Module not in Python path"
                    ],
                    'how_to_fix': [
                        "Install the package using pip install",
                        "Check the spelling of the module name",
                        "Activate your virtual environment",
                        "Verify the module exists"
                    ],
                    'prevention': [
                        "Use requirements.txt to track dependencies",
                        "Always work in virtual environments",
                        "Double-check package names before importing"
                    ],
                    'difficulty': 'intermediate',
                    'fix_time': '5-10 minutes'
                },
                'TypeError': {
                    'simple': "You're trying to do something with the wrong type of data.",
                    'what_happened': "Python expected one type of data but got another, or you called a function incorrectly.",
                    'common_causes': [
                        "Wrong number of function arguments",
                        "Mixing incompatible data types",
                        "Calling something that isn't a function",
                        "Using wrong operators for data types"
                    ],
                    'how_to_fix': [
                        "Check function arguments and their types",
                        "Verify you're using the right data types",
                        "Make sure you're calling functions correctly",
                        "Convert data types if needed"
                    ],
                    'prevention': [
                        "Use type hints in your functions",
                        "Check data types before operations",
                        "Read function documentation carefully"
                    ],
                    'difficulty': 'intermediate',
                    'fix_time': '3-8 minutes'
                }
            },
            'javascript': {
                'ReferenceError': {
                    'simple': "You're trying to use a variable or function that JavaScript doesn't know about.",
                    'what_happened': "JavaScript encountered a name that hasn't been declared or is out of scope.",
                    'common_causes': [
                        "Variable not declared with var, let, or const",
                        "Typo in variable name",
                        "Variable out of scope",
                        "Function called before definition"
                    ],
                    'how_to_fix': [
                        "Declare variables with var, let, or const",
                        "Check spelling of variable names",
                        "Make sure variables are in scope",
                        "Define functions before calling them"
                    ],
                    'prevention': [
                        "Always declare variables",
                        "Use 'use strict' mode",
                        "Be mindful of variable scope"
                    ],
                    'difficulty': 'beginner',
                    'fix_time': '2-5 minutes'
                },
                'SyntaxError': {
                    'simple': "There's a problem with how your JavaScript code is written.",
                    'what_happened': "JavaScript tried to parse your code but found something that doesn't follow the language rules.",
                    'common_causes': [
                        "Missing semicolons or commas",
                        "Unmatched brackets or parentheses",
                        "Invalid characters or tokens",
                        "Incorrect function syntax"
                    ],
                    'how_to_fix': [
                        "Check for missing semicolons",
                        "Verify all brackets and parentheses match",
                        "Look for typos in keywords",
                        "Use a code formatter"
                    ],
                    'prevention': [
                        "Use a linter like ESLint",
                        "Use consistent code formatting",
                        "Enable syntax highlighting"
                    ],
                    'difficulty': 'beginner',
                    'fix_time': '2-5 minutes'
                }
            }
        }

        self.logger.info("ErrorExplainer initialized")

    def explain_error(self, error: ErrorEvent, root_cause: Optional[RootCause] = None,
                     user_level: str = 'beginner') -> ErrorExplanation:
        """
        Generate user-friendly explanation for an error

        Args:
            error: Error event to explain
            root_cause: Optional root cause analysis
            user_level: User's technical level ('beginner', 'intermediate', 'advanced')

        Returns:
            Comprehensive error explanation
        """
        self.logger.info(f"Explaining error: {error.error_type}")

        # Get language context
        language = self._determine_language(error)

        # Try template-based explanation first
        explanation = self._generate_template_explanation(error, language, user_level)

        # Enhance with root cause analysis if available
        if root_cause:
            explanation = self._enhance_with_root_cause(explanation, root_cause)

        # Add AI-generated insights for complex errors
        if explanation.difficulty_level == 'advanced' or not explanation.simple_explanation:
            explanation = self._enhance_with_ai(explanation, error, user_level)

        self.logger.info(f"Generated explanation for {user_level} level user")
        return explanation

    def _determine_language(self, error: ErrorEvent) -> str:
        """Determine programming language from error context"""
        if error.file_path:
            from pathlib import Path
            path = Path(error.file_path)
            extension = path.suffix.lower()

            language_map = {
                '.py': 'python',
                '.js': 'javascript',
                '.html': 'html',
                '.css': 'css'
            }

            return language_map.get(extension, 'unknown')

        # Try to infer from error type
        if any(keyword in error.error_type.lower() for keyword in ['python', 'syntax', 'name', 'import', 'type']):
            return 'python'
        elif any(keyword in error.error_type.lower() for keyword in ['javascript', 'reference']):
            return 'javascript'

        return 'unknown'

    def _generate_template_explanation(self, error: ErrorEvent, language: str, user_level: str) -> ErrorExplanation:
        """Generate explanation using predefined templates"""
        # Get template for this error type
        templates = self.explanation_templates.get(language, {})
        template = templates.get(error.error_type, {})

        if template:
            # Use template data
            simple_explanation = template.get('simple', f"An error occurred: {error.error_type}")
            what_happened = template.get('what_happened', "An error was detected in your code.")
            why_it_happened = self._extract_specific_cause(error, template.get('common_causes', []))
            how_to_fix = template.get('how_to_fix', ["Check the error message for details"])
            prevention_tips = template.get('prevention', ["Follow best practices"])
            difficulty_level = template.get('difficulty', 'intermediate')
            estimated_fix_time = template.get('fix_time', '5-10 minutes')
        else:
            # Fallback for unknown errors
            simple_explanation = f"An error occurred: {error.error_type}"
            what_happened = f"Your code encountered a {error.error_type} error."
            why_it_happened = error.message
            how_to_fix = ["Review the error message", "Check the code around the error location"]
            prevention_tips = ["Use proper error handling", "Test your code thoroughly"]
            difficulty_level = 'intermediate'
            estimated_fix_time = '10-20 minutes'

        # Adjust explanation based on user level
        if user_level == 'beginner':
            simple_explanation = self._simplify_for_beginner(simple_explanation)
            how_to_fix = self._simplify_steps(how_to_fix)
        elif user_level == 'advanced':
            simple_explanation = self._add_technical_details(simple_explanation, error)

        return ErrorExplanation(
            error_id=error.id,
            simple_explanation=simple_explanation,
            technical_details=f"{error.error_type}: {error.message}",
            what_happened=what_happened,
            why_it_happened=why_it_happened,
            how_to_fix=how_to_fix,
            prevention_tips=prevention_tips,
            related_concepts=self._get_related_concepts(error.error_type, language),
            difficulty_level=difficulty_level,
            estimated_fix_time=estimated_fix_time
        )

    def _enhance_with_root_cause(self, explanation: ErrorExplanation, root_cause: RootCause) -> ErrorExplanation:
        """Enhance explanation with root cause analysis"""
        # Update why it happened with root cause
        explanation.why_it_happened = root_cause.primary_cause

        # Add contributing factors to prevention tips
        for factor in root_cause.contributing_factors:
            prevention_tip = f"Address: {factor}"
            if prevention_tip not in explanation.prevention_tips:
                explanation.prevention_tips.append(prevention_tip)

        # Use suggested solutions from root cause
        if root_cause.suggested_solutions:
            explanation.how_to_fix = root_cause.suggested_solutions[:3]  # Top 3 solutions

        # Adjust confidence-based messaging
        if root_cause.confidence > 0.8:
            explanation.simple_explanation += " (High confidence in this diagnosis)"
        elif root_cause.confidence < 0.5:
            explanation.simple_explanation += " (This is a preliminary analysis)"

        return explanation

    def _enhance_with_ai(self, explanation: ErrorExplanation, error: ErrorEvent, user_level: str) -> ErrorExplanation:
        """Enhance explanation with AI-generated insights"""
        try:
            # Build prompt for AI explanation
            prompt = self._build_explanation_prompt(error, user_level)

            # Get AI model
            llm = self.llm_manager.get_model('assistant')
            if llm:
                # Generate AI explanation
                ai_response = self.llm_manager.generate_with_context('assistant', prompt)

                # Parse and integrate AI insights
                ai_insights = self._parse_ai_explanation(ai_response)
                if ai_insights:
                    explanation.simple_explanation = ai_insights.get('simple', explanation.simple_explanation)
                    explanation.what_happened = ai_insights.get('what_happened', explanation.what_happened)

                    # Add AI suggestions to existing ones
                    ai_fixes = ai_insights.get('how_to_fix', [])
                    explanation.how_to_fix.extend(ai_fixes[:2])  # Add top 2 AI suggestions

        except Exception as e:
            self.logger.warning(f"Could not enhance with AI: {e}")

        return explanation

    def _extract_specific_cause(self, error: ErrorEvent, common_causes: List[str]) -> str:
        """Extract specific cause from error message"""
        error_msg = error.message.lower()

        # Look for specific patterns in the error message
        if 'missing' in error_msg:
            return "Something required is missing from your code"
        elif 'unexpected' in error_msg:
            return "The code contains something unexpected"
        elif 'not defined' in error_msg:
            return "You're using something that hasn't been defined"
        elif 'not found' in error_msg:
            return "A required file or module couldn't be found"
        elif 'invalid' in error_msg:
            return "The code contains invalid syntax or usage"

        # Return first common cause as fallback
        return common_causes[0] if common_causes else "An error occurred in your code"

    def _simplify_for_beginner(self, explanation: str) -> str:
        """Simplify explanation for beginners"""
        # Replace technical terms with simpler ones
        replacements = {
            'syntax': 'code writing rules',
            'variable': 'data container',
            'function': 'code block',
            'module': 'code file',
            'import': 'bring in',
            'exception': 'error',
            'argument': 'input',
            'parameter': 'input'
        }

        simplified = explanation
        for technical, simple in replacements.items():
            simplified = simplified.replace(technical, simple)

        return simplified

    def _simplify_steps(self, steps: List[str]) -> List[str]:
        """Simplify fix steps for beginners"""
        simplified_steps = []
        for step in steps:
            # Make steps more actionable and less technical
            if 'check' in step.lower():
                simplified_steps.append(f"Look at your code and {step.lower()}")
            elif 'use' in step.lower():
                simplified_steps.append(f"Try to {step.lower()}")
            else:
                simplified_steps.append(step)

        return simplified_steps

    def _add_technical_details(self, explanation: str, error: ErrorEvent) -> str:
        """Add technical details for advanced users"""
        technical_info = f" (Error details: {error.error_type}"
        if error.line_number:
            technical_info += f" at line {error.line_number}"
        if error.file_path:
            technical_info += f" in {error.file_path}"
        technical_info += ")"

        return explanation + technical_info

    def _get_related_concepts(self, error_type: str, language: str) -> List[str]:
        """Get related programming concepts for learning"""
        concept_map = {
            'python': {
                'SyntaxError': ['Python syntax', 'indentation', 'code structure', 'keywords'],
                'NameError': ['variables', 'scope', 'functions', 'imports'],
                'ImportError': ['modules', 'packages', 'pip', 'virtual environments'],
                'TypeError': ['data types', 'functions', 'type conversion', 'debugging']
            },
            'javascript': {
                'ReferenceError': ['variables', 'scope', 'hoisting', 'declarations'],
                'SyntaxError': ['JavaScript syntax', 'operators', 'functions', 'objects']
            }
        }

        return concept_map.get(language, {}).get(error_type, ['programming basics', 'debugging'])

    def _build_explanation_prompt(self, error: ErrorEvent, user_level: str) -> str:
        """Build prompt for AI explanation generation"""
        prompt = f"""You are a helpful programming tutor. Explain this error to a {user_level} level programmer:

Error Type: {error.error_type}
Error Message: {error.message}
"""

        if error.file_path:
            prompt += f"File: {error.file_path}\n"
        if error.line_number:
            prompt += f"Line: {error.line_number}\n"

        prompt += f"""
Please provide:
1. A simple explanation of what went wrong
2. What specifically happened in the code
3. 2-3 practical steps to fix it

Keep the explanation appropriate for a {user_level} level programmer.
Be encouraging and helpful, not intimidating."""

        return prompt

    def _parse_ai_explanation(self, ai_response: str) -> Optional[Dict[str, Any]]:
        """Parse AI response into structured explanation"""
        try:
            # Simple parsing - look for numbered sections
            lines = ai_response.split('\n')

            insights = {}
            current_section = None
            current_content = []

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # Look for section headers
                if line.startswith('1.') or 'simple explanation' in line.lower():
                    if current_section and current_content:
                        insights[current_section] = ' '.join(current_content)
                    current_section = 'simple'
                    current_content = [line.split('.', 1)[-1].strip()]
                elif line.startswith('2.') or 'what happened' in line.lower():
                    if current_section and current_content:
                        insights[current_section] = ' '.join(current_content)
                    current_section = 'what_happened'
                    current_content = [line.split('.', 1)[-1].strip()]
                elif line.startswith('3.') or 'fix' in line.lower():
                    if current_section and current_content:
                        insights[current_section] = ' '.join(current_content)
                    current_section = 'how_to_fix'
                    current_content = [line.split('.', 1)[-1].strip()]
                else:
                    if current_section:
                        current_content.append(line)

            # Add the last section
            if current_section and current_content:
                if current_section == 'how_to_fix':
                    # Split fix steps
                    fix_text = ' '.join(current_content)
                    insights[current_section] = [step.strip() for step in fix_text.split('-') if step.strip()]
                else:
                    insights[current_section] = ' '.join(current_content)

            return insights if insights else None

        except Exception as e:
            self.logger.error(f"Error parsing AI explanation: {e}")
            return None