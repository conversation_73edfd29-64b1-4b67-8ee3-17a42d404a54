["tests/test_agent.py::TestCodingAgent::test_agent_initialization", "tests/test_agent.py::TestCodingAgent::test_generate_code_basic", "tests/test_agent.py::TestCodingAgent::test_session_id_uniqueness", "tests/test_comprehensive_capability_assessment.py::test_current_generation_capabilities", "tests/test_comprehensive_capability_assessment.py::test_design_customization_control", "tests/test_comprehensive_capability_assessment.py::test_functional_specification_compliance", "tests/test_comprehensive_capability_assessment.py::test_technology_stack_flexibility", "tests/test_comprehensive_capability_assessment.py::test_universal_project_generation", "tests/test_conversation.py::TestConversation::test_add_message", "tests/test_conversation.py::TestConversation::test_auto_title_generation", "tests/test_conversation.py::TestConversation::test_conversation_creation", "tests/test_conversation.py::TestConversation::test_conversation_serialization", "tests/test_conversation.py::TestConversation::test_get_context_messages", "tests/test_conversation.py::TestConversation::test_get_formatted_context", "tests/test_conversation.py::TestConversationManager::test_add_message_to_current", "tests/test_conversation.py::TestConversationManager::test_conversation_list", "tests/test_conversation.py::TestConversationManager::test_conversation_management", "tests/test_conversation.py::TestConversationManager::test_create_conversation", "tests/test_conversation.py::TestConversationManager::test_delete_conversation", "tests/test_conversation.py::TestConversationManager::test_manager_creation", "tests/test_conversation.py::TestMessage::test_message_creation", "tests/test_conversation.py::TestMessage::test_message_from_dict", "tests/test_conversation.py::TestMessage::test_message_to_dict", "tests/test_database.py::TestDatabaseManager::test_code_generation_tracking", "tests/test_database.py::TestDatabaseManager::test_conversation_crud", "tests/test_database.py::TestDatabaseManager::test_database_error_handling", "tests/test_database.py::TestDatabaseManager::test_database_initialization", "tests/test_database.py::TestDatabaseManager::test_message_operations", "tests/test_database.py::TestDatabaseManager::test_performance_metrics", "tests/test_database.py::TestDatabaseManager::test_project_tracking", "tests/test_database_setup.py::TestDatabaseConnectionManager::test_create_sqlite_database", "tests/test_database_setup.py::TestDatabaseConnectionManager::test_setup_database_connection", "tests/test_database_setup.py::TestDatabaseConnectionManager::test_test_sqlite_connection", "tests/test_database_setup.py::TestDatabaseSelector::test_get_selection_reasoning", "tests/test_database_setup.py::TestDatabaseSelector::test_select_postgresql_for_complex_project", "tests/test_database_setup.py::TestDatabaseSelector::test_select_sqlite_for_small_project", "tests/test_database_setup.py::TestMigrationManager::test_create_migration", "tests/test_database_setup.py::TestMigrationManager::test_get_pending_migrations", "tests/test_database_setup.py::TestProjectDatabaseAnalyzer::test_analyze_javascript_models", "tests/test_database_setup.py::TestProjectDatabaseAnalyzer::test_analyze_project_requirements", "tests/test_database_setup.py::TestProjectDatabaseAnalyzer::test_analyze_python_models", "tests/test_database_setup.py::TestProjectDatabaseAnalyzer::test_extract_entities_from_text", "tests/test_database_setup.py::TestSchemaGenerator::test_generate_schema_from_requirements", "tests/test_database_setup.py::TestSchemaGenerator::test_generate_sql_script", "tests/test_database_setup.py::TestSchemaGenerator::test_map_field_type", "tests/test_dependency_database.py::test_database_setup", "tests/test_dependency_database.py::test_dependency_analysis", "tests/test_dependency_database.py::test_system_health", "tests/test_dependency_manager.py::TestCodeAnalysisEngine::test_analyze_directory", "tests/test_dependency_manager.py::TestCodeAnalysisEngine::test_analyze_javascript_file", "tests/test_dependency_manager.py::TestCodeAnalysisEngine::test_analyze_package_json", "tests/test_dependency_manager.py::TestCodeAnalysisEngine::test_analyze_python_file", "tests/test_dependency_manager.py::TestCodeAnalysisEngine::test_analyze_requirements_file", "tests/test_dependency_manager.py::TestCodeAnalysisEngine::test_nonexistent_file", "tests/test_dependency_manager.py::TestCodeAnalysisEngine::test_unsupported_file_type", "tests/test_dependency_manager.py::TestConflictResolver::test_assess_conflict_severity", "tests/test_dependency_manager.py::TestConflictResolver::test_detect_version_conflicts", "tests/test_dependency_manager.py::TestConflictResolver::test_known_incompatibilities", "tests/test_dependency_manager.py::TestConflictResolver::test_no_conflicts", "tests/test_dependency_manager.py::TestConflictResolver::test_resolve_conflicts", "tests/test_dependency_manager.py::TestDependencyCache::test_cache_expiration", "tests/test_dependency_manager.py::TestDependencyCache::test_cache_miss_and_store", "tests/test_dependency_manager.py::TestDependencyCache::test_cache_stats", "tests/test_dependency_manager.py::TestDependencyCache::test_clear_cache", "tests/test_dependency_manager.py::TestDependencyCache::test_package_info_cache", "tests/test_dependency_manager.py::TestDependencyManager::test_analyze_and_install_dependencies", "tests/test_dependency_manager.py::TestDependencyManager::test_get_dependency_summary", "tests/test_dependency_manager.py::TestEnhancedDependencyManager::test_cached_analysis", "tests/test_dependency_manager.py::TestPackageInstaller::test_install_multiple_packages", "tests/test_dependency_manager.py::TestPackageInstaller::test_install_npm_package_success", "tests/test_dependency_manager.py::TestPackageInstaller::test_install_package_failure", "tests/test_dependency_manager.py::TestPackageInstaller::test_install_pip_package_success", "tests/test_dependency_manager.py::TestPackageInstaller::test_skip_standard_library", "tests/test_end_to_end_simple.py::test_error_detection", "tests/test_end_to_end_simple.py::test_natural_language_to_code", "tests/test_end_to_end_simple.py::test_project_generation", "tests/test_end_to_end_simple.py::test_zero_coding_knowledge", "tests/test_end_to_end_todo_app.py::TestEndToEndTodoApp::test_complete_todo_app_workflow", "tests/test_explanation_system.py::TestExplanation::test_explanation_creation", "tests/test_explanation_system.py::TestExplanationContext::test_explanation_context_creation", "tests/test_explanation_system.py::TestExplanationSystem::test_add_beginner_context", "tests/test_explanation_system.py::TestExplanationSystem::test_apply_concise_communication_style", "tests/test_explanation_system.py::TestExplanationSystem::test_apply_encouraging_communication_style", "tests/test_explanation_system.py::TestExplanationSystem::test_apply_friendly_communication_style", "tests/test_explanation_system.py::TestExplanationSystem::test_apply_professional_communication_style", "tests/test_explanation_system.py::TestExplanationSystem::test_assess_user_understanding_advanced", "tests/test_explanation_system.py::TestExplanationSystem::test_assess_user_understanding_novice", "tests/test_explanation_system.py::TestExplanationSystem::test_calculate_explanation_confidence", "tests/test_explanation_system.py::TestExplanationSystem::test_determine_explanation_type_comparison", "tests/test_explanation_system.py::TestExplanationSystem::test_determine_explanation_type_concept", "tests/test_explanation_system.py::TestExplanationSystem::test_determine_explanation_type_step_by_step", "tests/test_explanation_system.py::TestExplanationSystem::test_error_handling_in_explanation", "tests/test_explanation_system.py::TestExplanationSystem::test_explain_integration_with_mock", "tests/test_explanation_system.py::TestExplanationSystem::test_generate_examples_beginner_level", "tests/test_explanation_system.py::TestExplanationSystem::test_generate_examples_with_project_context", "tests/test_explanation_system.py::TestExplanationSystem::test_generate_fallback_explanation", "tests/test_explanation_system.py::TestExplanationSystem::test_generate_follow_up_suggestions", "tests/test_explanation_system.py::TestExplanationSystem::test_generate_simple_explanation", "tests/test_explanation_system.py::TestExplanationSystem::test_identify_related_concepts", "tests/test_explanation_system.py::TestExplanationSystem::test_identify_related_concepts_beginner_filter", "tests/test_explanation_system.py::TestExplanationSystem::test_initialization", "tests/test_explanation_system.py::TestExplanationSystem::test_simplify_technical_terms", "tests/test_integration.py::TestIntegration::test_agent_initialization_integration", "tests/test_integration.py::TestIntegration::test_code_generation_with_validation_integration", "tests/test_integration.py::TestIntegration::test_component_isolation", "tests/test_integration.py::TestIntegration::test_conversation_persistence_integration", "tests/test_integration.py::TestIntegration::test_database_integration", "tests/test_integration.py::TestIntegration::test_error_handling_integration", "tests/test_integration.py::TestIntegration::test_llm_conversation_context_integration", "tests/test_integration.py::TestIntegration::test_validator_integration", "tests/test_integration.py::TestPerformanceIntegration::test_code_generation_performance", "tests/test_integration.py::TestPerformanceIntegration::test_validation_performance", "tests/test_integration.py::TestSecurityIntegration::test_dangerous_code_detection", "tests/test_integration.py::TestSecurityIntegration::test_safe_code_acceptance", "tests/test_llm_integration.py::TestLLMManager::test_error_handling", "tests/test_llm_integration.py::TestLLMManager::test_generate_response_basic", "tests/test_llm_integration.py::TestLLMManager::test_get_model_config", "tests/test_llm_integration.py::TestLLMManager::test_llm_manager_initialization", "tests/test_llm_integration.py::TestLLMManager::test_model_configurations", "tests/test_llm_integration.py::TestLLMManager::test_ollama_connection", "tests/test_llm_integration.py::TestModelConfig::test_model_config_creation", "tests/test_logging.py::TestExceptions::test_base_exception", "tests/test_logging.py::TestExceptions::test_exception_inheritance", "tests/test_logging.py::TestExceptions::test_llm_connection_error", "tests/test_logging.py::TestLogging::test_config_loading", "tests/test_logging.py::TestLogging::test_logger_creation", "tests/test_logging.py::TestLogging::test_logger_instance", "tests/test_monitoring.py::TestCircuitBreaker::test_circuit_breaker_closed_state", "tests/test_monitoring.py::TestCircuitBreaker::test_circuit_breaker_decorator", "tests/test_monitoring.py::TestCircuitBreaker::test_circuit_breaker_opens_after_failures", "tests/test_monitoring.py::TestCircuitBreaker::test_circuit_breaker_recovery", "tests/test_monitoring.py::TestMonitoringIntegration::test_database_health_check", "tests/test_monitoring.py::TestMonitoringIntegration::test_global_performance_monitor", "tests/test_monitoring.py::TestMonitoringIntegration::test_ollama_health_check", "tests/test_monitoring.py::TestMonitoringIntegration::test_performance_monitor_database_integration", "tests/test_monitoring.py::TestPerformanceMonitor::test_measure_operation_context_manager", "tests/test_monitoring.py::TestPerformanceMonitor::test_measure_operation_with_exception", "tests/test_monitoring.py::TestPerformanceMonitor::test_performance_monitor_initialization", "tests/test_monitoring.py::TestPerformanceMonitor::test_performance_summary", "tests/test_monitoring.py::TestPerformanceMonitor::test_system_health_check", "tests/test_monitoring.py::TestPerformanceMonitor::test_system_health_warning_status", "tests/test_monitoring.py::TestPerformanceThresholds::test_threshold_warning", "tests/test_nlp_integration.py::TestNLPIntegrationVariedInputs::test_ambiguous_and_vague_inputs", "tests/test_nlp_integration.py::TestNLPIntegrationVariedInputs::test_beginner_communication_style", "tests/test_nlp_integration.py::TestNLPIntegrationVariedInputs::test_casual_communication_style", "tests/test_nlp_integration.py::TestNLPIntegrationVariedInputs::test_error_handling_with_invalid_inputs", "tests/test_nlp_integration.py::TestNLPIntegrationVariedInputs::test_explanation_adaptation", "tests/test_nlp_integration.py::TestNLPIntegrationVariedInputs::test_formal_technical_communication", "tests/test_nlp_integration.py::TestNLPIntegrationVariedInputs::test_integration_workflow", "tests/test_nlp_integration.py::TestNLPIntegrationVariedInputs::test_multilingual_and_mixed_inputs", "tests/test_nlp_integration.py::TestNLPIntegrationVariedInputs::test_question_generation_variety", "tests/test_nlp_integration.py::TestNLPIntegrationVariedInputs::test_specific_domain_projects", "tests/test_personality_system.py::TestConversationMemory::test_add_long_term_memory", "tests/test_personality_system.py::TestConversationMemory::test_add_session_memory", "tests/test_personality_system.py::TestConversationMemory::test_conversation_memory_creation", "tests/test_personality_system.py::TestPersonalityProfile::test_personality_profile_creation", "tests/test_personality_system.py::TestPersonalityProfile::test_personality_profile_custom_traits", "tests/test_personality_system.py::TestPersonalitySystem::test_add_encouragement", "tests/test_personality_system.py::TestPersonalitySystem::test_add_enthusiasm", "tests/test_personality_system.py::TestPersonalitySystem::test_conversation_memory_long_term_limit", "tests/test_personality_system.py::TestPersonalitySystem::test_conversation_memory_session_limit", "tests/test_personality_system.py::TestPersonalitySystem::test_determine_personality_adjustments_formal_preference", "tests/test_personality_system.py::TestPersonalitySystem::test_determine_personality_adjustments_frustrated_context", "tests/test_personality_system.py::TestPersonalitySystem::test_determine_personality_adjustments_new_user", "tests/test_personality_system.py::TestPersonalitySystem::test_generate_greeting_familiar_user", "tests/test_personality_system.py::TestPersonalitySystem::test_generate_greeting_first_meeting", "tests/test_personality_system.py::TestPersonalitySystem::test_generate_greeting_returning_user", "tests/test_personality_system.py::TestPersonalitySystem::test_get_conversation_context", "tests/test_personality_system.py::TestPersonalitySystem::test_get_or_create_memory_existing_user", "tests/test_personality_system.py::TestPersonalitySystem::test_get_or_create_memory_new_user", "tests/test_personality_system.py::TestPersonalitySystem::test_get_personalized_response", "tests/test_personality_system.py::TestPersonalitySystem::test_get_personalized_response_error_handling", "tests/test_personality_system.py::TestPersonalitySystem::test_initialization", "tests/test_personality_system.py::TestPersonalitySystem::test_make_more_casual", "tests/test_personality_system.py::TestPersonalitySystem::test_make_more_professional", "tests/test_personality_system.py::TestPersonalitySystem::test_remember_user_preference", "tests/test_phase3_component_verification.py::test_phase3_components", "tests/test_phase3_comprehensive.py::TestPhase3CodeValidation::test_advanced_html_validation", "tests/test_phase3_comprehensive.py::TestPhase3CodeValidation::test_advanced_javascript_validation", "tests/test_phase3_comprehensive.py::TestPhase3CodeValidation::test_advanced_python_validation", "tests/test_phase3_comprehensive.py::TestPhase3ErrorDetectionEngine::test_auto_fix_generator", "tests/test_phase3_comprehensive.py::TestPhase3ErrorDetectionEngine::test_error_explainer", "tests/test_phase3_comprehensive.py::TestPhase3ErrorDetectionEngine::test_error_monitor_functionality", "tests/test_phase3_comprehensive.py::TestPhase3ErrorDetectionEngine::test_error_pattern_learner", "tests/test_phase3_comprehensive.py::TestPhase3ErrorDetectionEngine::test_root_cause_analyzer", "tests/test_phase3_comprehensive.py::TestPhase3Integration::test_integration_workflow", "tests/test_phase3_comprehensive.py::TestPhase3Integration::test_natural_conversation_interface", "tests/test_phase3_comprehensive.py::TestPhase3Integration::test_zero_coding_knowledge_requirement", "tests/test_phase3_simple.py::test_basic_functionality", "tests/test_phase3_simple.py::test_code_validation_logic", "tests/test_phase3_simple.py::test_project_structure_logic", "tests/test_phase3_validation.py::test_basic_imports", "tests/test_phase3_validation.py::test_code_validator_functionality", "tests/test_phase3_validation.py::test_database_components", "tests/test_phase3_validation.py::test_dependency_manager", "tests/test_phase3_validation.py::test_requirements_parser", "tests/test_phase3_validation.py::test_simple_workflow", "tests/test_question_generator.py::TestQuestion::test_question_creation", "tests/test_question_generator.py::TestQuestionGenerator::test_analyze_missing_information", "tests/test_question_generator.py::TestQuestionGenerator::test_error_handling_in_generation", "tests/test_question_generator.py::TestQuestionGenerator::test_fallback_questions", "tests/test_question_generator.py::TestQuestionGenerator::test_generate_questions_integration", "tests/test_question_generator.py::TestQuestionGenerator::test_initialization", "tests/test_question_generator.py::TestQuestionGenerator::test_level_specific_questions_beginner", "tests/test_question_generator.py::TestQuestionGenerator::test_level_specific_questions_expert", "tests/test_question_generator.py::TestQuestionGenerator::test_llm_question_parsing", "tests/test_question_generator.py::TestQuestionGenerator::test_pattern_question_generation_api", "tests/test_question_generator.py::TestQuestionGenerator::test_pattern_question_generation_web_app", "tests/test_question_generator.py::TestQuestionGenerator::test_question_prioritization", "tests/test_question_generator.py::TestQuestionGenerator::test_question_selection_variety", "tests/test_question_generator.py::TestQuestionGenerator::test_similar_question_detection", "tests/test_requirements_parser.py::TestProjectRequirement::test_project_requirement_creation", "tests/test_requirements_parser.py::TestRequirementParser::test_clarifications_identification", "tests/test_requirements_parser.py::TestRequirementParser::test_complexity_detection", "tests/test_requirements_parser.py::TestRequirementParser::test_confidence_score_calculation", "tests/test_requirements_parser.py::TestRequirementParser::test_error_handling", "tests/test_requirements_parser.py::TestRequirementParser::test_feature_detection", "tests/test_requirements_parser.py::TestRequirementParser::test_initialization", "tests/test_requirements_parser.py::TestRequirementParser::test_llm_analysis_with_mock", "tests/test_requirements_parser.py::TestRequirementParser::test_parse_requirements_integration", "tests/test_requirements_parser.py::TestRequirementParser::test_project_suggestions", "tests/test_requirements_parser.py::TestRequirementParser::test_project_type_detection_api", "tests/test_requirements_parser.py::TestRequirementParser::test_project_type_detection_web_app", "tests/test_requirements_parser.py::TestRequirementParser::test_technology_detection", "tests/test_requirements_parser.py::TestRequirementParser::test_user_profile_creation", "tests/test_requirements_parser.py::TestUserProfile::test_user_profile_creation", "tests/test_security.py::TestCodeSandbox::test_attribute_access_detection", "tests/test_security.py::TestCodeSandbox::test_dangerous_code_detection", "tests/test_security.py::TestCodeSandbox::test_safe_code_detection", "tests/test_security.py::TestInputSanitizer::test_dangerous_pattern_detection", "tests/test_security.py::TestInputSanitizer::test_filename_sanitization", "tests/test_security.py::TestInputSanitizer::test_html_escape_sanitization", "tests/test_security.py::TestInputSanitizer::test_json_validation", "tests/test_security.py::TestInputSanitizer::test_length_validation", "tests/test_security.py::TestInputSanitizer::test_safe_text_sanitization", "tests/test_security.py::TestRateLimiter::test_multiple_clients", "tests/test_security.py::TestRateLimiter::test_rate_limiting", "tests/test_security.py::TestRateLimiter::test_remaining_requests", "tests/test_security.py::TestSecurityAuditor::test_security_event_logging", "tests/test_security.py::TestSecurityIntegration::test_dangerous_input_workflow", "tests/test_security.py::TestSecurityIntegration::test_security_components_integration"]