# AI Coding Agent - Dependency Management System
"""
Comprehensive dependency management system for automatic package detection,
installation, and conflict resolution across multiple package managers.
"""

import ast
import json
import re
import subprocess
import tempfile
import time
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple, Union, Any
import logging

from src.logger import get_logger
from src.exceptions import CodingAgentError
from src.config import get_config
from src.monitoring import get_performance_monitor

config = get_config()


class PackageManager(Enum):
    """Supported package managers"""
    PIP = "pip"
    NPM = "npm"
    YARN = "yarn"
    CONDA = "conda"


class DependencyType(Enum):
    """Types of dependencies"""
    RUNTIME = "runtime"
    DEVELOPMENT = "development"
    OPTIONAL = "optional"
    PEER = "peer"


@dataclass
class PackageInfo:
    """Information about a detected package"""
    name: str
    version: Optional[str] = None
    package_manager: PackageManager = PackageManager.PIP
    dependency_type: DependencyType = DependencyType.RUNTIME
    source_file: Optional[str] = None
    line_number: Optional[int] = None
    is_standard_library: bool = False
    alternatives: List[str] = field(default_factory=list)
    description: Optional[str] = None


@dataclass
class ConflictInfo:
    """Information about version conflicts"""
    package_name: str
    required_versions: List[str]
    current_version: Optional[str]
    conflicting_packages: List[str]
    resolution_strategy: Optional[str] = None
    severity: str = "medium"  # low, medium, high, critical


class DependencyAnalysisError(CodingAgentError):
    """Raised when dependency analysis fails"""
    pass


class PackageInstallationError(CodingAgentError):
    """Raised when package installation fails"""
    pass


class CodeAnalysisEngine:
    """
    Advanced code analysis engine for detecting package dependencies
    across multiple programming languages and frameworks.
    """
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.performance_monitor = get_performance_monitor()
        
        # Standard library modules for different languages
        self.python_stdlib = self._load_python_stdlib()
        self.javascript_builtins = self._load_javascript_builtins()
        
        # Common package mappings (import name -> package name)
        self.package_mappings = self._load_package_mappings()
        
        # Framework-specific dependency patterns
        self.framework_patterns = self._load_framework_patterns()
        
        self.logger.info("CodeAnalysisEngine initialized with comprehensive language support")
    
    def analyze_file(self, file_path: Union[str, Path]) -> List[PackageInfo]:
        """
        Analyze a single file for package dependencies.
        
        Args:
            file_path: Path to the file to analyze
            
        Returns:
            List of detected package information
            
        Raises:
            DependencyAnalysisError: If analysis fails
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise DependencyAnalysisError(f"File not found: {file_path}")
        
        try:
            with self.performance_monitor.measure_operation("file_analysis"):
                # Determine file type and use appropriate analyzer
                if file_path.suffix == '.py':
                    return self._analyze_python_file(file_path)
                elif file_path.suffix in ['.js', '.jsx', '.ts', '.tsx']:
                    return self._analyze_javascript_file(file_path)
                elif file_path.name in ['requirements.txt', 'requirements-dev.txt']:
                    return self._analyze_requirements_file(file_path)
                elif file_path.name == 'package.json':
                    return self._analyze_package_json(file_path)
                elif file_path.suffix in ['.html', '.htm']:
                    return self._analyze_html_file(file_path)
                else:
                    self.logger.debug(f"Unsupported file type: {file_path.suffix}")
                    return []
                    
        except Exception as e:
            self.logger.error(f"Failed to analyze file {file_path}: {str(e)}")
            raise DependencyAnalysisError(f"Analysis failed for {file_path}: {str(e)}")
    
    def analyze_directory(self, directory_path: Union[str, Path], 
                         recursive: bool = True) -> Dict[str, List[PackageInfo]]:
        """
        Analyze all supported files in a directory for dependencies.
        
        Args:
            directory_path: Path to directory to analyze
            recursive: Whether to analyze subdirectories
            
        Returns:
            Dictionary mapping file paths to detected packages
        """
        directory_path = Path(directory_path)
        results = {}
        
        if not directory_path.exists():
            raise DependencyAnalysisError(f"Directory not found: {directory_path}")
        
        # Supported file patterns
        patterns = [
            '*.py', '*.js', '*.jsx', '*.ts', '*.tsx', '*.html', '*.htm',
            'requirements*.txt', 'package.json', 'setup.py', 'pyproject.toml'
        ]
        
        try:
            with self.performance_monitor.measure_operation("directory_analysis"):
                for pattern in patterns:
                    if recursive:
                        files = directory_path.rglob(pattern)
                    else:
                        files = directory_path.glob(pattern)
                    
                    for file_path in files:
                        try:
                            packages = self.analyze_file(file_path)
                            if packages:
                                results[str(file_path)] = packages
                        except DependencyAnalysisError as e:
                            self.logger.warning(f"Skipping file {file_path}: {e}")
                            continue
                
                self.logger.info(f"Analyzed {len(results)} files in {directory_path}")
                return results
                
        except Exception as e:
            self.logger.error(f"Failed to analyze directory {directory_path}: {str(e)}")
            raise DependencyAnalysisError(f"Directory analysis failed: {str(e)}")

    def _analyze_python_file(self, file_path: Path) -> List[PackageInfo]:
        """Analyze Python file for import statements and dependencies"""
        packages = []

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Parse AST to extract imports
            tree = ast.parse(content)

            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        package_info = self._process_python_import(
                            alias.name, file_path, node.lineno
                        )
                        if package_info:
                            packages.append(package_info)

                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        package_info = self._process_python_import(
                            node.module, file_path, node.lineno
                        )
                        if package_info:
                            packages.append(package_info)

            # Also check for requirements in comments or docstrings
            packages.extend(self._extract_requirements_from_comments(content, file_path))

        except SyntaxError as e:
            self.logger.warning(f"Syntax error in {file_path}: {e}")
        except Exception as e:
            self.logger.error(f"Error analyzing Python file {file_path}: {e}")

        return packages

    def _analyze_javascript_file(self, file_path: Path) -> List[PackageInfo]:
        """Analyze JavaScript/TypeScript file for import/require statements"""
        packages = []

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Regex patterns for different import styles
            patterns = [
                # ES6 imports: import ... from 'package'
                r"import\s+.*?\s+from\s+['\"]([^'\"]+)['\"]",
                # ES6 side-effect imports: import 'package'
                r"import\s+['\"]([^'\"]+)['\"]",
                # CommonJS: require('package')
                r"require\s*\(\s*['\"]([^'\"]+)['\"]\s*\)",
                # Dynamic imports: import('package')
                r"import\s*\(\s*['\"]([^'\"]+)['\"]\s*\)",
            ]

            for pattern in patterns:
                matches = re.finditer(pattern, content, re.MULTILINE)
                for match in matches:
                    package_name = match.group(1)
                    line_number = content[:match.start()].count('\n') + 1

                    package_info = self._process_javascript_import(
                        package_name, file_path, line_number
                    )
                    if package_info:
                        packages.append(package_info)

        except Exception as e:
            self.logger.error(f"Error analyzing JavaScript file {file_path}: {e}")

        return packages

    def _analyze_requirements_file(self, file_path: Path) -> List[PackageInfo]:
        """Analyze requirements.txt file"""
        packages = []

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                if line and not line.startswith('#'):
                    package_info = self._parse_requirement_line(line, file_path, line_num)
                    if package_info:
                        packages.append(package_info)

        except Exception as e:
            self.logger.error(f"Error analyzing requirements file {file_path}: {e}")

        return packages

    def _analyze_package_json(self, file_path: Path) -> List[PackageInfo]:
        """Analyze package.json file"""
        packages = []

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # Process different dependency types
            dependency_sections = {
                'dependencies': DependencyType.RUNTIME,
                'devDependencies': DependencyType.DEVELOPMENT,
                'peerDependencies': DependencyType.PEER,
                'optionalDependencies': DependencyType.OPTIONAL
            }

            for section, dep_type in dependency_sections.items():
                if section in data:
                    for package_name, version in data[section].items():
                        package_info = PackageInfo(
                            name=package_name,
                            version=version,
                            package_manager=PackageManager.NPM,
                            dependency_type=dep_type,
                            source_file=str(file_path)
                        )
                        packages.append(package_info)

        except Exception as e:
            self.logger.error(f"Error analyzing package.json {file_path}: {e}")

        return packages

    def _analyze_html_file(self, file_path: Path) -> List[PackageInfo]:
        """Analyze HTML file for CDN links and script references"""
        packages = []

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Look for CDN links and script tags
            patterns = [
                # CDN links like https://cdn.jsdelivr.net/npm/package@version
                r'https://cdn\.jsdelivr\.net/npm/([^@/]+)(?:@([^/]+))?',
                # unpkg CDN
                r'https://unpkg\.com/([^@/]+)(?:@([^/]+))?',
                # cdnjs
                r'https://cdnjs\.cloudflare\.com/ajax/libs/([^/]+)/([^/]+)',
            ]

            for pattern in patterns:
                matches = re.finditer(pattern, content, re.MULTILINE)
                for match in matches:
                    package_name = match.group(1)
                    version = match.group(2) if len(match.groups()) > 1 else None
                    line_number = content[:match.start()].count('\n') + 1

                    package_info = PackageInfo(
                        name=package_name,
                        version=version,
                        package_manager=PackageManager.NPM,
                        dependency_type=DependencyType.RUNTIME,
                        source_file=str(file_path),
                        line_number=line_number
                    )
                    packages.append(package_info)

        except Exception as e:
            self.logger.error(f"Error analyzing HTML file {file_path}: {e}")

        return packages

    def _process_python_import(self, module_name: str, file_path: Path,
                              line_number: int) -> Optional[PackageInfo]:
        """Process a Python import statement and determine package info"""
        # Split module name to get top-level package
        top_level = module_name.split('.')[0]

        # Check if it's a standard library module
        if top_level in self.python_stdlib:
            return PackageInfo(
                name=top_level,
                package_manager=PackageManager.PIP,
                source_file=str(file_path),
                line_number=line_number,
                is_standard_library=True
            )

        # Check for package name mappings
        package_name = self.package_mappings.get(top_level, top_level)

        return PackageInfo(
            name=package_name,
            package_manager=PackageManager.PIP,
            source_file=str(file_path),
            line_number=line_number,
            is_standard_library=False
        )

    def _process_javascript_import(self, package_name: str, file_path: Path,
                                  line_number: int) -> Optional[PackageInfo]:
        """Process a JavaScript import statement"""
        # Skip relative imports
        if package_name.startswith('.') or package_name.startswith('/'):
            return None

        # Skip built-in Node.js modules
        if package_name in self.javascript_builtins:
            return PackageInfo(
                name=package_name,
                package_manager=PackageManager.NPM,
                source_file=str(file_path),
                line_number=line_number,
                is_standard_library=True
            )

        # Extract scoped package name if needed
        if package_name.startswith('@'):
            # For scoped packages like @babel/core, keep the full scoped name
            parts = package_name.split('/')
            if len(parts) >= 2:
                package_name = f"{parts[0]}/{parts[1]}"
            # Don't modify further for scoped packages
        else:
            # For regular packages, take only the first part
            package_name = package_name.split('/')[0]

        return PackageInfo(
            name=package_name,
            package_manager=PackageManager.NPM,
            source_file=str(file_path),
            line_number=line_number,
            is_standard_library=False
        )

    def _parse_requirement_line(self, line: str, file_path: Path,
                               line_number: int) -> Optional[PackageInfo]:
        """Parse a line from requirements.txt"""
        # Handle different requirement formats
        # package==1.0.0, package>=1.0.0, package~=1.0.0, etc.

        # Remove comments
        line = line.split('#')[0].strip()
        if not line:
            return None

        # Parse package name and version specifier
        version_pattern = r'^([a-zA-Z0-9_.-]+)([><=!~]+.*)?$'
        match = re.match(version_pattern, line)

        if match:
            package_name = match.group(1)
            version_spec = match.group(2) if match.group(2) else None

            return PackageInfo(
                name=package_name,
                version=version_spec,
                package_manager=PackageManager.PIP,
                source_file=str(file_path),
                line_number=line_number,
                is_standard_library=False
            )

        return None

    def _extract_requirements_from_comments(self, content: str,
                                           file_path: Path) -> List[PackageInfo]:
        """Extract package requirements from comments and docstrings"""
        packages = []

        # Look for pip install commands in comments
        pip_pattern = r'#.*pip install\s+([a-zA-Z0-9_.-]+)'
        matches = re.finditer(pip_pattern, content, re.MULTILINE | re.IGNORECASE)

        for match in matches:
            package_name = match.group(1)
            line_number = content[:match.start()].count('\n') + 1

            package_info = PackageInfo(
                name=package_name,
                package_manager=PackageManager.PIP,
                source_file=str(file_path),
                line_number=line_number,
                is_standard_library=False
            )
            packages.append(package_info)

        return packages

    def _load_python_stdlib(self) -> Set[str]:
        """Load Python standard library module names"""
        # Common Python standard library modules
        stdlib_modules = {
            'os', 'sys', 'json', 'time', 'datetime', 'math', 'random', 're',
            'collections', 'itertools', 'functools', 'operator', 'pathlib',
            'urllib', 'http', 'email', 'html', 'xml', 'csv', 'sqlite3',
            'threading', 'multiprocessing', 'subprocess', 'socket', 'ssl',
            'hashlib', 'hmac', 'base64', 'binascii', 'struct', 'array',
            'weakref', 'copy', 'pickle', 'shelve', 'dbm', 'zlib', 'gzip',
            'bz2', 'lzma', 'zipfile', 'tarfile', 'configparser', 'logging',
            'getopt', 'argparse', 'shlex', 'glob', 'fnmatch', 'linecache',
            'tempfile', 'shutil', 'stat', 'filecmp', 'fileinput', 'io',
            'stringio', 'textwrap', 'unicodedata', 'codecs', 'locale',
            'gettext', 'calendar', 'heapq', 'bisect', 'pprint', 'reprlib',
            'enum', 'types', 'inspect', 'importlib', 'pkgutil', 'modulefinder',
            'runpy', 'ast', 'symtable', 'symbol', 'token', 'keyword',
            'tokenize', 'tabnanny', 'pyclbr', 'py_compile', 'compileall',
            'dis', 'pickletools', 'platform', 'errno', 'ctypes', 'mmap',
            'winreg', 'winsound', 'posix', 'pwd', 'spwd', 'grp', 'crypt',
            'termios', 'tty', 'pty', 'fcntl', 'pipes', 'resource', 'nis',
            'syslog', 'optparse', 'imp'
        }
        return stdlib_modules

    def _load_javascript_builtins(self) -> Set[str]:
        """Load JavaScript/Node.js built-in module names"""
        builtins = {
            # Node.js built-in modules
            'assert', 'buffer', 'child_process', 'cluster', 'crypto',
            'dgram', 'dns', 'domain', 'events', 'fs', 'http', 'https',
            'net', 'os', 'path', 'punycode', 'querystring', 'readline',
            'repl', 'stream', 'string_decoder', 'tls', 'tty', 'url',
            'util', 'v8', 'vm', 'zlib', 'constants', 'module', 'process',
            'console', 'timers', 'worker_threads', 'perf_hooks'
        }
        return builtins

    def _load_package_mappings(self) -> Dict[str, str]:
        """Load mappings from import names to package names"""
        mappings = {
            # Python package mappings
            'cv2': 'opencv-python',
            'PIL': 'Pillow',
            'sklearn': 'scikit-learn',
            'yaml': 'PyYAML',
            'bs4': 'beautifulsoup4',
            'dateutil': 'python-dateutil',
            'serial': 'pyserial',
            'psycopg2': 'psycopg2-binary',
            'MySQLdb': 'mysqlclient',
            'pymongo': 'pymongo',
            'redis': 'redis',
            'celery': 'celery',
            'flask': 'Flask',
            'django': 'Django',
            'fastapi': 'fastapi',
            'sqlalchemy': 'SQLAlchemy',
            'alembic': 'alembic',
            'pytest': 'pytest',
            'numpy': 'numpy',
            'pandas': 'pandas',
            'matplotlib': 'matplotlib',
            'seaborn': 'seaborn',
            'plotly': 'plotly',
            'dash': 'dash',
            'streamlit': 'streamlit',
            'gradio': 'gradio',
            'transformers': 'transformers',
            'torch': 'torch',
            'tensorflow': 'tensorflow',
            'keras': 'keras',
            'langchain': 'langchain',
            'openai': 'openai',
            'anthropic': 'anthropic',
            'ollama': 'ollama'
        }
        return mappings

    def _load_framework_patterns(self) -> Dict[str, List[str]]:
        """Load framework-specific dependency patterns"""
        patterns = {
            'flask': ['flask', 'flask-cors', 'flask-sqlalchemy', 'flask-migrate'],
            'django': ['django', 'djangorestframework', 'django-cors-headers'],
            'fastapi': ['fastapi', 'uvicorn', 'pydantic', 'python-multipart'],
            'react': ['react', 'react-dom', '@types/react', '@types/react-dom'],
            'vue': ['vue', '@vue/cli-service', 'vue-router', 'vuex'],
            'angular': ['@angular/core', '@angular/common', '@angular/router'],
            'express': ['express', 'cors', 'helmet', 'morgan'],
            'next': ['next', 'react', 'react-dom'],
            'nuxt': ['nuxt', 'vue'],
            'svelte': ['svelte', '@sveltejs/kit'],
            'streamlit': ['streamlit', 'pandas', 'numpy'],
            'gradio': ['gradio', 'numpy', 'pandas'],
            'dash': ['dash', 'plotly', 'pandas']
        }
        return patterns


class PackageInstaller:
    """
    Automatic package installer supporting multiple package managers
    with intelligent error handling and conflict resolution.
    """

    def __init__(self):
        self.logger = get_logger(__name__)
        self.performance_monitor = get_performance_monitor()

        # Cache for installed packages to avoid redundant checks
        self._installed_cache = {}
        self._cache_timeout = 300  # 5 minutes

        self.logger.info("PackageInstaller initialized with multi-manager support")

    def install_package(self, package_info: PackageInfo,
                       force_reinstall: bool = False) -> bool:
        """
        Install a single package using the appropriate package manager.

        Args:
            package_info: Package information to install
            force_reinstall: Whether to reinstall if already installed

        Returns:
            True if installation successful, False otherwise
        """
        try:
            with self.performance_monitor.measure_operation("package_installation"):
                # Skip standard library packages
                if package_info.is_standard_library:
                    self.logger.debug(f"Skipping standard library package: {package_info.name}")
                    return True

                # Check if already installed (unless force reinstall)
                if not force_reinstall and self._is_package_installed(package_info):
                    self.logger.info(f"Package {package_info.name} already installed")
                    return True

                # Install using appropriate package manager
                if package_info.package_manager == PackageManager.PIP:
                    return self._install_pip_package(package_info)
                elif package_info.package_manager == PackageManager.NPM:
                    return self._install_npm_package(package_info)
                elif package_info.package_manager == PackageManager.YARN:
                    return self._install_yarn_package(package_info)
                elif package_info.package_manager == PackageManager.CONDA:
                    return self._install_conda_package(package_info)
                else:
                    self.logger.error(f"Unsupported package manager: {package_info.package_manager}")
                    return False

        except Exception as e:
            self.logger.error(f"Failed to install package {package_info.name}: {str(e)}")
            raise PackageInstallationError(f"Installation failed: {str(e)}")

    def install_packages(self, packages: List[PackageInfo],
                        max_parallel: int = 3) -> Dict[str, bool]:
        """
        Install multiple packages, optionally in parallel.

        Args:
            packages: List of packages to install
            max_parallel: Maximum number of parallel installations

        Returns:
            Dictionary mapping package names to installation success status
        """
        results = {}

        # Group packages by manager for efficient batch installation
        grouped_packages = self._group_packages_by_manager(packages)

        for manager, package_list in grouped_packages.items():
            self.logger.info(f"Installing {len(package_list)} packages with {manager.value}")

            for package_info in package_list:
                try:
                    success = self.install_package(package_info)
                    results[package_info.name] = success

                    if success:
                        self.logger.info(f"✓ Successfully installed {package_info.name}")
                    else:
                        self.logger.warning(f"✗ Failed to install {package_info.name}")

                except PackageInstallationError as e:
                    self.logger.error(f"✗ Installation error for {package_info.name}: {e}")
                    results[package_info.name] = False

        return results

    def _is_package_installed(self, package_info: PackageInfo) -> bool:
        """Check if a package is already installed"""
        cache_key = f"{package_info.package_manager.value}:{package_info.name}"

        # Check cache first
        if cache_key in self._installed_cache:
            cached_time, is_installed = self._installed_cache[cache_key]
            if time.time() - cached_time < self._cache_timeout:
                return is_installed

        # Check actual installation status
        try:
            if package_info.package_manager == PackageManager.PIP:
                result = subprocess.run(
                    ['pip', 'show', package_info.name],
                    capture_output=True, text=True, timeout=30
                )
                is_installed = result.returncode == 0

            elif package_info.package_manager == PackageManager.NPM:
                result = subprocess.run(
                    ['npm', 'list', package_info.name],
                    capture_output=True, text=True, timeout=30
                )
                is_installed = result.returncode == 0

            else:
                # For other managers, assume not installed
                is_installed = False

            # Cache the result
            self._installed_cache[cache_key] = (time.time(), is_installed)
            return is_installed

        except Exception as e:
            self.logger.warning(f"Failed to check if {package_info.name} is installed: {e}")
            return False

    def _install_pip_package(self, package_info: PackageInfo) -> bool:
        """Install a Python package using pip"""
        try:
            # Build pip install command
            cmd = ['pip', 'install']

            if package_info.version:
                # Handle version specifiers
                if package_info.version.startswith(('==', '>=', '<=', '>', '<', '~=', '!=')):
                    package_spec = f"{package_info.name}{package_info.version}"
                else:
                    package_spec = f"{package_info.name}=={package_info.version}"
            else:
                package_spec = package_info.name

            cmd.append(package_spec)

            self.logger.info(f"Installing Python package: {package_spec}")

            # Execute pip install
            result = subprocess.run(
                cmd, capture_output=True, text=True, timeout=300
            )

            if result.returncode == 0:
                self.logger.info(f"Successfully installed {package_info.name}")
                # Clear cache for this package
                cache_key = f"pip:{package_info.name}"
                if cache_key in self._installed_cache:
                    del self._installed_cache[cache_key]
                return True
            else:
                self.logger.error(f"pip install failed: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            self.logger.error(f"pip install timeout for {package_info.name}")
            return False
        except Exception as e:
            self.logger.error(f"pip install error: {str(e)}")
            return False

    def _install_npm_package(self, package_info: PackageInfo) -> bool:
        """Install a JavaScript package using npm"""
        try:
            # Build npm install command
            cmd = ['npm', 'install']

            if package_info.version:
                package_spec = f"{package_info.name}@{package_info.version}"
            else:
                package_spec = package_info.name

            # Add dependency type flags
            if package_info.dependency_type == DependencyType.DEVELOPMENT:
                cmd.append('--save-dev')
            elif package_info.dependency_type == DependencyType.OPTIONAL:
                cmd.append('--save-optional')

            cmd.append(package_spec)

            self.logger.info(f"Installing npm package: {package_spec}")

            # Execute npm install
            result = subprocess.run(
                cmd, capture_output=True, text=True, timeout=300
            )

            if result.returncode == 0:
                self.logger.info(f"Successfully installed {package_info.name}")
                return True
            else:
                self.logger.error(f"npm install failed: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            self.logger.error(f"npm install timeout for {package_info.name}")
            return False
        except Exception as e:
            self.logger.error(f"npm install error: {str(e)}")
            return False

    def _install_yarn_package(self, package_info: PackageInfo) -> bool:
        """Install a JavaScript package using yarn"""
        try:
            cmd = ['yarn', 'add']

            if package_info.version:
                package_spec = f"{package_info.name}@{package_info.version}"
            else:
                package_spec = package_info.name

            if package_info.dependency_type == DependencyType.DEVELOPMENT:
                cmd.append('--dev')

            cmd.append(package_spec)

            self.logger.info(f"Installing yarn package: {package_spec}")

            result = subprocess.run(
                cmd, capture_output=True, text=True, timeout=300
            )

            return result.returncode == 0

        except Exception as e:
            self.logger.error(f"yarn install error: {str(e)}")
            return False

    def _install_conda_package(self, package_info: PackageInfo) -> bool:
        """Install a package using conda"""
        try:
            cmd = ['conda', 'install', '-y']

            if package_info.version:
                package_spec = f"{package_info.name}={package_info.version}"
            else:
                package_spec = package_info.name

            cmd.append(package_spec)

            self.logger.info(f"Installing conda package: {package_spec}")

            result = subprocess.run(
                cmd, capture_output=True, text=True, timeout=300
            )

            return result.returncode == 0

        except Exception as e:
            self.logger.error(f"conda install error: {str(e)}")
            return False

    def _group_packages_by_manager(self, packages: List[PackageInfo]) -> Dict[PackageManager, List[PackageInfo]]:
        """Group packages by their package manager"""
        grouped = {}

        for package in packages:
            if package.package_manager not in grouped:
                grouped[package.package_manager] = []
            grouped[package.package_manager].append(package)

        return grouped


class ConflictResolver:
    """
    Intelligent version conflict detection and resolution system
    for managing package dependencies across different managers.
    """

    def __init__(self):
        self.logger = get_logger(__name__)
        self.performance_monitor = get_performance_monitor()

        # Known compatibility matrices for common packages
        self.compatibility_rules = self._load_compatibility_rules()

        self.logger.info("ConflictResolver initialized with compatibility rules")

    def detect_conflicts(self, packages: List[PackageInfo]) -> List[ConflictInfo]:
        """
        Detect version conflicts among a list of packages.

        Args:
            packages: List of packages to check for conflicts

        Returns:
            List of detected conflicts
        """
        conflicts = []

        try:
            with self.performance_monitor.measure_operation("conflict_detection"):
                # Group packages by name to find version conflicts
                package_groups = self._group_packages_by_name(packages)

                for package_name, package_list in package_groups.items():
                    if len(package_list) > 1:
                        # Multiple versions of the same package
                        conflict = self._analyze_version_conflict(package_name, package_list)
                        if conflict:
                            conflicts.append(conflict)

                # Check for known incompatibilities
                incompatibility_conflicts = self._check_known_incompatibilities(packages)
                conflicts.extend(incompatibility_conflicts)

                self.logger.info(f"Detected {len(conflicts)} potential conflicts")
                return conflicts

        except Exception as e:
            self.logger.error(f"Conflict detection failed: {str(e)}")
            return []

    def resolve_conflicts(self, conflicts: List[ConflictInfo]) -> Dict[str, str]:
        """
        Propose resolutions for detected conflicts.

        Args:
            conflicts: List of conflicts to resolve

        Returns:
            Dictionary mapping package names to recommended versions
        """
        resolutions = {}

        for conflict in conflicts:
            try:
                resolution = self._propose_resolution(conflict)
                if resolution:
                    resolutions[conflict.package_name] = resolution
                    self.logger.info(f"Proposed resolution for {conflict.package_name}: {resolution}")

            except Exception as e:
                self.logger.warning(f"Failed to resolve conflict for {conflict.package_name}: {e}")

        return resolutions

    def _group_packages_by_name(self, packages: List[PackageInfo]) -> Dict[str, List[PackageInfo]]:
        """Group packages by name to identify duplicates"""
        grouped = {}

        for package in packages:
            if package.name not in grouped:
                grouped[package.name] = []
            grouped[package.name].append(package)

        return grouped

    def _analyze_version_conflict(self, package_name: str,
                                 package_list: List[PackageInfo]) -> Optional[ConflictInfo]:
        """Analyze version conflicts for a specific package"""
        versions = []
        conflicting_files = []

        for package in package_list:
            if package.version:
                versions.append(package.version)
            if package.source_file:
                conflicting_files.append(package.source_file)

        # If all versions are the same, no conflict
        unique_versions = list(set(versions))
        if len(unique_versions) <= 1:
            return None

        # Determine conflict severity
        severity = self._assess_conflict_severity(package_name, unique_versions)

        return ConflictInfo(
            package_name=package_name,
            required_versions=unique_versions,
            current_version=None,  # Will be determined later
            conflicting_packages=conflicting_files,
            severity=severity
        )

    def _check_known_incompatibilities(self, packages: List[PackageInfo]) -> List[ConflictInfo]:
        """Check for known package incompatibilities"""
        conflicts = []
        package_names = {pkg.name for pkg in packages}

        # Check against known incompatibility rules
        for rule_name, rule_data in self.compatibility_rules.items():
            if 'incompatible_with' in rule_data:
                if rule_name in package_names:
                    for incompatible_pkg in rule_data['incompatible_with']:
                        if incompatible_pkg in package_names:
                            conflict = ConflictInfo(
                                package_name=rule_name,
                                required_versions=[],
                                current_version=None,
                                conflicting_packages=[incompatible_pkg],
                                severity="high"
                            )
                            conflicts.append(conflict)

        return conflicts

    def _assess_conflict_severity(self, package_name: str, versions: List[str]) -> str:
        """Assess the severity of a version conflict"""
        try:
            # Parse version numbers to determine severity
            parsed_versions = []
            for version in versions:
                # Extract numeric version (remove operators like >=, ==, etc.)
                clean_version = re.sub(r'^[><=!~]+', '', version)
                version_parts = clean_version.split('.')

                if len(version_parts) >= 2:
                    try:
                        major = int(version_parts[0])
                        minor = int(version_parts[1])
                        parsed_versions.append((major, minor))
                    except ValueError:
                        continue

            if not parsed_versions:
                return "medium"

            # Check for major version differences
            major_versions = {v[0] for v in parsed_versions}
            if len(major_versions) > 1:
                return "critical"

            # Check for minor version differences
            minor_versions = {v[1] for v in parsed_versions}
            if len(minor_versions) > 2:
                return "high"
            elif len(minor_versions) > 1:
                return "medium"
            else:
                return "low"

        except Exception:
            return "medium"

    def _propose_resolution(self, conflict: ConflictInfo) -> Optional[str]:
        """Propose a resolution for a specific conflict"""
        try:
            if not conflict.required_versions:
                return None

            # For version conflicts, try to find a compatible version
            if len(conflict.required_versions) > 1:
                # Try to find the highest compatible version
                return self._find_compatible_version(conflict.required_versions)

            return conflict.required_versions[0]

        except Exception as e:
            self.logger.warning(f"Failed to propose resolution: {e}")
            return None

    def _find_compatible_version(self, versions: List[str]) -> str:
        """Find a version that satisfies all requirements"""
        # Simple strategy: return the highest version
        # In a real implementation, this would use proper version resolution
        try:
            # Remove version operators and sort
            clean_versions = []
            for version in versions:
                clean_version = re.sub(r'^[><=!~]+', '', version)
                clean_versions.append(clean_version)

            # Sort versions (simple string sort for now)
            clean_versions.sort(reverse=True)
            return clean_versions[0]

        except Exception:
            return versions[0]

    def _load_compatibility_rules(self) -> Dict[str, Dict]:
        """Load known compatibility rules for packages"""
        rules = {
            'django': {
                'incompatible_with': ['flask'],
                'requires': ['python>=3.8']
            },
            'flask': {
                'incompatible_with': ['django'],
                'requires': ['python>=3.7']
            },
            'tensorflow': {
                'incompatible_with': ['pytorch'],
                'requires': ['numpy>=1.19.0']
            },
            'pytorch': {
                'incompatible_with': ['tensorflow'],
                'requires': ['numpy>=1.19.0']
            },
            'react': {
                'requires': ['react-dom']
            },
            'vue': {
                'incompatible_with': ['react', 'angular']
            },
            'angular': {
                'incompatible_with': ['react', 'vue']
            }
        }
        return rules


class DependencyManager:
    """
    Main dependency management system that orchestrates code analysis,
    package installation, and conflict resolution.
    """

    def __init__(self):
        self.logger = get_logger(__name__)
        self.performance_monitor = get_performance_monitor()

        # Initialize components
        self.code_analyzer = CodeAnalysisEngine()
        self.package_installer = PackageInstaller()
        self.conflict_resolver = ConflictResolver()

        # Cache for dependency analysis results
        self._analysis_cache = {}
        self._cache_timeout = 600  # 10 minutes

        self.logger.info("DependencyManager initialized with all components")

    def analyze_and_install_dependencies(self, project_path: Union[str, Path],
                                       auto_install: bool = True,
                                       resolve_conflicts: bool = True) -> Dict[str, Any]:
        """
        Complete dependency management workflow: analyze, detect conflicts, and install.

        Args:
            project_path: Path to project directory
            auto_install: Whether to automatically install detected packages
            resolve_conflicts: Whether to automatically resolve conflicts

        Returns:
            Dictionary with analysis results, conflicts, and installation status
        """
        project_path = Path(project_path)

        try:
            with self.performance_monitor.measure_operation("full_dependency_workflow"):
                # Step 1: Analyze project for dependencies
                self.logger.info(f"Analyzing dependencies in {project_path}")
                analysis_results = self.code_analyzer.analyze_directory(project_path)

                # Flatten all detected packages
                all_packages = []
                for file_path, packages in analysis_results.items():
                    all_packages.extend(packages)

                # Remove duplicates while preserving package info
                unique_packages = self._deduplicate_packages(all_packages)

                self.logger.info(f"Found {len(unique_packages)} unique dependencies")

                # Step 2: Detect conflicts
                conflicts = []
                if resolve_conflicts:
                    conflicts = self.conflict_resolver.detect_conflicts(unique_packages)
                    if conflicts:
                        self.logger.warning(f"Detected {len(conflicts)} conflicts")

                # Step 3: Resolve conflicts
                resolutions = {}
                if conflicts and resolve_conflicts:
                    resolutions = self.conflict_resolver.resolve_conflicts(conflicts)
                    # Apply resolutions to package list
                    unique_packages = self._apply_resolutions(unique_packages, resolutions)

                # Step 4: Install packages
                installation_results = {}
                if auto_install:
                    # Filter out standard library packages
                    installable_packages = [
                        pkg for pkg in unique_packages
                        if not pkg.is_standard_library
                    ]

                    if installable_packages:
                        self.logger.info(f"Installing {len(installable_packages)} packages")
                        installation_results = self.package_installer.install_packages(
                            installable_packages
                        )

                # Step 5: Update requirements files
                self._update_requirements_files(project_path, unique_packages)

                return {
                    'analysis_results': analysis_results,
                    'detected_packages': unique_packages,
                    'conflicts': conflicts,
                    'resolutions': resolutions,
                    'installation_results': installation_results,
                    'success': len([r for r in installation_results.values() if r]) == len(installation_results) if installation_results else True
                }

        except Exception as e:
            self.logger.error(f"Dependency management workflow failed: {str(e)}")
            raise DependencyAnalysisError(f"Workflow failed: {str(e)}")

    def get_dependency_summary(self, project_path: Union[str, Path]) -> Dict[str, Any]:
        """
        Get a summary of project dependencies without installing them.

        Args:
            project_path: Path to project directory

        Returns:
            Summary of detected dependencies
        """
        try:
            analysis_results = self.code_analyzer.analyze_directory(project_path)

            # Categorize packages
            python_packages = []
            javascript_packages = []
            other_packages = []

            for file_path, packages in analysis_results.items():
                for package in packages:
                    if package.package_manager == PackageManager.PIP:
                        python_packages.append(package)
                    elif package.package_manager == PackageManager.NPM:
                        javascript_packages.append(package)
                    else:
                        other_packages.append(package)

            return {
                'total_files_analyzed': len(analysis_results),
                'python_packages': len(python_packages),
                'javascript_packages': len(javascript_packages),
                'other_packages': len(other_packages),
                'package_breakdown': {
                    'python': [pkg.name for pkg in python_packages],
                    'javascript': [pkg.name for pkg in javascript_packages],
                    'other': [pkg.name for pkg in other_packages]
                }
            }

        except Exception as e:
            self.logger.error(f"Failed to get dependency summary: {str(e)}")
            return {}

    def _deduplicate_packages(self, packages: List[PackageInfo]) -> List[PackageInfo]:
        """Remove duplicate packages while preserving the most specific version info"""
        package_dict = {}

        for package in packages:
            key = f"{package.name}:{package.package_manager.value}"

            if key not in package_dict:
                package_dict[key] = package
            else:
                # Keep the package with more specific version info
                existing = package_dict[key]
                if package.version and not existing.version:
                    package_dict[key] = package
                elif package.version and existing.version:
                    # Keep the more restrictive version
                    if '==' in package.version and '==' not in existing.version:
                        package_dict[key] = package

        return list(package_dict.values())

    def _apply_resolutions(self, packages: List[PackageInfo],
                          resolutions: Dict[str, str]) -> List[PackageInfo]:
        """Apply conflict resolutions to package list"""
        updated_packages = []

        for package in packages:
            if package.name in resolutions:
                # Create new package info with resolved version
                resolved_package = PackageInfo(
                    name=package.name,
                    version=resolutions[package.name],
                    package_manager=package.package_manager,
                    dependency_type=package.dependency_type,
                    source_file=package.source_file,
                    line_number=package.line_number,
                    is_standard_library=package.is_standard_library,
                    alternatives=package.alternatives,
                    description=package.description
                )
                updated_packages.append(resolved_package)
            else:
                updated_packages.append(package)

        return updated_packages

    def _update_requirements_files(self, project_path: Path,
                                  packages: List[PackageInfo]) -> None:
        """Update requirements.txt and package.json files with detected dependencies"""
        try:
            # Update requirements.txt for Python packages
            python_packages = [
                pkg for pkg in packages
                if pkg.package_manager == PackageManager.PIP and not pkg.is_standard_library
            ]

            if python_packages:
                self._update_requirements_txt(project_path, python_packages)

            # Update package.json for JavaScript packages
            js_packages = [
                pkg for pkg in packages
                if pkg.package_manager == PackageManager.NPM and not pkg.is_standard_library
            ]

            if js_packages:
                self._update_package_json(project_path, js_packages)

        except Exception as e:
            self.logger.warning(f"Failed to update requirements files: {e}")

    def _update_requirements_txt(self, project_path: Path,
                                packages: List[PackageInfo]) -> None:
        """Update or create requirements.txt file"""
        requirements_file = project_path / "requirements.txt"

        try:
            # Read existing requirements if file exists
            existing_requirements = set()
            if requirements_file.exists():
                with open(requirements_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#'):
                            # Extract package name
                            package_name = re.split(r'[><=!~]', line)[0]
                            existing_requirements.add(package_name)

            # Add new packages
            new_requirements = []
            for package in packages:
                if package.name not in existing_requirements:
                    if package.version:
                        if package.version.startswith(('==', '>=', '<=', '>', '<', '~=', '!=')):
                            requirement = f"{package.name}{package.version}"
                        else:
                            requirement = f"{package.name}=={package.version}"
                    else:
                        requirement = package.name
                    new_requirements.append(requirement)

            # Write updated requirements
            if new_requirements:
                with open(requirements_file, 'a', encoding='utf-8') as f:
                    if requirements_file.stat().st_size > 0:
                        f.write('\n')
                    f.write('\n'.join(new_requirements))
                    f.write('\n')

                self.logger.info(f"Added {len(new_requirements)} packages to requirements.txt")

        except Exception as e:
            self.logger.error(f"Failed to update requirements.txt: {e}")

    def _update_package_json(self, project_path: Path,
                            packages: List[PackageInfo]) -> None:
        """Update or create package.json file"""
        package_json_file = project_path / "package.json"

        try:
            # Read existing package.json or create new one
            if package_json_file.exists():
                with open(package_json_file, 'r', encoding='utf-8') as f:
                    package_data = json.load(f)
            else:
                package_data = {
                    "name": project_path.name,
                    "version": "1.0.0",
                    "description": "",
                    "main": "index.js",
                    "dependencies": {},
                    "devDependencies": {}
                }

            # Add new packages
            added_count = 0
            for package in packages:
                dep_section = "dependencies"
                if package.dependency_type == DependencyType.DEVELOPMENT:
                    dep_section = "devDependencies"

                if dep_section not in package_data:
                    package_data[dep_section] = {}

                if package.name not in package_data[dep_section]:
                    version = package.version if package.version else "^1.0.0"
                    package_data[dep_section][package.name] = version
                    added_count += 1

            # Write updated package.json
            if added_count > 0:
                with open(package_json_file, 'w', encoding='utf-8') as f:
                    json.dump(package_data, f, indent=2)

                self.logger.info(f"Added {added_count} packages to package.json")

        except Exception as e:
            self.logger.error(f"Failed to update package.json: {e}")


class DependencyCache:
    """
    Advanced caching system for dependency information and analysis results
    to improve performance and reduce redundant operations.
    """

    def __init__(self, cache_dir: Optional[Path] = None):
        self.logger = get_logger(__name__)

        # Set up cache directory
        if cache_dir:
            self.cache_dir = Path(cache_dir)
        else:
            self.cache_dir = Path.home() / ".coding_agent" / "dependency_cache"

        self.cache_dir.mkdir(parents=True, exist_ok=True)

        # In-memory cache for frequently accessed data
        self._memory_cache = {}
        self._cache_stats = {
            'hits': 0,
            'misses': 0,
            'file_cache_hits': 0,
            'file_cache_misses': 0
        }

        # Cache configuration
        self.memory_cache_size = 1000
        self.file_cache_ttl = 3600  # 1 hour
        self.memory_cache_ttl = 300  # 5 minutes

        self.logger.info(f"DependencyCache initialized with cache dir: {self.cache_dir}")

    def get_file_analysis(self, file_path: Path) -> Optional[List[PackageInfo]]:
        """Get cached analysis results for a file"""
        cache_key = self._get_file_cache_key(file_path)

        # Check memory cache first
        if cache_key in self._memory_cache:
            cached_time, cached_data = self._memory_cache[cache_key]
            if time.time() - cached_time < self.memory_cache_ttl:
                self._cache_stats['hits'] += 1
                return cached_data
            else:
                # Expired, remove from memory cache
                del self._memory_cache[cache_key]

        # Check file cache
        cache_file = self.cache_dir / f"{cache_key}.json"
        if cache_file.exists():
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)

                # Check if cache is still valid
                if time.time() - cache_data['timestamp'] < self.file_cache_ttl:
                    # Check if source file hasn't been modified
                    if file_path.exists() and file_path.stat().st_mtime <= cache_data['file_mtime']:
                        packages = self._deserialize_packages(cache_data['packages'])

                        # Store in memory cache
                        self._memory_cache[cache_key] = (time.time(), packages)
                        self._cache_stats['file_cache_hits'] += 1
                        return packages

                # Cache is invalid, remove file
                cache_file.unlink()

            except Exception as e:
                self.logger.warning(f"Failed to read cache file {cache_file}: {e}")

        self._cache_stats['misses'] += 1
        self._cache_stats['file_cache_misses'] += 1
        return None

    def store_file_analysis(self, file_path: Path, packages: List[PackageInfo]) -> None:
        """Store analysis results in cache"""
        cache_key = self._get_file_cache_key(file_path)

        try:
            # Store in memory cache
            self._memory_cache[cache_key] = (time.time(), packages)

            # Limit memory cache size
            if len(self._memory_cache) > self.memory_cache_size:
                # Remove oldest entries
                sorted_items = sorted(
                    self._memory_cache.items(),
                    key=lambda x: x[1][0]
                )
                for key, _ in sorted_items[:len(self._memory_cache) - self.memory_cache_size]:
                    del self._memory_cache[key]

            # Store in file cache
            cache_data = {
                'timestamp': time.time(),
                'file_mtime': file_path.stat().st_mtime if file_path.exists() else 0,
                'packages': self._serialize_packages(packages)
            }

            cache_file = self.cache_dir / f"{cache_key}.json"
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, indent=2)

        except Exception as e:
            self.logger.warning(f"Failed to store cache for {file_path}: {e}")

    def get_package_info(self, package_name: str, package_manager: PackageManager) -> Optional[Dict]:
        """Get cached package information"""
        cache_key = f"pkg_info_{package_manager.value}_{package_name}"

        if cache_key in self._memory_cache:
            cached_time, cached_data = self._memory_cache[cache_key]
            if time.time() - cached_time < self.memory_cache_ttl:
                return cached_data

        return None

    def store_package_info(self, package_name: str, package_manager: PackageManager,
                          info: Dict) -> None:
        """Store package information in cache"""
        cache_key = f"pkg_info_{package_manager.value}_{package_name}"
        self._memory_cache[cache_key] = (time.time(), info)

    def clear_cache(self, older_than_hours: Optional[int] = None) -> None:
        """Clear cache entries"""
        try:
            # Clear memory cache
            self._memory_cache.clear()

            # Clear file cache
            if older_than_hours:
                cutoff_time = time.time() - (older_than_hours * 3600)
                for cache_file in self.cache_dir.glob("*.json"):
                    if cache_file.stat().st_mtime < cutoff_time:
                        cache_file.unlink()
            else:
                # Clear all cache files
                for cache_file in self.cache_dir.glob("*.json"):
                    cache_file.unlink()

            self.logger.info("Cache cleared successfully")

        except Exception as e:
            self.logger.error(f"Failed to clear cache: {e}")

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache performance statistics"""
        total_requests = self._cache_stats['hits'] + self._cache_stats['misses']
        hit_rate = (self._cache_stats['hits'] / total_requests * 100) if total_requests > 0 else 0

        return {
            'memory_cache_size': len(self._memory_cache),
            'file_cache_size': len(list(self.cache_dir.glob("*.json"))),
            'hit_rate_percent': round(hit_rate, 2),
            'total_requests': total_requests,
            **self._cache_stats
        }

    def _get_file_cache_key(self, file_path: Path) -> str:
        """Generate a cache key for a file"""
        import hashlib

        # Create a hash of the file path for consistent cache keys
        path_str = str(file_path.resolve())
        return hashlib.md5(path_str.encode()).hexdigest()

    def _serialize_packages(self, packages: List[PackageInfo]) -> List[Dict]:
        """Serialize PackageInfo objects to JSON-compatible format"""
        serialized = []

        for package in packages:
            package_dict = {
                'name': package.name,
                'version': package.version,
                'package_manager': package.package_manager.value,
                'dependency_type': package.dependency_type.value,
                'source_file': package.source_file,
                'line_number': package.line_number,
                'is_standard_library': package.is_standard_library,
                'alternatives': package.alternatives,
                'description': package.description
            }
            serialized.append(package_dict)

        return serialized

    def _deserialize_packages(self, package_dicts: List[Dict]) -> List[PackageInfo]:
        """Deserialize JSON data back to PackageInfo objects"""
        packages = []

        for package_dict in package_dicts:
            try:
                package = PackageInfo(
                    name=package_dict['name'],
                    version=package_dict.get('version'),
                    package_manager=PackageManager(package_dict['package_manager']),
                    dependency_type=DependencyType(package_dict['dependency_type']),
                    source_file=package_dict.get('source_file'),
                    line_number=package_dict.get('line_number'),
                    is_standard_library=package_dict.get('is_standard_library', False),
                    alternatives=package_dict.get('alternatives', []),
                    description=package_dict.get('description')
                )
                packages.append(package)
            except Exception as e:
                self.logger.warning(f"Failed to deserialize package: {e}")
                continue

        return packages


# Enhanced DependencyManager with caching
class EnhancedDependencyManager(DependencyManager):
    """
    Enhanced dependency manager with caching and optimization features.
    """

    def __init__(self, cache_dir: Optional[Path] = None):
        super().__init__()

        # Initialize caching system
        self.cache = DependencyCache(cache_dir)

        # Override code analyzer to use caching
        self.code_analyzer = CachedCodeAnalysisEngine(self.cache)

        self.logger.info("EnhancedDependencyManager initialized with caching support")


class CachedCodeAnalysisEngine(CodeAnalysisEngine):
    """
    Code analysis engine with caching support for improved performance.
    """

    def __init__(self, cache: DependencyCache):
        super().__init__()
        self.cache = cache
        self.logger.info("CachedCodeAnalysisEngine initialized")

    def analyze_file(self, file_path: Union[str, Path]) -> List[PackageInfo]:
        """Analyze file with caching support"""
        file_path = Path(file_path)

        # Try to get from cache first
        cached_result = self.cache.get_file_analysis(file_path)
        if cached_result is not None:
            self.logger.debug(f"Cache hit for {file_path}")
            return cached_result

        # Cache miss, perform analysis
        self.logger.debug(f"Cache miss for {file_path}, analyzing...")
        packages = super().analyze_file(file_path)

        # Store result in cache
        self.cache.store_file_analysis(file_path, packages)

        return packages
