/* AI Coding Agent - Custom Styles */

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Message styles */
.message {
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.user-message .message-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    margin-left: auto;
    max-width: 80%;
}

.assistant-message .message-content {
    background: #f7f7f7;
    color: #333;
    max-width: 80%;
}

.system-message .message-content {
    background: #e3f2fd;
    color: #1976d2;
    text-align: center;
    font-style: italic;
}

.error-message .message-content {
    background: #ffebee;
    color: #c62828;
    border-left: 4px solid #f44336;
}

/* Resize handle */
#resize-handle {
    position: relative;
    user-select: none;
}

#resize-handle:hover::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 40px;
    background: rgba(59, 130, 246, 0.3);
    border-radius: 10px;
}

/* File tree styles */
.file-item {
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.file-item:hover {
    background-color: #f3f4f6;
}

.file-item.selected {
    background-color: #dbeafe;
    color: #1d4ed8;
}

.folder-item {
    font-weight: 500;
}

.folder-item .folder-icon {
    color: #f59e0b;
}

.file-item .file-icon {
    color: #6b7280;
}

/* Tab styles */
.tab-btn {
    border-bottom: 2px solid transparent;
    transition: all 0.2s;
}

.tab-btn.active {
    color: #2563eb;
    border-bottom-color: #2563eb;
}

.tab-btn:hover:not(.active) {
    color: #374151;
    border-bottom-color: #d1d5db;
}

/* Code editor styles */
.code-editor {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    line-height: 1.5;
}

/* Terminal styles */
.terminal {
    background: #1a1a1a;
    color: #00ff00;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    padding: 16px;
    height: 100%;
    overflow-y: auto;
}

.terminal-prompt {
    color: #00ff00;
}

.terminal-output {
    color: #ffffff;
}

.terminal-error {
    color: #ff6b6b;
}

/* Preview iframe */
.preview-iframe {
    width: 100%;
    height: 100%;
    border: none;
    background: white;
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
    display: inline-block;
    margin-right: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Status indicators */
.status-indicator {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.status-idle {
    background: #f3f4f6;
    color: #6b7280;
}

.status-generating {
    background: #fef3c7;
    color: #d97706;
}

.status-ready {
    background: #d1fae5;
    color: #065f46;
}

.status-error {
    background: #fee2e2;
    color: #dc2626;
}

.status-running {
    background: #dbeafe;
    color: #1d4ed8;
}

/* Responsive design */
@media (max-width: 768px) {
    #communication-panel {
        width: 100%;
    }
    
    #project-panel {
        display: none;
    }
    
    .mobile-show-project #communication-panel {
        display: none;
    }
    
    .mobile-show-project #project-panel {
        display: flex;
        width: 100%;
    }
}

/* Syntax highlighting for code snippets */
.hljs {
    background: #f8f9fa !important;
    border-radius: 6px;
    padding: 12px;
    margin: 8px 0;
    font-size: 14px;
    line-height: 1.5;
}

/* Custom button styles */
.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s;
    cursor: pointer;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    color: #495057;
    padding: 8px 16px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s;
    cursor: pointer;
}

.btn-secondary:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

/* Project preview styles */
.project-preview {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
    background: white;
}

.project-preview-header {
    background: #f9fafb;
    padding: 12px 16px;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.project-preview-content {
    height: 400px;
    overflow: hidden;
}

/* Enhanced Communication Features */
.typing-indicator {
    animation: fadeInUp 0.3s ease-out;
}

.attachment-area {
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 8px;
}

.attachment-item {
    transition: all 0.2s;
}

.attachment-item:hover {
    background: #dbeafe !important;
}

.drag-over {
    background: #e0f2fe !important;
    border: 2px dashed #0284c7 !important;
}

/* Code highlighting */
pre code {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    line-height: 1.4;
}

.language-javascript,
.language-js {
    color: #f7df1e;
}

.language-python {
    color: #3776ab;
}

.language-html {
    color: #e34c26;
}

.language-css {
    color: #1572b6;
}

/* Message enhancements */
.message-content {
    word-wrap: break-word;
    max-width: 100%;
}

.message-content ul {
    margin: 8px 0;
}

.message-content li {
    margin: 4px 0;
}

.message-content pre {
    margin: 8px 0;
    max-width: 100%;
    overflow-x: auto;
}

.message-content code {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* Search results modal */
.search-modal {
    backdrop-filter: blur(4px);
}

.search-result-item {
    transition: background-color 0.2s;
}

.search-result-item:hover {
    background-color: #f3f4f6;
}

/* Typing animation */
@keyframes bounce {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

.animate-bounce {
    animation: bounce 1.4s infinite;
}

/* Enhanced button styles */
#code-btn.active {
    background: #10b981 !important;
    color: white !important;
}

/* File upload styles */
.file-upload-area {
    border: 2px dashed #d1d5db;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    transition: all 0.2s;
}

.file-upload-area.drag-over {
    border-color: #3b82f6;
    background-color: #eff6ff;
}

.file-upload-area:hover {
    border-color: #9ca3af;
}

/* Message timestamps */
.message-timestamp {
    font-size: 11px;
    color: #9ca3af;
    margin-top: 4px;
}

/* Enhanced scrollbar for messages */
#messages-container::-webkit-scrollbar {
    width: 4px;
}

#messages-container::-webkit-scrollbar-track {
    background: transparent;
}

#messages-container::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 2px;
}

#messages-container::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}
