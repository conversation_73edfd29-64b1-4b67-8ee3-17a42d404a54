# AI Coding Agent - Error Pattern Learning
"""
Machine learning system for learning from error patterns and providing proactive suggestions
"""

import json
import pickle
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from collections import defaultdict, Counter
import hashlib

from src.logger import get_logger
from src.error_monitor import ErrorEvent
from src.root_cause_analyzer import RootCause, ErrorPattern
from src.database import DatabaseManager

@dataclass
class LearningPattern:
    """Represents a learned error pattern"""
    pattern_id: str
    pattern_hash: str
    error_signature: str
    frequency: int
    success_rate: float  # Rate of successful fixes
    context_features: Dict[str, Any]
    prevention_strategies: List[str]
    effectiveness_scores: Dict[str, float]
    last_updated: datetime

@dataclass
class PreventionSuggestion:
    """Proactive prevention suggestion"""
    suggestion_id: str
    priority: str  # 'low', 'medium', 'high', 'critical'
    category: str  # 'code_quality', 'dependency', 'environment', 'practice'
    title: str
    description: str
    implementation_steps: List[str]
    estimated_effort: str
    potential_impact: str
    confidence: float

class ErrorPatternLearner:
    """
    Machine learning system that learns from error patterns and provides proactive suggestions
    """

    def __init__(self, learning_data_path: str = "database/learning_data.pkl"):
        """Initialize error pattern learner"""
        self.logger = get_logger(__name__)
        self.db = DatabaseManager()
        self.learning_data_path = Path(learning_data_path)
        self.learning_data_path.parent.mkdir(exist_ok=True)

        # Learning data storage
        self.learned_patterns: Dict[str, LearningPattern] = {}
        self.prevention_rules: Dict[str, List[str]] = {}
        self.success_history: Dict[str, List[bool]] = {}

        # Feature extractors for pattern recognition
        self.feature_extractors = {
            'error_type': self._extract_error_type_features,
            'file_context': self._extract_file_context_features,
            'time_context': self._extract_time_context_features,
            'code_context': self._extract_code_context_features
        }

        # Load existing learning data
        self._load_learning_data()

        self.logger.info("ErrorPatternLearner initialized")

    def learn_from_error(self, error: ErrorEvent, root_cause: RootCause,
                        fix_applied: bool = False, fix_successful: bool = False) -> None:
        """
        Learn from an error occurrence and its resolution

        Args:
            error: Error event that occurred
            root_cause: Root cause analysis result
            fix_applied: Whether a fix was applied
            fix_successful: Whether the fix was successful
        """
        self.logger.info(f"Learning from error: {error.error_type}")

        # Extract features from the error
        features = self._extract_features(error, root_cause)

        # Create or update learning pattern
        pattern = self._create_or_update_pattern(error, root_cause, features)

        # Record fix outcome if applicable
        if fix_applied:
            self._record_fix_outcome(pattern.pattern_id, fix_successful)

        # Update prevention strategies
        self._update_prevention_strategies(pattern, root_cause)

        # Save learning data
        self._save_learning_data()

        self.logger.debug(f"Updated learning pattern: {pattern.pattern_id}")

    def get_prevention_suggestions(self, context: Optional[Dict[str, Any]] = None) -> List[PreventionSuggestion]:
        """
        Get proactive prevention suggestions based on learned patterns

        Args:
            context: Optional context information (file types, project structure, etc.)

        Returns:
            List of prevention suggestions
        """
        suggestions = []

        # Analyze learned patterns for prevention opportunities
        high_frequency_patterns = [
            pattern for pattern in self.learned_patterns.values()
            if pattern.frequency >= 3 and pattern.success_rate < 0.8
        ]

        for pattern in high_frequency_patterns:
            suggestion = self._generate_prevention_suggestion(pattern, context)
            if suggestion:
                suggestions.append(suggestion)

        # Add general best practice suggestions
        general_suggestions = self._get_general_prevention_suggestions(context)
        suggestions.extend(general_suggestions)

        # Sort by priority and confidence
        suggestions.sort(key=lambda s: (
            {'critical': 4, 'high': 3, 'medium': 2, 'low': 1}[s.priority],
            s.confidence
        ), reverse=True)

        return suggestions[:10]  # Return top 10 suggestions

    def analyze_error_trends(self, days: int = 30) -> Dict[str, Any]:
        """
        Analyze error trends over time

        Args:
            days: Number of days to analyze

        Returns:
            Trend analysis results
        """
        cutoff_date = datetime.now() - timedelta(days=days)

        # Filter recent patterns
        recent_patterns = [
            pattern for pattern in self.learned_patterns.values()
            if pattern.last_updated >= cutoff_date
        ]

        # Analyze trends
        trends = {
            'total_patterns': len(recent_patterns),
            'most_frequent_errors': self._get_most_frequent_errors(recent_patterns),
            'improvement_areas': self._identify_improvement_areas(recent_patterns),
            'success_rates': self._calculate_success_rates(recent_patterns),
            'prevention_effectiveness': self._analyze_prevention_effectiveness()
        }

        return trends

    def get_similar_patterns(self, error: ErrorEvent, limit: int = 5) -> List[LearningPattern]:
        """
        Find similar error patterns to help with resolution

        Args:
            error: Error to find similar patterns for
            limit: Maximum number of patterns to return

        Returns:
            List of similar patterns
        """
        # Extract features from the current error
        features = self._extract_features(error, None)

        # Calculate similarity scores
        similarities = []
        for pattern in self.learned_patterns.values():
            similarity = self._calculate_pattern_similarity(features, pattern.context_features)
            similarities.append((similarity, pattern))

        # Sort by similarity and return top matches
        similarities.sort(key=lambda x: x[0], reverse=True)
        return [pattern for _, pattern in similarities[:limit]]

    def _extract_features(self, error: ErrorEvent, root_cause: Optional[RootCause]) -> Dict[str, Any]:
        """Extract features from error for pattern recognition"""
        features = {}

        # Apply all feature extractors
        for name, extractor in self.feature_extractors.items():
            try:
                features[name] = extractor(error, root_cause)
            except Exception as e:
                self.logger.warning(f"Error in feature extractor {name}: {e}")
                features[name] = {}

        return features

    def _extract_error_type_features(self, error: ErrorEvent, root_cause: Optional[RootCause]) -> Dict[str, Any]:
        """Extract error type related features"""
        features = {
            'error_type': error.error_type,
            'severity': error.severity,
            'source': error.source
        }

        if root_cause:
            features.update({
                'category': root_cause.category,
                'confidence': root_cause.confidence,
                'primary_cause': root_cause.primary_cause
            })

        return features

    def _extract_file_context_features(self, error: ErrorEvent, root_cause: Optional[RootCause]) -> Dict[str, Any]:
        """Extract file context features"""
        features = {}

        if error.file_path:
            path = Path(error.file_path)
            features.update({
                'file_extension': path.suffix,
                'file_name': path.name,
                'directory_depth': len(path.parts),
                'is_test_file': 'test' in path.name.lower()
            })

        if error.line_number:
            features['line_number'] = error.line_number

        return features

    def _extract_time_context_features(self, error: ErrorEvent, root_cause: Optional[RootCause]) -> Dict[str, Any]:
        """Extract time-related features"""
        features = {
            'hour_of_day': error.timestamp.hour,
            'day_of_week': error.timestamp.weekday(),
            'is_weekend': error.timestamp.weekday() >= 5
        }

        return features

    def _extract_code_context_features(self, error: ErrorEvent, root_cause: Optional[RootCause]) -> Dict[str, Any]:
        """Extract code context features"""
        features = {}

        if error.details:
            features.update({
                'has_details': True,
                'detail_keys': list(error.details.keys())
            })
        else:
            features['has_details'] = False

        if error.stack_trace:
            features.update({
                'has_stack_trace': True,
                'stack_depth': len(error.stack_trace.split('\n'))
            })
        else:
            features['has_stack_trace'] = False

        return features

    def _create_or_update_pattern(self, error: ErrorEvent, root_cause: RootCause,
                                features: Dict[str, Any]) -> LearningPattern:
        """Create new or update existing learning pattern"""
        # Create pattern signature
        signature = self._create_error_signature(error, root_cause)
        pattern_hash = hashlib.md5(signature.encode()).hexdigest()

        if pattern_hash in self.learned_patterns:
            # Update existing pattern
            pattern = self.learned_patterns[pattern_hash]
            pattern.frequency += 1
            pattern.last_updated = datetime.now()

            # Update context features (merge with existing)
            for key, value in features.items():
                if key in pattern.context_features:
                    if isinstance(value, dict) and isinstance(pattern.context_features[key], dict):
                        pattern.context_features[key].update(value)
                    else:
                        pattern.context_features[key] = value
                else:
                    pattern.context_features[key] = value
        else:
            # Create new pattern
            import uuid
            pattern = LearningPattern(
                pattern_id=str(uuid.uuid4()),
                pattern_hash=pattern_hash,
                error_signature=signature,
                frequency=1,
                success_rate=0.0,
                context_features=features,
                prevention_strategies=[],
                effectiveness_scores={},
                last_updated=datetime.now()
            )
            self.learned_patterns[pattern_hash] = pattern

        return pattern

    def _create_error_signature(self, error: ErrorEvent, root_cause: RootCause) -> str:
        """Create unique signature for error pattern"""
        components = [
            error.error_type,
            error.source,
            root_cause.category if root_cause else 'unknown'
        ]

        # Add file extension if available
        if error.file_path:
            path = Path(error.file_path)
            components.append(path.suffix)

        return '|'.join(components)

    def _record_fix_outcome(self, pattern_id: str, successful: bool) -> None:
        """Record the outcome of a fix attempt"""
        if pattern_id not in self.success_history:
            self.success_history[pattern_id] = []

        self.success_history[pattern_id].append(successful)

        # Update success rate in pattern
        pattern = next((p for p in self.learned_patterns.values() if p.pattern_id == pattern_id), None)
        if pattern:
            successes = sum(self.success_history[pattern_id])
            total = len(self.success_history[pattern_id])
            pattern.success_rate = successes / total if total > 0 else 0.0

    def _update_prevention_strategies(self, pattern: LearningPattern, root_cause: RootCause) -> None:
        """Update prevention strategies for a pattern"""
        # Add new prevention strategies from root cause
        for solution in root_cause.suggested_solutions:
            if solution not in pattern.prevention_strategies:
                pattern.prevention_strategies.append(solution)

        # Limit to top 5 strategies
        pattern.prevention_strategies = pattern.prevention_strategies[:5]

    def _generate_prevention_suggestion(self, pattern: LearningPattern,
                                      context: Optional[Dict[str, Any]]) -> Optional[PreventionSuggestion]:
        """Generate prevention suggestion from learned pattern"""
        try:
            import uuid

            # Determine priority based on frequency and success rate
            if pattern.frequency >= 10 and pattern.success_rate < 0.5:
                priority = 'critical'
            elif pattern.frequency >= 5 and pattern.success_rate < 0.7:
                priority = 'high'
            elif pattern.frequency >= 3:
                priority = 'medium'
            else:
                priority = 'low'

            # Generate title and description
            error_type = pattern.context_features.get('error_type', {}).get('error_type', 'Unknown')
            title = f"Prevent recurring {error_type} errors"
            description = f"This error has occurred {pattern.frequency} times with a {pattern.success_rate:.1%} fix success rate."

            # Use prevention strategies as implementation steps
            steps = pattern.prevention_strategies if pattern.prevention_strategies else [
                "Review code for similar patterns",
                "Add error handling",
                "Implement validation checks"
            ]

            return PreventionSuggestion(
                suggestion_id=str(uuid.uuid4()),
                priority=priority,
                category=self._categorize_prevention(pattern),
                title=title,
                description=description,
                implementation_steps=steps,
                estimated_effort=self._estimate_effort(pattern),
                potential_impact=f"Could prevent {pattern.frequency} similar errors",
                confidence=min(0.9, pattern.frequency / 10.0)
            )

        except Exception as e:
            self.logger.error(f"Error generating prevention suggestion: {e}")
            return None

    def _categorize_prevention(self, pattern: LearningPattern) -> str:
        """Categorize prevention suggestion"""
        error_type = pattern.context_features.get('error_type', {}).get('error_type', '').lower()

        if 'import' in error_type or 'module' in error_type:
            return 'dependency'
        elif 'syntax' in error_type:
            return 'code_quality'
        elif 'name' in error_type or 'reference' in error_type:
            return 'practice'
        else:
            return 'environment'

    def _estimate_effort(self, pattern: LearningPattern) -> str:
        """Estimate effort required for prevention"""
        if pattern.frequency >= 10:
            return 'high'
        elif pattern.frequency >= 5:
            return 'medium'
        else:
            return 'low'

    def _get_general_prevention_suggestions(self, context: Optional[Dict[str, Any]]) -> List[PreventionSuggestion]:
        """Get general best practice prevention suggestions"""
        import uuid

        suggestions = []

        # Code quality suggestions
        suggestions.append(PreventionSuggestion(
            suggestion_id=str(uuid.uuid4()),
            priority='medium',
            category='code_quality',
            title='Use a code linter',
            description='Set up automated code quality checking to catch errors early',
            implementation_steps=[
                'Install pylint or flake8 for Python',
                'Configure linting rules',
                'Set up pre-commit hooks',
                'Integrate with your IDE'
            ],
            estimated_effort='medium',
            potential_impact='Prevents 60-80% of syntax and style errors',
            confidence=0.8
        ))

        # Testing suggestions
        suggestions.append(PreventionSuggestion(
            suggestion_id=str(uuid.uuid4()),
            priority='high',
            category='practice',
            title='Implement comprehensive testing',
            description='Add unit tests to catch errors before they reach production',
            implementation_steps=[
                'Write unit tests for critical functions',
                'Set up automated test running',
                'Aim for 80%+ code coverage',
                'Add integration tests'
            ],
            estimated_effort='high',
            potential_impact='Prevents 70-90% of logic errors',
            confidence=0.9
        ))

        return suggestions

    def _get_most_frequent_errors(self, patterns: List[LearningPattern]) -> List[Dict[str, Any]]:
        """Get most frequent error types"""
        error_counts = Counter()
        for pattern in patterns:
            error_type = pattern.context_features.get('error_type', {}).get('error_type', 'Unknown')
            error_counts[error_type] += pattern.frequency

        return [{'error_type': error, 'count': count} for error, count in error_counts.most_common(5)]

    def _identify_improvement_areas(self, patterns: List[LearningPattern]) -> List[str]:
        """Identify areas that need improvement"""
        areas = []

        # Check for patterns with low success rates
        low_success_patterns = [p for p in patterns if p.success_rate < 0.5 and p.frequency >= 3]
        if low_success_patterns:
            areas.append("Error resolution strategies need improvement")

        # Check for high frequency patterns
        high_freq_patterns = [p for p in patterns if p.frequency >= 10]
        if high_freq_patterns:
            areas.append("Prevention strategies need strengthening")

        # Check for syntax errors
        syntax_patterns = [p for p in patterns if 'syntax' in p.error_signature.lower()]
        if len(syntax_patterns) >= 3:
            areas.append("Code quality tools should be implemented")

        return areas

    def _calculate_success_rates(self, patterns: List[LearningPattern]) -> Dict[str, float]:
        """Calculate success rates by category"""
        category_rates = defaultdict(list)

        for pattern in patterns:
            category = pattern.context_features.get('error_type', {}).get('category', 'unknown')
            category_rates[category].append(pattern.success_rate)

        return {
            category: sum(rates) / len(rates) if rates else 0.0
            for category, rates in category_rates.items()
        }

    def _analyze_prevention_effectiveness(self) -> Dict[str, Any]:
        """Analyze effectiveness of prevention strategies"""
        # This would analyze which prevention strategies are most effective
        # For now, return placeholder data
        return {
            'most_effective_strategies': [
                'Code linting',
                'Unit testing',
                'Type checking'
            ],
            'average_effectiveness': 0.75
        }

    def _calculate_pattern_similarity(self, features1: Dict[str, Any], features2: Dict[str, Any]) -> float:
        """Calculate similarity between two feature sets"""
        similarity = 0.0
        total_features = 0

        # Compare each feature category
        for category in features1:
            if category in features2:
                category_similarity = self._compare_feature_category(features1[category], features2[category])
                similarity += category_similarity
            total_features += 1

        return similarity / total_features if total_features > 0 else 0.0

    def _compare_feature_category(self, features1: Dict[str, Any], features2: Dict[str, Any]) -> float:
        """Compare features within a category"""
        if not isinstance(features1, dict) or not isinstance(features2, dict):
            return 1.0 if features1 == features2 else 0.0

        matches = 0
        total = 0

        for key in set(features1.keys()) | set(features2.keys()):
            total += 1
            if key in features1 and key in features2:
                if features1[key] == features2[key]:
                    matches += 1

        return matches / total if total > 0 else 0.0

    def _load_learning_data(self) -> None:
        """Load learning data from disk"""
        try:
            if self.learning_data_path.exists():
                with open(self.learning_data_path, 'rb') as f:
                    data = pickle.load(f)
                    self.learned_patterns = data.get('patterns', {})
                    self.success_history = data.get('success_history', {})
                    self.prevention_rules = data.get('prevention_rules', {})
                self.logger.info(f"Loaded {len(self.learned_patterns)} learning patterns")
        except Exception as e:
            self.logger.warning(f"Could not load learning data: {e}")

    def _save_learning_data(self) -> None:
        """Save learning data to disk"""
        try:
            data = {
                'patterns': self.learned_patterns,
                'success_history': self.success_history,
                'prevention_rules': self.prevention_rules
            }
            with open(self.learning_data_path, 'wb') as f:
                pickle.dump(data, f)
        except Exception as e:
            self.logger.error(f"Could not save learning data: {e}")