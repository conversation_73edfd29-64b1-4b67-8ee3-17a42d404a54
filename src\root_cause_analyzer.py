# AI Coding Agent - Root Cause Analysis
"""
Intelligent error analysis system for identifying root causes and patterns
"""

import re
import ast
import json
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
from collections import defaultdict, Counter

from .logger import get_logger
from .error_monitor import ErrorEvent
from .database import DatabaseManager

@dataclass
class RootCause:
    """Represents a root cause analysis result"""
    error_id: str
    primary_cause: str
    contributing_factors: List[str]
    confidence: float  # 0.0 to 1.0
    category: str  # 'syntax', 'logic', 'dependency', 'environment', 'design'
    description: str
    evidence: Dict[str, Any]
    suggested_solutions: List[str]
    related_errors: List[str]

@dataclass
class ErrorPattern:
    """Represents a recurring error pattern"""
    pattern_id: str
    error_types: List[str]
    frequency: int
    first_seen: datetime
    last_seen: datetime
    affected_files: List[str]
    common_context: Dict[str, Any]
    severity_trend: str  # 'increasing', 'stable', 'decreasing'

class RootCauseAnalyzer:
    """
    Intelligent error analysis system that identifies root causes and patterns
    """

    def __init__(self):
        """Initialize root cause analyzer"""
        self.logger = get_logger(__name__)
        self.db = DatabaseManager()

        # Knowledge base of common error patterns and their causes
        self.error_knowledge_base = {
            'python': {
                'SyntaxError': {
                    'common_causes': [
                        'Missing parentheses, brackets, or quotes',
                        'Incorrect indentation',
                        'Invalid character or encoding',
                        'Mixing tabs and spaces'
                    ],
                    'patterns': [
                        r'invalid syntax',
                        r'unexpected EOF',
                        r'unmatched',
                        r'invalid character'
                    ]
                },
                'NameError': {
                    'common_causes': [
                        'Variable used before definition',
                        'Typo in variable name',
                        'Variable out of scope',
                        'Missing import statement'
                    ],
                    'patterns': [
                        r"name '(.+)' is not defined",
                        r'undefined variable'
                    ]
                },
                'ImportError': {
                    'common_causes': [
                        'Missing dependency package',
                        'Incorrect module path',
                        'Virtual environment not activated',
                        'Package not installed'
                    ],
                    'patterns': [
                        r'No module named',
                        r'cannot import name'
                    ]
                },
                'TypeError': {
                    'common_causes': [
                        'Incorrect function arguments',
                        'Wrong data type operation',
                        'Missing required parameters',
                        'Type mismatch'
                    ],
                    'patterns': [
                        r'takes \d+ positional arguments',
                        r'unsupported operand type',
                        r'not callable'
                    ]
                }
            },
            'javascript': {
                'ReferenceError': {
                    'common_causes': [
                        'Variable not declared',
                        'Function not defined',
                        'Scope issues',
                        'Typo in variable name'
                    ],
                    'patterns': [
                        r'(.+) is not defined',
                        r'undefined variable'
                    ]
                },
                'SyntaxError': {
                    'common_causes': [
                        'Missing semicolons or commas',
                        'Unmatched brackets or parentheses',
                        'Invalid character',
                        'Incorrect function syntax'
                    ],
                    'patterns': [
                        r'Unexpected token',
                        r'Unexpected end of input',
                        r'Invalid or unexpected token'
                    ]
                }
            }
        }

        # Pattern tracking
        self.detected_patterns: Dict[str, ErrorPattern] = {}
        self.analysis_cache: Dict[str, RootCause] = {}

        self.logger.info("RootCauseAnalyzer initialized")

    def analyze_error(self, error: ErrorEvent, context: Optional[Dict[str, Any]] = None) -> RootCause:
        """
        Perform comprehensive root cause analysis on an error

        Args:
            error: Error event to analyze
            context: Additional context information

        Returns:
            Root cause analysis result
        """
        # Check cache first
        if error.id in self.analysis_cache:
            return self.analysis_cache[error.id]

        self.logger.info(f"Analyzing error: {error.error_type} - {error.message}")

        # Determine language context
        language = self._determine_language(error)

        # Analyze the error
        root_cause = self._perform_analysis(error, language, context)

        # Find related errors
        root_cause.related_errors = self._find_related_errors(error)

        # Cache the result
        self.analysis_cache[error.id] = root_cause

        # Update pattern tracking
        self._update_pattern_tracking(error, root_cause)

        self.logger.info(f"Analysis complete: {root_cause.primary_cause} (confidence: {root_cause.confidence:.2f})")

        return root_cause

    def get_error_patterns(self, days: int = 7) -> List[ErrorPattern]:
        """
        Get detected error patterns from the last N days

        Args:
            days: Number of days to look back

        Returns:
            List of detected error patterns
        """
        cutoff_date = datetime.now() - timedelta(days=days)

        recent_patterns = []
        for pattern in self.detected_patterns.values():
            if pattern.last_seen >= cutoff_date:
                recent_patterns.append(pattern)

        # Sort by frequency and recency
        recent_patterns.sort(key=lambda p: (p.frequency, p.last_seen), reverse=True)

        return recent_patterns

    def suggest_preventive_measures(self, patterns: List[ErrorPattern]) -> List[Dict[str, Any]]:
        """
        Suggest preventive measures based on error patterns

        Args:
            patterns: List of error patterns to analyze

        Returns:
            List of preventive measure suggestions
        """
        suggestions = []

        for pattern in patterns:
            if pattern.frequency >= 3:  # Recurring pattern
                suggestion = {
                    'pattern_id': pattern.pattern_id,
                    'priority': 'high' if pattern.frequency >= 5 else 'medium',
                    'measures': self._generate_preventive_measures(pattern),
                    'estimated_impact': self._estimate_prevention_impact(pattern)
                }
                suggestions.append(suggestion)

        return suggestions

    def _determine_language(self, error: ErrorEvent) -> str:
        """Determine programming language from error context"""
        if error.file_path:
            path = Path(error.file_path)
            extension = path.suffix.lower()

            language_map = {
                '.py': 'python',
                '.js': 'javascript',
                '.html': 'html',
                '.css': 'css'
            }

            return language_map.get(extension, 'unknown')

        # Try to infer from error type
        if any(keyword in error.error_type.lower() for keyword in ['python', 'syntax', 'name', 'import', 'type']):
            return 'python'
        elif any(keyword in error.error_type.lower() for keyword in ['javascript', 'reference']):
            return 'javascript'

        return 'unknown'

    def _perform_analysis(self, error: ErrorEvent, language: str, context: Optional[Dict[str, Any]]) -> RootCause:
        """Perform the actual root cause analysis"""
        # Get knowledge base for the language
        kb = self.error_knowledge_base.get(language, {})
        error_info = kb.get(error.error_type, {})

        # Initialize analysis result
        primary_cause = "Unknown cause"
        contributing_factors = []
        confidence = 0.5
        category = "unknown"
        description = f"Analysis of {error.error_type} error"
        evidence = {}
        suggested_solutions = []

        if error_info:
            # Use knowledge base
            primary_cause = error_info['common_causes'][0] if error_info['common_causes'] else "Unknown cause"
            contributing_factors = error_info['common_causes'][1:3]  # Take 2-3 factors
            confidence = 0.8

            # Determine category
            category = self._categorize_error(error, language)

            # Pattern matching for more specific analysis
            for pattern in error_info.get('patterns', []):
                if re.search(pattern, error.message, re.IGNORECASE):
                    confidence = min(0.95, confidence + 0.1)
                    evidence[f'pattern_match_{pattern}'] = True

                    # Extract specific information from pattern
                    match = re.search(pattern, error.message, re.IGNORECASE)
                    if match and match.groups():
                        evidence['extracted_info'] = match.groups()

            # Generate solutions based on error type
            suggested_solutions = self._generate_solutions(error, language, error_info)

        # Additional analysis based on error details
        if error.details:
            evidence.update(error.details)

            # Specific analysis for different error types
            if error.error_type == 'ImportError' and 'No module named' in error.message:
                module_name = self._extract_module_name(error.message)
                if module_name:
                    primary_cause = f"Missing dependency: {module_name}"
                    suggested_solutions.insert(0, f"Install missing package: pip install {module_name}")
                    confidence = 0.9

            elif error.error_type == 'NameError' and 'is not defined' in error.message:
                var_name = self._extract_variable_name(error.message)
                if var_name:
                    primary_cause = f"Undefined variable: {var_name}"
                    suggested_solutions.insert(0, f"Define variable '{var_name}' before use")
                    confidence = 0.85

        # Context-based analysis
        if context:
            self._analyze_context(context, evidence, contributing_factors)

        return RootCause(
            error_id=error.id,
            primary_cause=primary_cause,
            contributing_factors=contributing_factors,
            confidence=confidence,
            category=category,
            description=description,
            evidence=evidence,
            suggested_solutions=suggested_solutions,
            related_errors=[]  # Will be filled later
        )

    def _categorize_error(self, error: ErrorEvent, language: str) -> str:
        """Categorize the error type"""
        error_type = error.error_type.lower()

        if any(keyword in error_type for keyword in ['syntax', 'indentation', 'tab']):
            return 'syntax'
        elif any(keyword in error_type for keyword in ['import', 'module', 'package']):
            return 'dependency'
        elif any(keyword in error_type for keyword in ['name', 'reference', 'undefined']):
            return 'logic'
        elif any(keyword in error_type for keyword in ['type', 'attribute', 'value']):
            return 'logic'
        elif any(keyword in error_type for keyword in ['permission', 'access', 'file']):
            return 'environment'
        else:
            return 'unknown'

    def _generate_solutions(self, error: ErrorEvent, language: str, error_info: Dict[str, Any]) -> List[str]:
        """Generate specific solutions based on error type and context"""
        solutions = []

        error_type = error.error_type

        if error_type == 'SyntaxError':
            solutions.extend([
                "Check for missing parentheses, brackets, or quotes",
                "Verify proper indentation (use consistent spaces or tabs)",
                "Look for typos or invalid characters",
                "Ensure all code blocks are properly closed"
            ])

        elif error_type == 'ImportError' or error_type == 'ModuleNotFoundError':
            solutions.extend([
                "Install the missing package using pip or your package manager",
                "Check if the module name is spelled correctly",
                "Verify the module is in your Python path",
                "Activate your virtual environment if using one"
            ])

        elif error_type == 'NameError':
            solutions.extend([
                "Define the variable before using it",
                "Check for typos in variable names",
                "Ensure the variable is in the correct scope",
                "Import the required module or function"
            ])

        elif error_type == 'TypeError':
            solutions.extend([
                "Check function arguments and their types",
                "Verify you're calling the function correctly",
                "Ensure variables are the expected data type",
                "Check for missing or extra parameters"
            ])

        # Add language-specific solutions
        if language == 'javascript':
            if 'ReferenceError' in error_type:
                solutions.extend([
                    "Declare variables with var, let, or const",
                    "Check variable scope and hoisting rules",
                    "Ensure functions are defined before calling them"
                ])

        return solutions[:5]  # Limit to top 5 solutions

    def _extract_module_name(self, error_message: str) -> Optional[str]:
        """Extract module name from import error message"""
        patterns = [
            r"No module named '([^']+)'",
            r"No module named ([^\s]+)",
            r"cannot import name '([^']+)'"
        ]

        for pattern in patterns:
            match = re.search(pattern, error_message)
            if match:
                return match.group(1)

        return None

    def _extract_variable_name(self, error_message: str) -> Optional[str]:
        """Extract variable name from name error message"""
        patterns = [
            r"name '([^']+)' is not defined",
            r"'([^']+)' is not defined",
            r"([^\s]+) is not defined"
        ]

        for pattern in patterns:
            match = re.search(pattern, error_message)
            if match:
                return match.group(1)

        return None

    def _analyze_context(self, context: Dict[str, Any], evidence: Dict[str, Any], contributing_factors: List[str]) -> None:
        """Analyze additional context information"""
        if 'recent_changes' in context:
            evidence['recent_changes'] = context['recent_changes']
            contributing_factors.append("Recent code changes may have introduced the issue")

        if 'dependencies' in context:
            evidence['dependencies'] = context['dependencies']
            if context['dependencies'].get('outdated'):
                contributing_factors.append("Outdated dependencies may be causing compatibility issues")

        if 'environment' in context:
            evidence['environment'] = context['environment']
            if context['environment'].get('virtual_env_active') is False:
                contributing_factors.append("Virtual environment may not be activated")

    def _find_related_errors(self, error: ErrorEvent) -> List[str]:
        """Find errors related to the current error"""
        related = []

        # Look for errors in the same file
        if error.file_path:
            # This would typically query the database for recent errors in the same file
            # For now, we'll return an empty list as a placeholder
            pass

        # Look for errors with similar messages
        # This would typically use similarity algorithms

        return related

    def _update_pattern_tracking(self, error: ErrorEvent, root_cause: RootCause) -> None:
        """Update error pattern tracking"""
        pattern_key = f"{error.error_type}_{root_cause.category}"

        if pattern_key in self.detected_patterns:
            # Update existing pattern
            pattern = self.detected_patterns[pattern_key]
            pattern.frequency += 1
            pattern.last_seen = error.timestamp

            if error.file_path and error.file_path not in pattern.affected_files:
                pattern.affected_files.append(error.file_path)
        else:
            # Create new pattern
            pattern = ErrorPattern(
                pattern_id=pattern_key,
                error_types=[error.error_type],
                frequency=1,
                first_seen=error.timestamp,
                last_seen=error.timestamp,
                affected_files=[error.file_path] if error.file_path else [],
                common_context={'category': root_cause.category},
                severity_trend='stable'
            )
            self.detected_patterns[pattern_key] = pattern

    def _generate_preventive_measures(self, pattern: ErrorPattern) -> List[str]:
        """Generate preventive measures for a pattern"""
        measures = []

        if 'syntax' in pattern.pattern_id.lower():
            measures.extend([
                "Use a code linter (pylint, flake8) to catch syntax errors early",
                "Set up pre-commit hooks to validate code before commits",
                "Use an IDE with real-time syntax checking"
            ])

        elif 'import' in pattern.pattern_id.lower() or 'dependency' in pattern.common_context.get('category', ''):
            measures.extend([
                "Maintain a requirements.txt file with pinned versions",
                "Use virtual environments for project isolation",
                "Set up automated dependency checking"
            ])

        elif 'name' in pattern.pattern_id.lower() or 'logic' in pattern.common_context.get('category', ''):
            measures.extend([
                "Use type hints and static type checking (mypy)",
                "Write unit tests to catch logic errors early",
                "Follow consistent naming conventions"
            ])

        return measures

    def _estimate_prevention_impact(self, pattern: ErrorPattern) -> Dict[str, Any]:
        """Estimate the impact of preventing this pattern"""
        return {
            'time_saved_hours': pattern.frequency * 0.5,  # Assume 30 min per error
            'confidence': 0.7 if pattern.frequency >= 5 else 0.5,
            'effort_required': 'low' if pattern.frequency >= 10 else 'medium'
        }